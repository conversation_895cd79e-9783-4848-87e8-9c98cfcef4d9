"""
Unit tests for advanced reporting system.
"""

import json
import os
import tempfile
import unittest
import xml.etree.ElementTree as ET
from datetime import datetime

from tool_connectors.reporting import (
    ReportMetadata, ReportSection, ReportTemplate, ReportGenerator,
    JSONFormatter, XMLFormatter, CSVFormatter, HTMLFormatter,
    get_report_generator
)
from tool_connectors.base import ToolResult, ToolStatus
from tool_connectors import ToolManager


class TestReportMetadata(unittest.TestCase):
    """Test cases for ReportMetadata"""
    
    def test_metadata_creation(self):
        """Test creating report metadata"""
        metadata = ReportMetadata(
            title="Test Report",
            description="Test description",
            author="Test Author",
            version="2.0",
            tags=["test", "report"],
            custom_fields={"project": "test_project"}
        )
        
        self.assertEqual(metadata.title, "Test Report")
        self.assertEqual(metadata.description, "Test description")
        self.assertEqual(metadata.author, "Test Author")
        self.assertEqual(metadata.version, "2.0")
        self.assertEqual(metadata.tags, ["test", "report"])
        self.assertEqual(metadata.custom_fields["project"], "test_project")
        self.assertIsInstance(metadata.created_at, datetime)


class TestReportSection(unittest.TestCase):
    """Test cases for ReportSection"""
    
    def test_section_creation(self):
        """Test creating report sections"""
        section = ReportSection(
            title="Test Section",
            content="Test content",
            data={"key": "value"},
            charts=[{"type": "bar", "data": [1, 2, 3]}]
        )
        
        self.assertEqual(section.title, "Test Section")
        self.assertEqual(section.content, "Test content")
        self.assertEqual(section.data["key"], "value")
        self.assertEqual(len(section.charts), 1)
    
    def test_nested_sections(self):
        """Test nested sections"""
        subsection = ReportSection("Subsection", "Subsection content")
        main_section = ReportSection("Main Section", "Main content", subsections=[subsection])
        
        self.assertEqual(len(main_section.subsections), 1)
        self.assertEqual(main_section.subsections[0].title, "Subsection")


class TestReportFormatters(unittest.TestCase):
    """Test cases for report formatters"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.metadata = ReportMetadata(
            title="Test Report",
            description="Test description",
            author="Test Author"
        )
        
        self.sections = [
            ReportSection("Section 1", "Content 1", data={"metric": 42}),
            ReportSection("Section 2", "Content 2")
        ]
        
        self.results = [
            ToolResult(
                tool_name="nmap",
                command="nmap example.com",
                status=ToolStatus.COMPLETED,
                execution_time=1.5,
                timestamp=1234567890,
                findings=[
                    {"type": "port", "port": 80, "severity": "info"},
                    {"type": "port", "port": 443, "severity": "info"}
                ]
            ),
            ToolResult(
                tool_name="nikto",
                command="nikto -h example.com",
                status=ToolStatus.COMPLETED,
                execution_time=2.3,
                timestamp=1234567891,
                findings=[
                    {"type": "vulnerability", "description": "Test vuln", "severity": "medium"}
                ]
            )
        ]
    
    def test_json_formatter(self):
        """Test JSON formatter"""
        formatter = JSONFormatter()
        
        result = formatter.format_report(self.metadata, self.sections, self.results)
        
        # Should be valid JSON
        data = json.loads(result)
        
        self.assertIn('metadata', data)
        self.assertIn('sections', data)
        self.assertIn('results', data)
        self.assertIn('summary', data)
        
        self.assertEqual(data['metadata']['title'], "Test Report")
        self.assertEqual(len(data['sections']), 2)
        self.assertEqual(len(data['results']), 2)
        self.assertEqual(data['summary']['total_tools_executed'], 2)
        self.assertEqual(data['summary']['total_findings'], 3)
    
    def test_xml_formatter(self):
        """Test XML formatter"""
        formatter = XMLFormatter()
        
        result = formatter.format_report(self.metadata, self.sections, self.results)
        
        # Should be valid XML
        root = ET.fromstring(result)
        
        self.assertEqual(root.tag, 'report')
        
        # Check metadata
        metadata_elem = root.find('metadata')
        self.assertIsNotNone(metadata_elem)
        self.assertEqual(metadata_elem.find('title').text, "Test Report")
        
        # Check sections
        sections_elem = root.find('sections')
        self.assertIsNotNone(sections_elem)
        self.assertEqual(len(sections_elem.findall('section')), 2)
        
        # Check results
        results_elem = root.find('results')
        self.assertIsNotNone(results_elem)
        self.assertEqual(len(results_elem.findall('tool_result')), 2)
    
    def test_csv_formatter(self):
        """Test CSV formatter"""
        formatter = CSVFormatter()
        
        result = formatter.format_report(self.metadata, self.sections, self.results)
        
        # Should contain CSV headers and data
        lines = result.split('\n')
        
        # Check metadata comments
        self.assertTrue(any('Test Report' in line for line in lines))
        
        # Check CSV headers
        self.assertTrue(any('Tool Name' in line for line in lines))
        self.assertTrue(any('Status' in line for line in lines))
        
        # Check data rows
        self.assertTrue(any('nmap' in line for line in lines))
        self.assertTrue(any('nikto' in line for line in lines))
    
    def test_html_formatter(self):
        """Test HTML formatter"""
        formatter = HTMLFormatter()
        
        result = formatter.format_report(self.metadata, self.sections, self.results)
        
        # Should be valid HTML
        self.assertIn('<!DOCTYPE html>', result)
        self.assertIn('<html', result)
        self.assertIn('</html>', result)
        
        # Check content
        self.assertIn('Test Report', result)
        self.assertIn('nmap', result)
        self.assertIn('nikto', result)
        self.assertIn('Executive Summary', result)
    
    def test_file_extensions(self):
        """Test formatter file extensions"""
        self.assertEqual(JSONFormatter().get_file_extension(), ".json")
        self.assertEqual(XMLFormatter().get_file_extension(), ".xml")
        self.assertEqual(CSVFormatter().get_file_extension(), ".csv")
        self.assertEqual(HTMLFormatter().get_file_extension(), ".html")


class TestReportTemplate(unittest.TestCase):
    """Test cases for ReportTemplate"""
    
    def test_template_creation(self):
        """Test creating report templates"""
        template = ReportTemplate("test_template", "Test template description")
        
        self.assertEqual(template.name, "test_template")
        self.assertEqual(template.description, "Test template description")
        self.assertEqual(len(template.sections), 0)
    
    def test_template_sections(self):
        """Test adding sections to templates"""
        template = ReportTemplate("test_template")
        section = ReportSection("Test Section", "Test content")
        
        template.add_section(section)
        
        self.assertEqual(len(template.sections), 1)
        self.assertEqual(template.sections[0].title, "Test Section")
    
    def test_metadata_template(self):
        """Test metadata template functionality"""
        template = ReportTemplate("test_template")
        template.set_metadata_template(
            title="Template Title",
            tags=["template", "test"]
        )
        
        metadata = template.generate_metadata(author="Custom Author")
        
        self.assertEqual(metadata.title, "Template Title")
        self.assertEqual(metadata.author, "Custom Author")
        self.assertEqual(metadata.tags, ["template", "test"])


class TestReportGenerator(unittest.TestCase):
    """Test cases for ReportGenerator"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.generator = ReportGenerator()
        self.results = [
            ToolResult(
                tool_name="nmap",
                command="nmap example.com",
                status=ToolStatus.COMPLETED,
                execution_time=1.5,
                timestamp=1234567890,
                findings=[{"type": "port", "port": 80, "severity": "info"}]
            )
        ]
    
    def test_generator_initialization(self):
        """Test report generator initialization"""
        self.assertIn('json', self.generator.formatters)
        self.assertIn('xml', self.generator.formatters)
        self.assertIn('csv', self.generator.formatters)
        self.assertIn('html', self.generator.formatters)
        
        # Should have default templates
        self.assertIn('security_assessment', self.generator.templates)
        self.assertIn('penetration_test', self.generator.templates)
        self.assertIn('quick_scan', self.generator.templates)
    
    def test_available_formats(self):
        """Test getting available formats"""
        formats = self.generator.get_available_formats()
        
        self.assertIn('json', formats)
        self.assertIn('xml', formats)
        self.assertIn('csv', formats)
        self.assertIn('html', formats)
    
    def test_available_templates(self):
        """Test getting available templates"""
        templates = self.generator.get_available_templates()
        
        self.assertIn('security_assessment', templates)
        self.assertIn('penetration_test', templates)
        self.assertIn('quick_scan', templates)
    
    def test_generate_report_basic(self):
        """Test basic report generation"""
        report = self.generator.generate_report(self.results, 'json')
        
        # Should be valid JSON
        data = json.loads(report)
        self.assertIn('metadata', data)
        self.assertIn('results', data)
    
    def test_generate_report_with_template(self):
        """Test report generation with template"""
        report = self.generator.generate_report(
            self.results, 
            'json', 
            template_name='security_assessment'
        )
        
        data = json.loads(report)
        self.assertEqual(data['metadata']['title'], "Security Assessment Report")
        self.assertIn('security', data['metadata']['tags'])
    
    def test_generate_report_with_custom_sections(self):
        """Test report generation with custom sections"""
        custom_section = ReportSection("Custom Section", "Custom content")
        
        report = self.generator.generate_report(
            self.results,
            'json',
            custom_sections=[custom_section]
        )
        
        data = json.loads(report)
        section_titles = [s['title'] for s in data['sections']]
        self.assertIn("Custom Section", section_titles)
    
    def test_save_report(self):
        """Test saving reports to file"""
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = os.path.join(temp_dir, "test_report")
            
            report_content = self.generator.generate_report(self.results, 'html')
            saved_path = self.generator.save_report(report_content, file_path, 'html')
            
            self.assertTrue(os.path.exists(saved_path))
            self.assertTrue(saved_path.endswith('.html'))
            
            with open(saved_path, 'r', encoding='utf-8') as f:
                content = f.read()
                self.assertIn('<!DOCTYPE html>', content)
    
    def test_unsupported_format(self):
        """Test handling of unsupported formats"""
        with self.assertRaises(ValueError):
            self.generator.generate_report(self.results, 'unsupported_format')
    
    def test_analysis_sections(self):
        """Test automatic analysis section generation"""
        # Create results with different severities
        results = [
            ToolResult(
                tool_name="nmap",
                command="nmap example.com",
                status=ToolStatus.COMPLETED,
                execution_time=1.0,
                timestamp=1234567890,
                findings=[{"severity": "high", "type": "vulnerability"}]
            ),
            ToolResult(
                tool_name="nikto",
                command="nikto example.com",
                status=ToolStatus.FAILED,
                execution_time=0.5,
                timestamp=1234567891,
                findings=[]
            )
        ]
        
        report = self.generator.generate_report(results, 'json')
        data = json.loads(report)
        
        # Should have analysis sections
        section_titles = [s['title'] for s in data['sections']]
        self.assertIn("Tool Execution Analysis", section_titles)
        self.assertIn("Risk Assessment", section_titles)
        
        # Check risk assessment
        risk_section = next(s for s in data['sections'] if s['title'] == "Risk Assessment")
        self.assertEqual(risk_section['data']['overall_risk'], 'HIGH')


class TestToolManagerReportingIntegration(unittest.TestCase):
    """Test ToolManager integration with reporting"""
    
    def test_reporting_integration(self):
        """Test that ToolManager integrates with reporting"""
        manager = ToolManager(mock_mode=True)
        
        self.assertIsNotNone(manager.report_generator)
        
        # Execute some tools
        result1 = manager.execute_tool('nmap', 'example.com')
        result2 = manager.execute_tool('nikto', 'example.com')
        
        results = [result1, result2]
        
        # Generate report
        report = manager.generate_advanced_report(results, 'json')
        
        # Should be valid JSON
        data = json.loads(report)
        self.assertIn('metadata', data)
        self.assertIn('results', data)
        self.assertEqual(len(data['results']), 2)
    
    def test_report_formats_and_templates(self):
        """Test report formats and templates methods"""
        manager = ToolManager(mock_mode=True)
        
        formats = manager.get_available_report_formats()
        self.assertIn('html', formats)
        self.assertIn('json', formats)
        
        templates = manager.get_available_report_templates()
        self.assertIn('security_assessment', templates)
    
    def test_save_report(self):
        """Test saving reports through ToolManager"""
        manager = ToolManager(mock_mode=True)
        
        result = manager.execute_tool('nmap', 'example.com')
        
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = os.path.join(temp_dir, "manager_report.html")
            
            saved_path = manager.save_advanced_report([result], file_path, 'html')
            
            self.assertTrue(os.path.exists(saved_path))
            
            with open(saved_path, 'r', encoding='utf-8') as f:
                content = f.read()
                self.assertIn('Security Scan Report', content)
                self.assertIn('nmap', content)
    
    def test_custom_report_sections(self):
        """Test creating custom report sections"""
        manager = ToolManager(mock_mode=True)
        
        section = manager.create_report_section(
            "Custom Analysis",
            "This is a custom analysis section",
            {"metric1": 100, "metric2": 200}
        )
        
        self.assertEqual(section.title, "Custom Analysis")
        self.assertEqual(section.data["metric1"], 100)
        
        # Use in report
        result = manager.execute_tool('nmap', 'example.com')
        report = manager.generate_advanced_report([result], 'json', custom_sections=[section])
        
        data = json.loads(report)
        section_titles = [s['title'] for s in data['sections']]
        self.assertIn("Custom Analysis", section_titles)


class TestGlobalReportGenerator(unittest.TestCase):
    """Test global report generator function"""
    
    def test_get_report_generator(self):
        """Test global report generator function"""
        generator1 = get_report_generator()
        generator2 = get_report_generator()
        
        # Should return same instance
        self.assertIs(generator1, generator2)


if __name__ == '__main__':
    unittest.main()
