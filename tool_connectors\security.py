"""
Security and sandboxing features for tool connectors.

This module provides comprehensive security controls including input validation,
command injection prevention, resource limits, and sandboxed execution environments.
"""

import logging
import os
import re
import shlex
import subprocess
import tempfile
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union
import threading

# Optional imports
try:
    import resource
    HAS_RESOURCE = True
except ImportError:
    HAS_RESOURCE = False

try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False

from .base import ToolResult, ToolStatus


@dataclass
class SecurityPolicy:
    """Security policy configuration."""
    
    # Input validation
    max_input_length: int = 1000
    allowed_characters: str = r'[a-zA-Z0-9\.\-_:/\s]+'
    blocked_patterns: List[str] = field(default_factory=lambda: [
        r'[;&|`$(){}[\]\\]',  # Shell metacharacters
        r'\.\./|\.\.\\',       # Directory traversal
        r'rm\s+-rf',          # Dangerous commands
        r'sudo|su\s',         # Privilege escalation
        r'chmod\s+777',       # Dangerous permissions
        r'>/dev/null',        # Output redirection
        r'2>&1',              # Error redirection
    ])
    
    # Command execution
    allowed_commands: Set[str] = field(default_factory=lambda: {
        'nmap', 'nikto', 'sqlmap', 'gobuster', 'openvas-cli'
    })
    command_timeout: int = 300  # 5 minutes
    max_processes: int = 5
    
    # Resource limits
    max_memory_mb: int = 1024   # 1GB
    max_cpu_percent: int = 80
    max_disk_usage_mb: int = 100
    max_network_connections: int = 100
    
    # Sandboxing
    use_sandbox: bool = True
    sandbox_directory: Optional[str] = None
    restrict_network: bool = False
    restrict_filesystem: bool = True
    
    # Logging and monitoring
    log_all_commands: bool = True
    monitor_resources: bool = True
    alert_on_violations: bool = True


class SecurityViolation(Exception):
    """Exception raised when security policy is violated."""
    
    def __init__(self, message: str, violation_type: str, details: Dict[str, Any] = None):
        super().__init__(message)
        self.violation_type = violation_type
        self.details = details or {}


class InputValidator:
    """Input validation and sanitization."""
    
    def __init__(self, policy: SecurityPolicy):
        """
        Initialize input validator.
        
        Args:
            policy: Security policy configuration
        """
        self.policy = policy
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Compile regex patterns for efficiency
        self.allowed_pattern = re.compile(self.policy.allowed_characters)
        self.blocked_patterns = [re.compile(pattern, re.IGNORECASE) 
                               for pattern in self.policy.blocked_patterns]
    
    def validate_input(self, input_value: str, input_name: str = "input") -> str:
        """
        Validate and sanitize input.
        
        Args:
            input_value: Input value to validate
            input_name: Name of the input for error reporting
            
        Returns:
            Sanitized input value
            
        Raises:
            SecurityViolation: If input violates security policy
        """
        if not isinstance(input_value, str):
            input_value = str(input_value)
        
        # Check length
        if len(input_value) > self.policy.max_input_length:
            raise SecurityViolation(
                f"Input '{input_name}' exceeds maximum length of {self.policy.max_input_length}",
                "input_length",
                {"input_name": input_name, "length": len(input_value)}
            )
        
        # Check for blocked patterns first (higher priority)
        for pattern in self.blocked_patterns:
            if pattern.search(input_value):
                self.logger.warning(f"Blocked pattern detected in {input_name}: {pattern.pattern}")
                raise SecurityViolation(
                    f"Input '{input_name}' contains blocked pattern: {pattern.pattern}",
                    "blocked_pattern",
                    {"input_name": input_name, "pattern": pattern.pattern}
                )

        # Validate against allowed characters (only if no blocked patterns found)
        if not self.allowed_pattern.fullmatch(input_value):
            invalid_chars = set(input_value) - set(re.findall(r'[a-zA-Z0-9\.\-_:/\s]', input_value))
            if invalid_chars:  # Only raise if there are actually invalid characters
                raise SecurityViolation(
                    f"Input '{input_name}' contains invalid characters: {invalid_chars}",
                    "invalid_characters",
                    {"input_name": input_name, "invalid_chars": list(invalid_chars)}
                )
        
        # Sanitize input (basic escaping)
        sanitized = shlex.quote(input_value.strip())
        
        if sanitized != input_value.strip():
            self.logger.info(f"Sanitized input '{input_name}': '{input_value}' -> '{sanitized}'")
        
        return sanitized
    
    def validate_command(self, command: str, tool_name: str) -> List[str]:
        """
        Validate and parse command for execution.
        
        Args:
            command: Command string to validate
            tool_name: Name of the tool being executed
            
        Returns:
            List of command arguments
            
        Raises:
            SecurityViolation: If command violates security policy
        """
        # Parse command safely
        try:
            args = shlex.split(command)
        except ValueError as e:
            raise SecurityViolation(
                f"Invalid command syntax: {e}",
                "command_syntax",
                {"command": command, "tool": tool_name}
            )
        
        if not args:
            raise SecurityViolation(
                "Empty command",
                "empty_command",
                {"tool": tool_name}
            )
        
        # Check if tool is allowed
        base_command = Path(args[0]).name
        if base_command not in self.policy.allowed_commands:
            raise SecurityViolation(
                f"Command '{base_command}' is not in allowed commands list",
                "unauthorized_command",
                {"command": base_command, "tool": tool_name, "allowed": list(self.policy.allowed_commands)}
            )
        
        # Validate each argument
        for i, arg in enumerate(args[1:], 1):
            try:
                self.validate_input(arg, f"arg_{i}")
            except SecurityViolation as e:
                e.details.update({"command": command, "tool": tool_name, "arg_index": i})
                raise
        
        return args


class ResourceMonitor:
    """Resource usage monitoring and enforcement."""
    
    def __init__(self, policy: SecurityPolicy):
        """
        Initialize resource monitor.
        
        Args:
            policy: Security policy configuration
        """
        self.policy = policy
        self.logger = logging.getLogger(self.__class__.__name__)
        self.active_processes: Dict[int, psutil.Process] = {}
        self._monitoring = False
        self._monitor_thread = None
    
    def start_monitoring(self):
        """Start resource monitoring."""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_thread = threading.Thread(target=self._monitor_resources, daemon=True)
        self._monitor_thread.start()
        self.logger.info("Resource monitoring started")
    
    def stop_monitoring(self):
        """Stop resource monitoring."""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=1)
        self.logger.info("Resource monitoring stopped")
    
    def _monitor_resources(self):
        """Monitor system resources continuously."""
        if not HAS_PSUTIL:
            self.logger.warning("psutil not available - resource monitoring disabled")
            return

        while self._monitoring:
            try:
                # Check system resources
                memory_percent = psutil.virtual_memory().percent
                cpu_percent = psutil.cpu_percent(interval=1)
                
                # Check memory usage
                if memory_percent > 90:  # Critical threshold
                    self.logger.critical(f"Critical memory usage: {memory_percent:.1f}%")
                    if self.policy.alert_on_violations:
                        self._handle_resource_violation("memory", memory_percent)
                
                # Check CPU usage
                if cpu_percent > self.policy.max_cpu_percent:
                    self.logger.warning(f"High CPU usage: {cpu_percent:.1f}%")
                
                # Check active processes
                if len(self.active_processes) > self.policy.max_processes:
                    self.logger.warning(f"Too many active processes: {len(self.active_processes)}")
                
                # Clean up finished processes
                finished_pids = []
                for pid, process in self.active_processes.items():
                    try:
                        if not process.is_running():
                            finished_pids.append(pid)
                    except psutil.NoSuchProcess:
                        finished_pids.append(pid)
                
                for pid in finished_pids:
                    del self.active_processes[pid]
                
                time.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                self.logger.error(f"Error in resource monitoring: {e}")
                time.sleep(10)  # Wait longer on error
    
    def _handle_resource_violation(self, resource_type: str, current_value: float):
        """Handle resource violation."""
        violation = SecurityViolation(
            f"Resource limit exceeded: {resource_type} = {current_value}",
            "resource_limit",
            {"resource": resource_type, "value": current_value}
        )
        
        if self.policy.alert_on_violations:
            self.logger.error(f"Security violation: {violation}")
    
    def register_process(self, process: subprocess.Popen) -> int:
        """
        Register a process for monitoring.

        Args:
            process: Process to monitor

        Returns:
            Process ID
        """
        if not HAS_PSUTIL:
            return process.pid

        try:
            ps_process = psutil.Process(process.pid)
            self.active_processes[process.pid] = ps_process
            self.logger.debug(f"Registered process {process.pid} for monitoring")
            return process.pid
        except psutil.NoSuchProcess:
            self.logger.warning(f"Could not register process {process.pid} - already finished")
            return process.pid
    
    def unregister_process(self, pid: int):
        """
        Unregister a process from monitoring.
        
        Args:
            pid: Process ID to unregister
        """
        if pid in self.active_processes:
            del self.active_processes[pid]
            self.logger.debug(f"Unregistered process {pid}")
    
    def check_process_limits(self, pid: int) -> Dict[str, Any]:
        """
        Check if process is within resource limits.

        Args:
            pid: Process ID to check

        Returns:
            Dictionary with resource usage information
        """
        if not HAS_PSUTIL:
            return {"error": "psutil not available"}

        if pid not in self.active_processes:
            return {"error": "Process not registered"}

        try:
            process = self.active_processes[pid]

            # Get resource usage
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            cpu_percent = process.cpu_percent()

            # Check limits
            violations = []
            if memory_mb > self.policy.max_memory_mb:
                violations.append(f"Memory: {memory_mb:.1f}MB > {self.policy.max_memory_mb}MB")

            if cpu_percent > self.policy.max_cpu_percent:
                violations.append(f"CPU: {cpu_percent:.1f}% > {self.policy.max_cpu_percent}%")

            return {
                "pid": pid,
                "memory_mb": memory_mb,
                "cpu_percent": cpu_percent,
                "violations": violations,
                "within_limits": len(violations) == 0
            }

        except psutil.NoSuchProcess:
            self.unregister_process(pid)
            return {"error": "Process no longer exists"}
        except Exception as e:
            return {"error": str(e)}


class SandboxEnvironment:
    """Sandboxed execution environment."""
    
    def __init__(self, policy: SecurityPolicy):
        """
        Initialize sandbox environment.
        
        Args:
            policy: Security policy configuration
        """
        self.policy = policy
        self.logger = logging.getLogger(self.__class__.__name__)
        self.sandbox_dir = None
        self._setup_sandbox()
    
    def _setup_sandbox(self):
        """Set up sandbox directory."""
        if self.policy.sandbox_directory:
            self.sandbox_dir = Path(self.policy.sandbox_directory)
        else:
            self.sandbox_dir = Path(tempfile.mkdtemp(prefix="toolconnector_sandbox_"))
        
        self.sandbox_dir.mkdir(parents=True, exist_ok=True)
        
        # Set restrictive permissions
        if self.policy.restrict_filesystem:
            os.chmod(self.sandbox_dir, 0o700)
        
        self.logger.info(f"Sandbox directory: {self.sandbox_dir}")
    
    def get_sandbox_path(self, relative_path: str = "") -> Path:
        """
        Get path within sandbox.
        
        Args:
            relative_path: Relative path within sandbox
            
        Returns:
            Absolute path within sandbox
        """
        if relative_path:
            return self.sandbox_dir / relative_path
        return self.sandbox_dir
    
    def create_secure_environment(self) -> Dict[str, str]:
        """
        Create secure environment variables.
        
        Returns:
            Dictionary of environment variables
        """
        # Start with minimal environment
        secure_env = {
            'PATH': '/usr/bin:/bin',
            'HOME': str(self.sandbox_dir),
            'TMPDIR': str(self.sandbox_dir / 'tmp'),
            'USER': 'toolconnector',
            'SHELL': '/bin/sh'
        }
        
        # Create tmp directory
        tmp_dir = self.sandbox_dir / 'tmp'
        tmp_dir.mkdir(exist_ok=True)
        
        # Add tool-specific paths if they exist
        tool_paths = [
            '/usr/bin/nmap',
            '/usr/bin/nikto',
            '/usr/bin/sqlmap',
            '/usr/bin/gobuster'
        ]
        
        existing_paths = []
        for tool_path in tool_paths:
            if Path(tool_path).exists():
                existing_paths.append(str(Path(tool_path).parent))
        
        if existing_paths:
            secure_env['PATH'] = ':'.join(set(existing_paths + ['/usr/bin', '/bin']))
        
        return secure_env
    
    def cleanup(self):
        """Clean up sandbox directory."""
        if self.sandbox_dir and self.sandbox_dir.exists():
            try:
                import shutil
                shutil.rmtree(self.sandbox_dir)
                self.logger.info(f"Cleaned up sandbox: {self.sandbox_dir}")
            except Exception as e:
                self.logger.error(f"Failed to cleanup sandbox {self.sandbox_dir}: {e}")


class SecureExecutor:
    """Secure command executor with sandboxing and resource limits."""
    
    def __init__(self, policy: SecurityPolicy):
        """
        Initialize secure executor.
        
        Args:
            policy: Security policy configuration
        """
        self.policy = policy
        self.logger = logging.getLogger(self.__class__.__name__)
        self.validator = InputValidator(policy)
        self.resource_monitor = ResourceMonitor(policy)
        self.sandbox = SandboxEnvironment(policy) if policy.use_sandbox else None
        
        # Start resource monitoring
        if policy.monitor_resources:
            self.resource_monitor.start_monitoring()
    
    def execute_command(self, command: str, tool_name: str, 
                       working_dir: Optional[str] = None,
                       input_data: Optional[str] = None) -> ToolResult:
        """
        Execute command securely with all safety measures.
        
        Args:
            command: Command to execute
            tool_name: Name of the tool
            working_dir: Working directory (optional)
            input_data: Input data to pass to command (optional)
            
        Returns:
            ToolResult with execution results
        """
        start_time = time.time()
        
        try:
            # Validate command
            args = self.validator.validate_command(command, tool_name)
            
            # Set up execution environment
            env = self.sandbox.create_secure_environment() if self.sandbox else None
            cwd = self.sandbox.get_sandbox_path() if self.sandbox else working_dir
            
            # Log command execution
            if self.policy.log_all_commands:
                self.logger.info(f"Executing: {tool_name} - {' '.join(args)}")
            
            # Execute with timeout and resource limits
            process = subprocess.Popen(
                args,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE if input_data else None,
                cwd=cwd,
                env=env,
                text=True
            )
            
            # Register for monitoring
            pid = self.resource_monitor.register_process(process)
            
            try:
                # Execute with timeout
                stdout, stderr = process.communicate(
                    input=input_data,
                    timeout=self.policy.command_timeout
                )
                
                execution_time = time.time() - start_time
                
                # Check final resource usage
                resource_info = self.resource_monitor.check_process_limits(pid)
                
                # Create result
                result = ToolResult(
                    tool_name=tool_name,
                    command=command,
                    status=ToolStatus.COMPLETED if process.returncode == 0 else ToolStatus.FAILED,
                    exit_code=process.returncode,
                    stdout=stdout,
                    stderr=stderr,
                    execution_time=execution_time,
                    timestamp=start_time
                )
                
                # Add security metadata
                if hasattr(result, 'parsed_data'):
                    result.parsed_data['security'] = {
                        'sandboxed': self.policy.use_sandbox,
                        'resource_usage': resource_info,
                        'validation_passed': True
                    }
                
                return result
                
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
                raise SecurityViolation(
                    f"Command timeout after {self.policy.command_timeout} seconds",
                    "timeout",
                    {"tool": tool_name, "timeout": self.policy.command_timeout}
                )
            
            finally:
                self.resource_monitor.unregister_process(pid)
        
        except SecurityViolation:
            raise
        except Exception as e:
            execution_time = time.time() - start_time
            return ToolResult(
                tool_name=tool_name,
                command=command,
                status=ToolStatus.FAILED,
                execution_time=execution_time,
                timestamp=start_time,
                error_message=f"Execution error: {str(e)}"
            )
    
    def cleanup(self):
        """Clean up resources."""
        self.resource_monitor.stop_monitoring()
        if self.sandbox:
            self.sandbox.cleanup()


# Global security manager instance
_security_manager: Optional[SecureExecutor] = None


def get_security_manager(policy: Optional[SecurityPolicy] = None) -> SecureExecutor:
    """
    Get the global security manager instance.
    
    Args:
        policy: Security policy (only used on first call)
        
    Returns:
        SecureExecutor instance
    """
    global _security_manager
    
    if _security_manager is None:
        if policy is None:
            policy = SecurityPolicy()
        _security_manager = SecureExecutor(policy)
    
    return _security_manager
