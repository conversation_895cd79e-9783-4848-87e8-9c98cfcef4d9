"""
Base Tool Connector Interface

This module defines the abstract base class and common data structures
for all security tool connectors in the Ethical Hacking AI system.
"""

import asyncio
import logging
import subprocess
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional, Union


class ToolStatus(Enum):
    """Status enumeration for tool execution"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


class Severity(Enum):
    """Severity levels for findings"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


@dataclass
class ToolResult:
    """Standardized result structure for all tool outputs"""
    tool_name: str
    command: str
    status: ToolStatus
    exit_code: Optional[int] = None
    stdout: str = ""
    stderr: str = ""
    execution_time: float = 0.0
    timestamp: float = field(default_factory=time.time)
    findings: List[Dict[str, Any]] = field(default_factory=list)
    raw_output: str = ""
    parsed_data: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None


@dataclass
class ToolError(Exception):
    """Custom exception for tool execution errors"""
    tool_name: str
    message: str
    exit_code: Optional[int] = None
    stderr: Optional[str] = None
    
    def __str__(self):
        return f"{self.tool_name}: {self.message}"


class BaseToolConnector(ABC):
    """
    Abstract base class for all security tool connectors.

    This class defines the standard interface that all tool connectors
    must implement to ensure consistent behavior across the system.
    """

    def __init__(self, tool_path: Optional[str] = None, timeout: int = 300,
                 mock_mode: bool = False, mock_data: Optional[Dict[str, Any]] = None,
                 secure_mode: bool = True):
        """
        Initialize the tool connector.

        Args:
            tool_path: Path to the tool executable (if not in PATH)
            timeout: Default timeout for tool execution in seconds
            mock_mode: Enable mock mode for testing/demo purposes
            mock_data: Mock data to return when in mock mode
            secure_mode: Enable secure execution mode with sandboxing
        """
        self.tool_path = tool_path
        self.timeout = timeout
        self.mock_mode = mock_mode
        self.mock_data = mock_data or {}
        self.secure_mode = secure_mode
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        self.tool_available = self._validate_tool_availability()
        self._security_manager = None
    
    @property
    @abstractmethod
    def tool_name(self) -> str:
        """Return the name of the tool"""
        pass
    
    @property
    @abstractmethod
    def default_command(self) -> str:
        """Return the default command for the tool"""
        pass
    
    @abstractmethod
    def build_command(self, target: str, **kwargs) -> List[str]:
        """
        Build the command line arguments for the tool.
        
        Args:
            target: The target to scan/test
            **kwargs: Additional tool-specific parameters
            
        Returns:
            List of command line arguments
        """
        pass
    
    @abstractmethod
    def parse_output(self, stdout: str, stderr: str) -> Dict[str, Any]:
        """
        Parse the tool's output into a standardized format.
        
        Args:
            stdout: Standard output from the tool
            stderr: Standard error from the tool
            
        Returns:
            Parsed data in a standardized format
        """
        pass
    
    def _validate_tool_availability(self) -> bool:
        """
        Validate that the tool is available and executable.

        Returns:
            True if tool is available, False otherwise
        """
        if self.mock_mode:
            self.logger.info(f"{self.tool_name} running in mock mode")
            return True

        try:
            cmd = [self.tool_path or self.default_command, "--version"]
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode != 0:
                self.logger.warning(f"{self.tool_name} may not be properly installed")
                return False
            else:
                self.logger.debug(f"{self.tool_name} is available")
                return True
        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
            self.logger.error(f"Failed to validate {self.tool_name}: {e}")
            return False
    
    def execute(self, target: str, **kwargs) -> ToolResult:
        """
        Execute the tool synchronously.
        
        Args:
            target: The target to scan/test
            **kwargs: Additional tool-specific parameters
            
        Returns:
            ToolResult containing execution results
        """
        return asyncio.run(self.execute_async(target, **kwargs))
    
    async def execute_async(self, target: str, **kwargs) -> ToolResult:
        """
        Execute the tool asynchronously.

        Args:
            target: The target to scan/test
            **kwargs: Additional tool-specific parameters

        Returns:
            ToolResult containing execution results
        """
        start_time = time.time()
        command = self.build_command(target, **kwargs)
        command_str = " ".join(command)

        self.logger.info(f"Executing {self.tool_name}: {command_str}")

        result = ToolResult(
            tool_name=self.tool_name,
            command=command_str,
            status=ToolStatus.RUNNING
        )

        # Handle mock mode
        if self.mock_mode:
            return await self._execute_mock(target, result, **kwargs)

        # Check if tool is available
        if not self.tool_available:
            result.status = ToolStatus.FAILED
            result.error_message = f"{self.tool_name} is not available on this system"
            result.execution_time = time.time() - start_time
            return result

        try:
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=kwargs.get('timeout', self.timeout)
            )
            
            result.exit_code = process.returncode
            result.stdout = stdout.decode('utf-8', errors='ignore')
            result.stderr = stderr.decode('utf-8', errors='ignore')
            result.raw_output = result.stdout
            result.execution_time = time.time() - start_time
            
            if process.returncode == 0:
                result.status = ToolStatus.COMPLETED
                result.parsed_data = self.parse_output(result.stdout, result.stderr)
                result.findings = self._extract_findings(result.parsed_data)
            else:
                result.status = ToolStatus.FAILED
                result.error_message = f"Tool exited with code {process.returncode}"
                
        except asyncio.TimeoutError:
            result.status = ToolStatus.TIMEOUT
            result.error_message = f"Tool execution timed out after {self.timeout} seconds"
            self.logger.error(f"{self.tool_name} execution timed out")
            
        except Exception as e:
            result.status = ToolStatus.FAILED
            result.error_message = str(e)
            self.logger.error(f"Error executing {self.tool_name}: {e}")
        
        return result

    async def _execute_mock(self, target: str, result: ToolResult, **kwargs) -> ToolResult:
        """
        Execute in mock mode for testing/demo purposes.

        Args:
            target: The target to scan/test
            result: ToolResult object to populate
            **kwargs: Additional parameters

        Returns:
            Mocked ToolResult
        """
        start_time = time.time()

        # Simulate execution time
        mock_delay = kwargs.get('mock_delay', 1.0)
        await asyncio.sleep(mock_delay)

        # Generate mock output
        mock_output = self._generate_mock_output(target, **kwargs)

        result.status = ToolStatus.COMPLETED
        result.exit_code = 0
        result.stdout = mock_output['stdout']
        result.stderr = mock_output['stderr']
        result.execution_time = time.time() - start_time
        result.raw_output = mock_output['stdout']

        # Parse mock output
        try:
            result.parsed_data = self.parse_output(result.stdout, result.stderr)
            result.findings = self._extract_findings(result.parsed_data)
        except Exception as e:
            self.logger.error(f"Error parsing mock output: {e}")
            result.findings = mock_output.get('findings', [])

        return result

    def _generate_mock_output(self, target: str, **kwargs) -> Dict[str, Any]:
        """
        Generate mock output for testing/demo purposes.
        Should be overridden by specific connectors for realistic mock data.

        Args:
            target: The target being scanned
            **kwargs: Additional parameters

        Returns:
            Dictionary with mock stdout, stderr, and findings
        """
        return {
            'stdout': f"Mock output for {self.tool_name} scanning {target}",
            'stderr': "",
            'findings': [
                {
                    'type': 'mock_finding',
                    'severity': 'info',
                    'host': target,
                    'description': f"Mock finding from {self.tool_name}",
                    'recommendation': 'This is a mock finding for demonstration purposes'
                }
            ]
        }

    def _extract_findings(self, parsed_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract security findings from parsed data.
        
        Args:
            parsed_data: Parsed tool output
            
        Returns:
            List of standardized findings
        """
        # Default implementation - should be overridden by specific connectors
        return []
    
    def validate_target(self, target: str) -> bool:
        """
        Validate that the target is appropriate for this tool.
        
        Args:
            target: The target to validate
            
        Returns:
            True if target is valid, False otherwise
        """
        # Basic validation - can be overridden by specific connectors
        return bool(target and target.strip())

    def is_available(self) -> bool:
        """
        Check if the tool is available for execution.

        Returns:
            True if tool is available (either installed or in mock mode)
        """
        return self.tool_available or self.mock_mode

    def get_tool_info(self) -> Dict[str, Any]:
        """
        Get information about the tool connector.

        Returns:
            Dictionary with tool information
        """
        return {
            'name': self.tool_name,
            'available': self.is_available(),
            'mock_mode': self.mock_mode,
            'tool_path': self.tool_path,
            'default_command': self.default_command,
            'timeout': self.timeout
        }

    def enable_mock_mode(self, mock_data: Optional[Dict[str, Any]] = None):
        """
        Enable mock mode for testing/demo purposes.

        Args:
            mock_data: Optional mock data to use
        """
        self.mock_mode = True
        if mock_data:
            self.mock_data.update(mock_data)
        self.logger.info(f"Mock mode enabled for {self.tool_name}")

    def disable_mock_mode(self):
        """Disable mock mode and return to normal operation."""
        self.mock_mode = False
        self.logger.info(f"Mock mode disabled for {self.tool_name}")

    def _get_security_manager(self):
        """Get or create security manager instance."""
        if self._security_manager is None and self.secure_mode:
            from .security import get_security_manager, SecurityPolicy

            # Create security policy for this tool
            policy = SecurityPolicy()
            policy.allowed_commands.add(self.tool_name.lower())
            policy.command_timeout = self.timeout

            self._security_manager = get_security_manager(policy)

        return self._security_manager

    def _execute_secure(self, command: str, working_dir: Optional[str] = None,
                       input_data: Optional[str] = None) -> ToolResult:
        """
        Execute command securely with sandboxing and validation.

        Args:
            command: Command to execute
            working_dir: Working directory (optional)
            input_data: Input data to pass to command (optional)

        Returns:
            ToolResult with execution results
        """
        if not self.secure_mode:
            # Fall back to regular execution
            return self._execute_command(command, working_dir, input_data)

        security_manager = self._get_security_manager()
        if security_manager:
            return security_manager.execute_command(
                command, self.tool_name, working_dir, input_data
            )
        else:
            # Fall back to regular execution if security manager unavailable
            return self._execute_command(command, working_dir, input_data)
