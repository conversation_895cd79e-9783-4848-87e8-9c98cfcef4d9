"""
Tool Manager

This module provides a unified interface for managing and executing
multiple security tools through their respective connectors.
"""

import asyncio
import json
import logging
import time
from typing import Any, Dict, List, Optional, Type, Union, Callable

from .base import BaseToolConnector, ToolResult, ToolStatus, Severity
from .nmap_connector import NmapConnector
from .openvas_connector import OpenVASConnector
from .sqlmap_connector import SQLmapConnector
from .nikto_connector import NiktoConnector
from .gobuster_connector import GobusterConnector
from .config import get_config_manager, ConfigManager
from .plugin_system import get_plugin_manager, PluginManager
from .workflows import WorkflowEngine, WorkflowDefinition, WorkflowStep, ExecutionMode
from .cache import get_cache_manager, CacheManager, SQLiteCache, FileCache, MemoryCache
from .monitoring import get_monitoring_manager, MonitoringManager
from .reporting import get_report_generator, ReportGenerator, ReportSection, ReportTemplate
from .security import SecurityPolicy, get_security_manager, SecurityViolation
from .monitoring import get_monitoring_manager, MonitoringManager


class ToolManager:
    """
    Unified manager for all security tool connectors.

    This class provides a single interface for executing multiple security tools,
    managing their execution, and aggregating results.
    """

    def __init__(self, mock_mode: bool = False, config_file: Optional[str] = None,
                 cache_enabled: bool = True, cache_backend: str = "memory",
                 monitoring_enabled: bool = True, log_level: str = "INFO",
                 secure_mode: bool = True, security_policy: Optional[SecurityPolicy] = None):
        """
        Initialize the tool manager with available connectors.

        Args:
            mock_mode: Enable mock mode for all connectors
            config_file: Optional path to configuration file
            cache_enabled: Whether to enable result caching
            cache_backend: Cache backend type ('memory', 'file', 'sqlite')
            monitoring_enabled: Whether to enable monitoring
            log_level: Logging level for monitoring
            secure_mode: Enable secure execution mode
            security_policy: Custom security policy (optional)
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self._connectors: Dict[str, BaseToolConnector] = {}
        self.config = get_config_manager(config_file)
        self.plugin_manager = get_plugin_manager()

        # Override mock mode from config if not explicitly set
        if mock_mode is False and self.config.is_mock_mode():
            mock_mode = True
        self.mock_mode = mock_mode
        self.secure_mode = secure_mode

        # Initialize security manager
        self._init_security_manager(secure_mode, security_policy)

        # Initialize monitoring manager
        self._init_monitoring_manager(monitoring_enabled, log_level)

        # Initialize cache manager
        self._init_cache_manager(cache_enabled, cache_backend)

        self._register_default_connectors()
        self._discover_and_register_plugins()

        # Initialize workflow engine
        self.workflow_engine = WorkflowEngine(self)

        # Initialize report generator
        self.report_generator = get_report_generator()

    def _init_security_manager(self, secure_mode: bool, security_policy: Optional[SecurityPolicy]):
        """Initialize the security manager."""
        if secure_mode:
            if security_policy is None:
                # Create default security policy
                security_policy = SecurityPolicy()

                # Configure based on config file
                security_policy.command_timeout = self.config.get('security.command_timeout', 300)
                security_policy.max_memory_mb = self.config.get('security.max_memory_mb', 1024)
                security_policy.max_cpu_percent = self.config.get('security.max_cpu_percent', 80)
                security_policy.use_sandbox = self.config.get('security.use_sandbox', True)
                security_policy.log_all_commands = self.config.get('security.log_all_commands', True)

            self.security_manager = get_security_manager(security_policy)
            self.security_policy = security_policy
            self.logger.info("Security manager initialized with sandboxing enabled")
        else:
            self.security_manager = None
            self.security_policy = None
            self.logger.info("Security manager disabled")

    def _init_monitoring_manager(self, monitoring_enabled: bool, log_level: str):
        """Initialize the monitoring manager."""
        if monitoring_enabled:
            log_file = self.config.get('general.log_file', None)
            self.monitoring = get_monitoring_manager(log_level, log_file)
            self.logger.info("Monitoring system initialized")
        else:
            self.monitoring = None
            self.logger.info("Monitoring system disabled")

    def _init_cache_manager(self, cache_enabled: bool, cache_backend: str):
        """Initialize the cache manager with specified backend."""
        if not cache_enabled:
            self.cache_manager = get_cache_manager(enabled=False)
            return

        # Create appropriate backend
        backend = None
        if cache_backend == "file":
            cache_dir = self.config.get('general.cache_directory', './cache')
            backend = FileCache(cache_dir)
        elif cache_backend == "sqlite":
            db_path = self.config.get('general.cache_database', './cache.db')
            backend = SQLiteCache(db_path)
        elif cache_backend == "memory":
            backend = MemoryCache()
        else:
            self.logger.warning(f"Unknown cache backend '{cache_backend}', using memory cache")
            backend = MemoryCache()

        # Get cache TTL from config
        default_ttl = self.config.get('general.cache_ttl', 3600)

        # Create a new cache manager instance instead of using global one
        from .cache import CacheManager
        self.cache_manager = CacheManager(backend, default_ttl, cache_enabled)
        self.logger.info(f"Initialized {cache_backend} cache backend (TTL: {default_ttl}s)")

    def _register_default_connectors(self):
        """Register all default tool connectors using configuration"""
        connectors_config = {
            'nmap': NmapConnector,
            'sqlmap': SQLmapConnector,
            'nikto': NiktoConnector,
            'gobuster': GobusterConnector,
            'openvas': OpenVASConnector
        }

        for tool_name, connector_class in connectors_config.items():
            try:
                tool_config = self.config.get_tool_config(tool_name)
                tool_path = self.config.get_tool_path(tool_name)
                timeout = self.config.get_tool_timeout(tool_name)

                # Create connector with configuration
                if tool_name == 'openvas':
                    # OpenVAS has special initialization parameters
                    host = tool_config.get('default_params', {}).get('host', 'localhost')
                    port = tool_config.get('default_params', {}).get('port', 9390)
                    self._connectors[tool_name] = connector_class(
                        tool_path=tool_path if tool_path != tool_name else None,
                        timeout=timeout,
                        host=host,
                        port=port,
                        mock_mode=self.mock_mode,
                        secure_mode=self.secure_mode
                    )
                else:
                    self._connectors[tool_name] = connector_class(
                        tool_path=tool_path if tool_path != tool_name else None,
                        timeout=timeout,
                        mock_mode=self.mock_mode,
                        secure_mode=self.secure_mode
                    )

                self.logger.debug(f"Initialized {tool_name} connector with config")

            except Exception as e:
                self.logger.warning(f"Failed to initialize {tool_name} connector: {e}")

    def _discover_and_register_plugins(self):
        """Discover and register plugin connectors."""
        try:
            plugins = self.plugin_manager.discover_plugins(auto_load=False)

            for plugin_info in plugins:
                try:
                    # Create connector instance with configuration
                    tool_config = self.config.get_tool_config(plugin_info.name)
                    tool_path = self.config.get_tool_path(plugin_info.name)
                    timeout = self.config.get_tool_timeout(plugin_info.name)

                    connector = plugin_info.connector_class(
                        tool_path=tool_path if tool_path != plugin_info.name else None,
                        timeout=timeout,
                        mock_mode=self.mock_mode
                    )

                    self._connectors[plugin_info.name] = connector
                    self.logger.info(f"Registered plugin connector: {plugin_info.name}")

                except Exception as e:
                    self.logger.warning(f"Failed to initialize plugin {plugin_info.name}: {e}")

        except Exception as e:
            self.logger.warning(f"Error discovering plugins: {e}")

    def register_connector(self, name: str, connector: BaseToolConnector):
        """
        Register a custom tool connector.
        
        Args:
            name: Name to register the connector under
            connector: Tool connector instance
        """
        self._connectors[name] = connector
        self.logger.info(f"Registered connector: {name}")
    
    def get_available_tools(self) -> List[str]:
        """Get list of available tool names"""
        return list(self._connectors.keys())
    
    def get_connector(self, tool_name: str) -> Optional[BaseToolConnector]:
        """
        Get a specific tool connector.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Tool connector instance or None if not found
        """
        return self._connectors.get(tool_name)
    
    def execute_tool(self, tool_name: str, target: str, **kwargs) -> ToolResult:
        """
        Execute a specific tool synchronously with caching and monitoring support.

        Args:
            tool_name: Name of the tool to execute
            target: Target to scan/test
            **kwargs: Tool-specific parameters

        Returns:
            ToolResult containing execution results

        Raises:
            ValueError: If tool is not available
        """
        # Start monitoring
        monitoring_context = None
        if self.monitoring:
            monitoring_context = self.monitoring.start_tool_execution(tool_name, target)

        # Check cache first
        cached_result = self.cache_manager.get_cached_result(tool_name, target, **kwargs)
        if cached_result:
            # End monitoring for cache hit
            if self.monitoring and monitoring_context:
                self.monitoring.end_tool_execution(monitoring_context, cached_result, cache_hit=True)
            return cached_result

        connector = self.get_connector(tool_name)
        if not connector:
            raise ValueError(f"Tool '{tool_name}' is not available")

        try:
            result = connector.execute(target, **kwargs)

            # Cache the result
            self.cache_manager.cache_result(tool_name, target, result, **kwargs)

            # End monitoring for successful execution
            if self.monitoring and monitoring_context:
                self.monitoring.end_tool_execution(monitoring_context, result, cache_hit=False)

            return result
        except Exception as e:
            self.logger.error(f"Error executing {tool_name}: {e}")
            # Create error result
            error_result = ToolResult(
                tool_name=tool_name,
                command=f"Failed to execute {tool_name}",
                status=ToolStatus.FAILED,
                error_message=str(e)
            )

            # Cache error result if policy allows
            self.cache_manager.cache_result(tool_name, target, error_result, **kwargs)

            # End monitoring for failed execution
            if self.monitoring and monitoring_context:
                self.monitoring.end_tool_execution(monitoring_context, error_result, cache_hit=False)

            return error_result
    
    async def execute_tool_async(self, tool_name: str, target: str, **kwargs) -> ToolResult:
        """
        Execute a specific tool asynchronously.
        
        Args:
            tool_name: Name of the tool to execute
            target: Target to scan/test
            **kwargs: Tool-specific parameters
            
        Returns:
            ToolResult containing execution results
            
        Raises:
            ValueError: If tool is not available
        """
        connector = self.get_connector(tool_name)
        if not connector:
            raise ValueError(f"Tool '{tool_name}' is not available")
        
        return await connector.execute_async(target, **kwargs)
    
    def execute_multiple_tools(self, tools: List[str], target: str, 
                             tool_configs: Optional[Dict[str, Dict]] = None) -> Dict[str, ToolResult]:
        """
        Execute multiple tools synchronously.
        
        Args:
            tools: List of tool names to execute
            target: Target to scan/test
            tool_configs: Optional configurations for each tool
            
        Returns:
            Dictionary mapping tool names to their results
        """
        return asyncio.run(self.execute_multiple_tools_async(tools, target, tool_configs))
    
    async def execute_multiple_tools_async(self, tools: List[str], target: str,
                                         tool_configs: Optional[Dict[str, Dict]] = None) -> Dict[str, ToolResult]:
        """
        Execute multiple tools asynchronously.
        
        Args:
            tools: List of tool names to execute
            target: Target to scan/test
            tool_configs: Optional configurations for each tool
            
        Returns:
            Dictionary mapping tool names to their results
        """
        if tool_configs is None:
            tool_configs = {}
        
        tasks = []
        for tool_name in tools:
            config = tool_configs.get(tool_name, {})
            task = self.execute_tool_async(tool_name, target, **config)
            tasks.append((tool_name, task))
        
        results = {}
        for tool_name, task in tasks:
            try:
                result = await task
                results[tool_name] = result
            except Exception as e:
                self.logger.error(f"Error executing {tool_name}: {e}")
                # Create error result
                results[tool_name] = ToolResult(
                    tool_name=tool_name,
                    command=f"Failed to execute {tool_name}",
                    status=ToolStatus.FAILED,
                    error_message=str(e)
                )
        
        return results

    async def execute_tool_async(self, tool_name: str, target: str, **kwargs) -> ToolResult:
        """
        Execute a tool asynchronously.

        Args:
            tool_name: Name of the tool to execute
            target: Target to scan
            **kwargs: Additional parameters for the tool

        Returns:
            Tool execution result
        """
        import asyncio

        # Create a wrapper function that calls execute_tool with the arguments
        def execute_wrapper():
            return self.execute_tool(tool_name, target, **kwargs)

        # Run the synchronous execute_tool in a thread pool
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, execute_wrapper)
    
    def execute_scan_workflow(self, target: str, workflow_type: str = 'comprehensive') -> Dict[str, ToolResult]:
        """
        Execute a predefined scanning workflow.
        
        Args:
            target: Target to scan
            workflow_type: Type of workflow ('comprehensive', 'web', 'network')
            
        Returns:
            Dictionary mapping tool names to their results
        """
        # Get workflow tools from configuration first
        tools = self.config.get_workflow_tools(workflow_type)

        if not tools:
            # Fallback to hardcoded workflows
            workflows = {
                'comprehensive': ['nmap', 'nikto', 'gobuster', 'sqlmap'],
                'web': ['nikto', 'gobuster', 'sqlmap'],
                'network': ['nmap', 'openvas'],
                'quick': ['nmap', 'nikto']
            }

            if workflow_type not in workflows:
                raise ValueError(f"Unknown workflow type: {workflow_type}")

            tools = workflows[workflow_type]
        
        # Configure tools based on workflow
        tool_configs = self._get_workflow_configs(workflow_type, target)
        
        return self.execute_multiple_tools(tools, target, tool_configs)
    
    def _get_workflow_configs(self, workflow_type: str, target: str) -> Dict[str, Dict]:
        """Get tool configurations for specific workflows using configuration system"""
        configs = {}

        # Get workflow tools from configuration
        workflow_tools = self.config.get_workflow_tools(workflow_type)
        if not workflow_tools:
            # Fallback to hardcoded workflows if not in config
            workflow_tools = self._get_default_workflow_tools(workflow_type)

        # Build configurations for each tool in the workflow
        for tool_name in workflow_tools:
            tool_defaults = self.config.get_tool_defaults(tool_name)
            tool_config = tool_defaults.copy()

            # Apply workflow-specific overrides
            if workflow_type == 'comprehensive':
                if tool_name == 'nmap':
                    tool_config.update({
                        'scan_type': 'tcp_syn',
                        'service_detection': True,
                        'scripts': ['vuln', 'safe'],
                        'timing': 4
                    })
                elif tool_name == 'nikto':
                    tool_config.update({
                        'ssl': target.startswith('https'),
                        'timeout': 15
                    })
                elif tool_name == 'gobuster':
                    tool_config.update({
                        'mode': 'dir',
                        'extensions': ['php', 'html', 'txt', 'js'],
                        'threads': 20
                    })
                elif tool_name == 'sqlmap':
                    tool_config.update({
                        'level': 2,
                        'risk': 2,
                        'batch': True
                    })

            elif workflow_type == 'web':
                if tool_name == 'nikto':
                    tool_config.update({
                        'ssl': target.startswith('https'),
                        'timeout': 20
                    })
                elif tool_name == 'gobuster':
                    tool_config.update({
                        'mode': 'dir',
                        'extensions': ['php', 'html', 'txt', 'js', 'asp', 'aspx'],
                        'threads': 30
                    })
                elif tool_name == 'sqlmap':
                    tool_config.update({
                        'level': 3,
                        'risk': 2,
                        'batch': True,
                        'forms': True
                    })

            elif workflow_type == 'network':
                if tool_name == 'nmap':
                    tool_config.update({
                        'scan_type': 'tcp_syn',
                        'service_detection': True,
                        'os_detection': True,
                        'scripts': ['default', 'vuln'],
                        'timing': 3
                    })

            elif workflow_type == 'quick':
                if tool_name == 'nmap':
                    tool_config.update({
                        'scan_type': 'tcp_syn',
                        'ports': '1-1000',
                        'timing': 5
                    })
                elif tool_name == 'nikto':
                    tool_config.update({
                        'timeout': 10
                    })

            configs[tool_name] = tool_config

        return configs

    def _get_default_workflow_tools(self, workflow_type: str) -> List[str]:
        """Get default workflow tools if not configured"""
        default_workflows = {
            'quick': ['nmap', 'nikto'],
            'comprehensive': ['nmap', 'nikto', 'gobuster', 'sqlmap'],
            'web': ['nikto', 'gobuster', 'sqlmap'],
            'network': ['nmap']
        }

        if workflow_type not in default_workflows:
            raise ValueError(f"Unknown workflow type: {workflow_type}")

        return default_workflows[workflow_type]

    def register_plugin_connector(self, name: str, connector_class: Type[BaseToolConnector],
                                 metadata: Optional[Dict[str, Any]] = None):
        """
        Register a plugin connector class.

        Args:
            name: Plugin name
            connector_class: Connector class
            metadata: Optional plugin metadata
        """
        # Register with plugin manager
        self.plugin_manager.register_plugin_class(name, connector_class, metadata)

        # Create and register connector instance
        try:
            tool_config = self.config.get_tool_config(name)
            tool_path = self.config.get_tool_path(name)
            timeout = self.config.get_tool_timeout(name)

            connector = connector_class(
                tool_path=tool_path if tool_path != name else None,
                timeout=timeout,
                mock_mode=self.mock_mode
            )

            self._connectors[name] = connector
            self.logger.info(f"Registered plugin connector: {name}")

        except Exception as e:
            self.logger.error(f"Failed to create connector for plugin {name}: {e}")

    def unregister_connector(self, name: str) -> bool:
        """
        Unregister a connector.

        Args:
            name: Connector name

        Returns:
            True if connector was unregistered, False if not found
        """
        if name in self._connectors:
            del self._connectors[name]
            self.plugin_manager.unregister_plugin(name)
            self.logger.info(f"Unregistered connector: {name}")
            return True
        return False

    def get_plugin_info(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Get plugin information.

        Args:
            name: Plugin name

        Returns:
            Plugin information or None if not found
        """
        plugin_info = self.plugin_manager.get_plugin(name)
        if not plugin_info:
            return None

        return {
            'name': plugin_info.name,
            'class': plugin_info.connector_class.__name__,
            'module_path': plugin_info.module_path,
            'metadata': plugin_info.metadata
        }

    def list_plugins(self) -> List[str]:
        """Get list of registered plugin names."""
        return self.plugin_manager.list_plugins()

    def get_all_plugin_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all registered plugins."""
        plugin_info = {}
        for name in self.list_plugins():
            info = self.get_plugin_info(name)
            if info:
                plugin_info[name] = info
        return plugin_info

    def create_workflow(self, name: str, description: str = "") -> WorkflowDefinition:
        """
        Create a new workflow definition.

        Args:
            name: Workflow name
            description: Workflow description

        Returns:
            New workflow definition
        """
        return WorkflowDefinition(name, description)

    def create_workflow_step(self, name: str, tool_name: str, **kwargs) -> WorkflowStep:
        """
        Create a workflow step.

        Args:
            name: Step name
            tool_name: Tool to execute
            **kwargs: Additional step configuration (goes into config dict)

        Returns:
            New workflow step
        """
        # Separate WorkflowStep parameters from tool configuration
        step_params = {}
        config_params = {}

        # Known WorkflowStep parameters
        step_param_names = {
            'dependencies', 'conditions', 'execution_mode', 'timeout',
            'retry_count', 'retry_delay', 'continue_on_failure', 'metadata'
        }

        for key, value in kwargs.items():
            if key in step_param_names:
                step_params[key] = value
            else:
                config_params[key] = value

        return WorkflowStep(
            name=name,
            tool_name=tool_name,
            config=config_params,
            **step_params
        )

    async def execute_advanced_workflow(self, workflow: WorkflowDefinition, target: str,
                                      global_config: Optional[Dict[str, Any]] = None):
        """
        Execute an advanced workflow with dependency management and conditions.

        Args:
            workflow: Workflow definition to execute
            target: Target for the workflow
            global_config: Global configuration for all steps

        Returns:
            Workflow execution result
        """
        return await self.workflow_engine.execute_workflow(workflow, target, global_config)

    def get_running_workflows(self):
        """Get currently running workflows."""
        return self.workflow_engine.get_running_workflows()

    def cancel_workflow(self, workflow_name: str) -> bool:
        """
        Cancel a running workflow.

        Args:
            workflow_name: Name of workflow to cancel

        Returns:
            True if workflow was cancelled
        """
        return self.workflow_engine.cancel_workflow(workflow_name)

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return self.cache_manager.get_cache_stats()

    def clear_cache(self) -> bool:
        """Clear all cached results."""
        return self.cache_manager.clear_cache()

    def invalidate_cache(self, tool_name: Optional[str] = None,
                        target: Optional[str] = None) -> int:
        """
        Invalidate cached results.

        Args:
            tool_name: Tool name pattern (optional)
            target: Target pattern (optional)

        Returns:
            Number of entries invalidated
        """
        return self.cache_manager.invalidate_cache(tool_name, target)

    def query_cached_results(self, tool_name: Optional[str] = None,
                           target: Optional[str] = None,
                           status: Optional[str] = None,
                           limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Query cached results with filters.

        Args:
            tool_name: Tool name to filter by
            target: Target to filter by
            status: Status to filter by
            limit: Maximum number of results

        Returns:
            List of cached results
        """
        if hasattr(self.cache_manager.backend, 'query'):
            return self.cache_manager.backend.query(tool_name, target, status, limit)
        else:
            # Fallback for backends without query support
            return []

    def get_monitoring_dashboard(self) -> str:
        """Get monitoring dashboard as text."""
        if not self.monitoring:
            return "Monitoring is disabled"
        return self.monitoring.dashboard.generate_dashboard()

    def print_monitoring_dashboard(self):
        """Print monitoring dashboard to console."""
        if not self.monitoring:
            print("Monitoring is disabled")
            return
        self.monitoring.dashboard.print_dashboard()

    def save_monitoring_dashboard(self, file_path: str):
        """Save monitoring dashboard to file."""
        if not self.monitoring:
            with open(file_path, 'w') as f:
                f.write("Monitoring is disabled\n")
            return
        self.monitoring.dashboard.save_dashboard(file_path)

    def get_monitoring_summary(self) -> Dict[str, Any]:
        """Get comprehensive monitoring summary."""
        if not self.monitoring:
            return {'monitoring': 'disabled'}
        return self.monitoring.get_monitoring_summary()

    def get_health_status(self) -> Dict[str, Any]:
        """Get current health status."""
        if not self.monitoring:
            return {'health': 'monitoring disabled'}
        return self.monitoring.health.run_all_checks()

    def get_performance_metrics(self, tool_name: Optional[str] = None,
                               hours: int = 24) -> Dict[str, Any]:
        """Get performance metrics."""
        if not self.monitoring:
            return {'performance': 'monitoring disabled'}
        return self.monitoring.performance.get_performance_summary(tool_name, hours)

    def add_health_check(self, name: str, check_function: Callable[[], bool],
                        description: str, timeout: float = 5.0, critical: bool = True):
        """Add a custom health check."""
        if not self.monitoring:
            self.logger.warning("Cannot add health check: monitoring is disabled")
            return

        from .monitoring import HealthCheck
        check = HealthCheck(name, check_function, description, timeout, critical)
        self.monitoring.health.add_check(check)

    def remove_health_check(self, name: str):
        """Remove a health check."""
        if not self.monitoring:
            return
        self.monitoring.health.remove_check(name)

    def generate_advanced_report(self, results: List[ToolResult],
                                output_format: str = 'html',
                                template_name: Optional[str] = None,
                                metadata_overrides: Optional[Dict[str, Any]] = None,
                                custom_sections: Optional[List[ReportSection]] = None) -> str:
        """
        Generate an advanced report from tool results using the reporting system.

        Args:
            results: List of tool results
            output_format: Output format (html, json, xml, csv)
            template_name: Template to use (optional)
            metadata_overrides: Metadata overrides (optional)
            custom_sections: Custom sections to add (optional)

        Returns:
            Formatted report as string
        """
        return self.report_generator.generate_report(
            results, output_format, template_name, metadata_overrides, custom_sections
        )

    def save_advanced_report(self, results: List[ToolResult], file_path: str,
                            output_format: str = 'html',
                            template_name: Optional[str] = None,
                            metadata_overrides: Optional[Dict[str, Any]] = None,
                            custom_sections: Optional[List[ReportSection]] = None) -> str:
        """
        Generate and save an advanced report from tool results.

        Args:
            results: List of tool results
            file_path: Path to save the report
            output_format: Output format (html, json, xml, csv)
            template_name: Template to use (optional)
            metadata_overrides: Metadata overrides (optional)
            custom_sections: Custom sections to add (optional)

        Returns:
            Path to saved report file
        """
        report_content = self.generate_advanced_report(
            results, output_format, template_name, metadata_overrides, custom_sections
        )
        return self.report_generator.save_report(report_content, file_path, output_format)

    def get_available_report_formats(self) -> List[str]:
        """Get list of available report formats."""
        return self.report_generator.get_available_formats()

    def get_available_report_templates(self) -> List[str]:
        """Get list of available report templates."""
        return self.report_generator.get_available_templates()

    def add_report_template(self, template: ReportTemplate):
        """Add a custom report template."""
        self.report_generator.add_template(template)

    def create_report_section(self, title: str, content: str,
                             data: Optional[Dict[str, Any]] = None) -> ReportSection:
        """Create a report section."""
        return ReportSection(title=title, content=content, data=data)

    def generate_workflow_report(self, workflow_result, output_format: str = 'html',
                                template_name: Optional[str] = None) -> str:
        """
        Generate a report from workflow execution results.

        Args:
            workflow_result: WorkflowResult from workflow execution
            output_format: Output format (html, json, xml, csv)
            template_name: Template to use (optional)

        Returns:
            Formatted report as string
        """
        # Convert workflow results to tool results
        tool_results = list(workflow_result.step_results.values())

        # Add workflow-specific metadata
        metadata_overrides = {
            'title': f'Workflow Report - {workflow_result.workflow_name}',
            'description': f'Results from workflow execution: {workflow_result.workflow_name}',
            'tags': ['workflow', 'automation', workflow_result.workflow_name],
            'custom_fields': {
                'workflow_name': workflow_result.workflow_name,
                'workflow_status': workflow_result.status.value,
                'execution_time': workflow_result.execution_time,
                'total_steps': len(workflow_result.step_results),
                'failed_steps': len(workflow_result.failed_steps),
                'skipped_steps': len(workflow_result.skipped_steps)
            }
        }

        # Add workflow-specific sections
        custom_sections = [
            ReportSection(
                title="Workflow Overview",
                content=f"Workflow '{workflow_result.workflow_name}' executed with status: {workflow_result.status.value}",
                data={
                    'Workflow Name': workflow_result.workflow_name,
                    'Status': workflow_result.status.value,
                    'Execution Time': f"{workflow_result.execution_time:.2f} seconds",
                    'Total Steps': len(workflow_result.step_results),
                    'Successful Steps': len(workflow_result.step_results) - len(workflow_result.failed_steps),
                    'Failed Steps': len(workflow_result.failed_steps),
                    'Skipped Steps': len(workflow_result.skipped_steps),
                    'Total Findings': workflow_result.total_findings
                }
            )
        ]

        if workflow_result.failed_steps:
            custom_sections.append(ReportSection(
                title="Failed Steps",
                content=f"The following {len(workflow_result.failed_steps)} steps failed during execution:",
                data={step: "Failed" for step in workflow_result.failed_steps}
            ))

        if workflow_result.skipped_steps:
            custom_sections.append(ReportSection(
                title="Skipped Steps",
                content=f"The following {len(workflow_result.skipped_steps)} steps were skipped:",
                data={step: "Skipped (conditions not met)" for step in workflow_result.skipped_steps}
            ))

        return self.generate_advanced_report(
            tool_results, output_format, template_name, metadata_overrides, custom_sections
        )

    def get_security_status(self) -> Dict[str, Any]:
        """Get current security status and configuration."""
        if not self.secure_mode:
            return {'security': 'disabled'}

        return {
            'security_enabled': True,
            'sandbox_enabled': self.security_policy.use_sandbox if self.security_policy else False,
            'command_timeout': self.security_policy.command_timeout if self.security_policy else None,
            'max_memory_mb': self.security_policy.max_memory_mb if self.security_policy else None,
            'allowed_commands': list(self.security_policy.allowed_commands) if self.security_policy else [],
            'resource_monitoring': self.security_policy.monitor_resources if self.security_policy else False
        }

    def update_security_policy(self, **policy_updates):
        """
        Update security policy settings.

        Args:
            **policy_updates: Security policy attributes to update
        """
        if not self.secure_mode or not self.security_policy:
            self.logger.warning("Cannot update security policy: security mode disabled")
            return

        for key, value in policy_updates.items():
            if hasattr(self.security_policy, key):
                setattr(self.security_policy, key, value)
                self.logger.info(f"Updated security policy: {key} = {value}")
            else:
                self.logger.warning(f"Unknown security policy attribute: {key}")

    def validate_input(self, input_value: str, input_name: str = "input") -> str:
        """
        Validate input using security policy.

        Args:
            input_value: Input to validate
            input_name: Name of input for error reporting

        Returns:
            Validated and sanitized input

        Raises:
            SecurityViolation: If input violates security policy
        """
        if not self.secure_mode or not self.security_manager:
            return input_value

        return self.security_manager.validator.validate_input(input_value, input_name)

    def is_command_allowed(self, command: str) -> bool:
        """
        Check if a command is allowed by security policy.

        Args:
            command: Command to check

        Returns:
            True if command is allowed, False otherwise
        """
        if not self.secure_mode or not self.security_policy:
            return True

        try:
            import shlex
            args = shlex.split(command)
            if args:
                from pathlib import Path
                base_command = Path(args[0]).name
                return base_command in self.security_policy.allowed_commands
        except Exception:
            return False

        return False

    def get_resource_usage(self) -> Dict[str, Any]:
        """
        Get current resource usage information.

        Returns:
            Dictionary with resource usage data
        """
        if not self.secure_mode or not self.security_manager:
            return {'resource_monitoring': 'disabled'}

        try:
            import psutil
            return {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_usage_percent': psutil.disk_usage('/').percent,
                'active_processes': len(self.security_manager.resource_monitor.active_processes),
                'max_processes': self.security_policy.max_processes if self.security_policy else None
            }
        except ImportError:
            return {'error': 'psutil not available - resource monitoring disabled'}
        except Exception as e:
            return {'error': str(e)}

    def cleanup_security(self):
        """Clean up security resources."""
        if self.security_manager:
            self.security_manager.cleanup()
            self.logger.info("Security resources cleaned up")
    
    def aggregate_findings(self, results: Dict[str, ToolResult]) -> Dict[str, Any]:
        """
        Aggregate findings from multiple tool results.
        
        Args:
            results: Dictionary of tool results
            
        Returns:
            Aggregated findings summary
        """
        aggregated = {
            'total_findings': 0,
            'findings_by_severity': {
                'critical': 0,
                'high': 0,
                'medium': 0,
                'low': 0,
                'info': 0
            },
            'findings_by_tool': {},
            'all_findings': [],
            'execution_summary': {}
        }
        
        for tool_name, result in results.items():
            # Execution summary
            aggregated['execution_summary'][tool_name] = {
                'status': result.status.value,
                'execution_time': result.execution_time,
                'findings_count': len(result.findings)
            }
            
            # Findings
            tool_findings = result.findings
            aggregated['findings_by_tool'][tool_name] = len(tool_findings)
            aggregated['all_findings'].extend(tool_findings)
            
            # Count by severity
            for finding in tool_findings:
                severity = finding.get('severity', 'info').lower()
                if severity in aggregated['findings_by_severity']:
                    aggregated['findings_by_severity'][severity] += 1
        
        aggregated['total_findings'] = len(aggregated['all_findings'])
        
        return aggregated
    
    def generate_report(self, results: Dict[str, ToolResult], format: str = 'json') -> str:
        """
        Generate a report from tool results.
        
        Args:
            results: Dictionary of tool results
            format: Report format ('json', 'text', 'html')
            
        Returns:
            Formatted report string
        """
        aggregated = self.aggregate_findings(results)
        
        if format == 'json':
            import json
            return json.dumps(aggregated, indent=2, default=str)
        
        elif format == 'text':
            return self._generate_text_report(aggregated)
        
        elif format == 'html':
            return self._generate_html_report(aggregated)
        
        else:
            raise ValueError(f"Unsupported report format: {format}")
    
    def _generate_text_report(self, aggregated: Dict[str, Any]) -> str:
        """Generate a text-based report"""
        lines = []
        lines.append("=== SECURITY SCAN REPORT ===")
        lines.append("")
        
        # Summary
        lines.append("SUMMARY:")
        lines.append(f"Total Findings: {aggregated['total_findings']}")
        lines.append("")
        
        # Severity breakdown
        lines.append("FINDINGS BY SEVERITY:")
        for severity, count in aggregated['findings_by_severity'].items():
            lines.append(f"  {severity.upper()}: {count}")
        lines.append("")
        
        # Tool breakdown
        lines.append("FINDINGS BY TOOL:")
        for tool, count in aggregated['findings_by_tool'].items():
            lines.append(f"  {tool}: {count}")
        lines.append("")
        
        # Execution summary
        lines.append("EXECUTION SUMMARY:")
        for tool, summary in aggregated['execution_summary'].items():
            lines.append(f"  {tool}:")
            lines.append(f"    Status: {summary['status']}")
            lines.append(f"    Time: {summary['execution_time']:.2f}s")
            lines.append(f"    Findings: {summary['findings_count']}")
        
        return "\n".join(lines)
    
    def _generate_html_report(self, aggregated: Dict[str, Any]) -> str:
        """Generate an HTML report"""
        html = """
        <html>
        <head><title>Security Scan Report</title></head>
        <body>
        <h1>Security Scan Report</h1>
        <h2>Summary</h2>
        <p>Total Findings: {total}</p>
        <h2>Findings by Severity</h2>
        <ul>
        """.format(total=aggregated['total_findings'])
        
        for severity, count in aggregated['findings_by_severity'].items():
            html += f"<li>{severity.upper()}: {count}</li>"
        
        html += "</ul></body></html>"
        return html

    def enable_mock_mode(self, mock_data: Optional[Dict[str, Dict[str, Any]]] = None):
        """
        Enable mock mode for all connectors.

        Args:
            mock_data: Optional mock data for specific tools
        """
        self.mock_mode = True
        for tool_name, connector in self._connectors.items():
            tool_mock_data = mock_data.get(tool_name, {}) if mock_data else {}
            connector.enable_mock_mode(tool_mock_data)
        self.logger.info("Mock mode enabled for all connectors")

    def disable_mock_mode(self):
        """Disable mock mode for all connectors."""
        self.mock_mode = False
        for connector in self._connectors.values():
            connector.disable_mock_mode()
        self.logger.info("Mock mode disabled for all connectors")

    def get_tool_status(self) -> Dict[str, Dict[str, Any]]:
        """
        Get status information for all registered tools.

        Returns:
            Dictionary with tool status information
        """
        status = {}
        for tool_name, connector in self._connectors.items():
            status[tool_name] = connector.get_tool_info()
        return status

    def get_available_tools_detailed(self) -> Dict[str, Dict[str, Any]]:
        """
        Get detailed information about available tools.

        Returns:
            Dictionary with detailed tool information
        """
        available = {}
        for tool_name, connector in self._connectors.items():
            if connector.is_available():
                available[tool_name] = connector.get_tool_info()
        return available
