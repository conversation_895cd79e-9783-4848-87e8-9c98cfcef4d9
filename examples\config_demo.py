#!/usr/bin/env python3
"""
Configuration System Demo

This script demonstrates the configuration management capabilities of the tool connector framework.
It shows how to:
1. Load configuration from files
2. Override settings with environment variables
3. Use configuration in tool managers
4. Create custom configurations
5. Save and load configurations
"""

import os
import sys
import tempfile
from pathlib import Path

# Add the parent directory to the path so we can import tool_connectors
sys.path.insert(0, str(Path(__file__).parent.parent))

from tool_connectors.config import ConfigManager, get_config_manager, get_config
from tool_connectors import ToolManager
import yaml
import json


def demo_default_configuration():
    """Demonstrate default configuration loading"""
    print("=== Default Configuration Demo ===")
    
    config = ConfigManager()
    
    print("Configuration sources:", config.get_config_sources())
    print()
    
    print("Default tool paths:")
    for tool in ['nmap', 'nikto', 'sqlmap', 'gobuster']:
        path = config.get_tool_path(tool)
        timeout = config.get_tool_timeout(tool)
        print(f"  {tool}: path='{path}', timeout={timeout}s")
    print()
    
    print("Default workflows:")
    for workflow in ['quick', 'comprehensive', 'web', 'network']:
        tools = config.get_workflow_tools(workflow)
        print(f"  {workflow}: {tools}")
    print()
    
    print("General settings:")
    print(f"  Mock mode: {config.is_mock_mode()}")
    print(f"  Max concurrent tools: {config.get_max_concurrent_tools()}")
    print(f"  Output directory: {config.get_output_directory()}")
    print()


def demo_custom_configuration():
    """Demonstrate custom configuration loading"""
    print("=== Custom Configuration Demo ===")
    
    # Create a temporary config file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        custom_config = {
            'general': {
                'mock_mode': True,
                'max_concurrent_tools': 8,
                'output_directory': '/tmp/custom_scans',
                'log_level': 'DEBUG'
            },
            'tools': {
                'nmap': {
                    'path': '/usr/local/bin/nmap',
                    'timeout': 600,
                    'default_params': {
                        'timing': 3,
                        'ports': '1-10000'
                    }
                },
                'nikto': {
                    'path': '/opt/nikto/nikto.pl',
                    'timeout': 900,
                    'default_params': {
                        'timeout': 60,
                        'plugins': '@@ALL'
                    }
                }
            },
            'workflows': {
                'custom_quick': ['nmap'],
                'custom_web': ['nikto', 'gobuster']
            }
        }
        
        yaml.dump(custom_config, f)
        config_file = f.name
    
    try:
        # Load custom configuration
        config = ConfigManager(config_file)
        
        print(f"Loaded configuration from: {config_file}")
        print("Configuration sources:", config.get_config_sources())
        print()
        
        print("Custom settings:")
        print(f"  Mock mode: {config.is_mock_mode()}")
        print(f"  Max concurrent tools: {config.get_max_concurrent_tools()}")
        print(f"  Output directory: {config.get_output_directory()}")
        print(f"  Log level: {config.get('general.log_level')}")
        print()
        
        print("Custom tool configurations:")
        for tool in ['nmap', 'nikto']:
            tool_config = config.get_tool_config(tool)
            print(f"  {tool}:")
            print(f"    Path: {tool_config.get('path')}")
            print(f"    Timeout: {tool_config.get('timeout')}s")
            print(f"    Default params: {tool_config.get('default_params', {})}")
        print()
        
        print("Custom workflows:")
        for workflow in ['custom_quick', 'custom_web']:
            tools = config.get_workflow_tools(workflow)
            print(f"  {workflow}: {tools}")
        print()
        
    finally:
        # Clean up temporary file
        os.unlink(config_file)


def demo_environment_override():
    """Demonstrate environment variable overrides"""
    print("=== Environment Variable Override Demo ===")
    
    # Set some environment variables
    env_vars = {
        'TOOLCONNECTOR_NMAP_PATH': '/custom/nmap',
        'TOOLCONNECTOR_NMAP_TIMEOUT': '1200',
        'TOOLCONNECTOR_MOCK_MODE': 'true',
        'TOOLCONNECTOR_MAX_CONCURRENT': '12',
        'TOOLCONNECTOR_OUTPUT_DIR': '/env/output'
    }
    
    # Save original environment
    original_env = {}
    for key in env_vars:
        original_env[key] = os.environ.get(key)
        os.environ[key] = env_vars[key]
    
    try:
        config = ConfigManager()
        
        print("Environment variables set:")
        for key, value in env_vars.items():
            print(f"  {key}={value}")
        print()
        
        print("Configuration after environment override:")
        print(f"  Nmap path: {config.get_tool_path('nmap')}")
        print(f"  Nmap timeout: {config.get_tool_timeout('nmap')}s")
        print(f"  Mock mode: {config.is_mock_mode()}")
        print(f"  Max concurrent tools: {config.get_max_concurrent_tools()}")
        print(f"  Output directory: {config.get_output_directory()}")
        print()
        
    finally:
        # Restore original environment
        for key, value in original_env.items():
            if value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = value


def demo_tool_manager_integration():
    """Demonstrate ToolManager integration with configuration"""
    print("=== ToolManager Configuration Integration Demo ===")
    
    # Create a custom config
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        config = {
            'general': {
                'mock_mode': True,
                'max_concurrent_tools': 3
            },
            'tools': {
                'nmap': {
                    'timeout': 120,
                    'default_params': {
                        'timing': 5,
                        'ports': '80,443,8080'
                    }
                }
            },
            'workflows': {
                'demo_scan': ['nmap', 'nikto']
            }
        }
        
        yaml.dump(config, f)
        config_file = f.name
    
    try:
        # Create ToolManager with custom config
        manager = ToolManager(config_file=config_file)
        
        print(f"ToolManager created with config: {config_file}")
        print(f"Mock mode enabled: {manager.mock_mode}")
        print()
        
        # Show tool status
        print("Tool status:")
        status = manager.get_tool_status()
        for tool_name, info in status.items():
            if info['available']:
                print(f"  {tool_name}: Available (Mock: {info['mock_mode']})")
        print()
        
        # Execute a workflow
        print("Executing demo_scan workflow...")
        results = manager.execute_scan_workflow('example.com', workflow_type='demo_scan')
        
        print("Workflow results:")
        for tool_name, result in results.items():
            print(f"  {tool_name}: {result.status.value} ({len(result.findings)} findings)")
        print()
        
    finally:
        os.unlink(config_file)


def demo_configuration_save_load():
    """Demonstrate saving and loading configurations"""
    print("=== Configuration Save/Load Demo ===")
    
    # Create and modify a configuration
    config = ConfigManager()
    
    # Make some modifications
    config.set('general.mock_mode', True)
    config.set('general.custom_setting', 'demo_value')
    config.set('tools.custom_tool.path', '/path/to/custom/tool')
    config.set('tools.custom_tool.timeout', 300)
    config.set('workflows.custom_workflow', ['nmap', 'custom_tool'])
    
    # Save to YAML
    yaml_file = tempfile.mktemp(suffix='.yaml')
    config.save_config(yaml_file, 'yaml')
    print(f"Configuration saved to YAML: {yaml_file}")
    
    # Save to JSON
    json_file = tempfile.mktemp(suffix='.json')
    config.save_config(json_file, 'json')
    print(f"Configuration saved to JSON: {json_file}")
    
    try:
        # Load from YAML and verify
        loaded_config = ConfigManager(yaml_file)
        print("\nLoaded from YAML:")
        print(f"  Mock mode: {loaded_config.is_mock_mode()}")
        print(f"  Custom setting: {loaded_config.get('general.custom_setting')}")
        print(f"  Custom tool path: {loaded_config.get('tools.custom_tool.path')}")
        print(f"  Custom workflow: {loaded_config.get_workflow_tools('custom_workflow')}")
        
        # Show file contents
        print(f"\nYAML file contents:")
        with open(yaml_file, 'r') as f:
            print(f.read()[:500] + "..." if len(f.read()) > 500 else f.read())
        
    finally:
        # Clean up
        for file_path in [yaml_file, json_file]:
            if os.path.exists(file_path):
                os.unlink(file_path)


def demo_global_config_functions():
    """Demonstrate global configuration functions"""
    print("=== Global Configuration Functions Demo ===")
    
    # Use global functions
    print("Using global configuration functions:")
    print(f"  Nmap path: {get_config('tools.nmap.path')}")
    print(f"  Default timeout: {get_config('tools.nmap.timeout', 'not found')}")
    print(f"  Non-existent key: {get_config('nonexistent.key', 'default_value')}")
    print()
    
    # Get tool configuration
    from tool_connectors.config import get_tool_config
    nmap_config = get_tool_config('nmap')
    print("Nmap configuration:")
    for key, value in nmap_config.items():
        print(f"  {key}: {value}")
    print()


def main():
    """Main function to run all configuration demos"""
    print("Tool Connectors Framework - Configuration System Demo")
    print("=" * 60)
    print()
    
    try:
        demo_default_configuration()
        demo_custom_configuration()
        demo_environment_override()
        demo_tool_manager_integration()
        demo_configuration_save_load()
        demo_global_config_functions()
        
        print("=== Configuration Demo Summary ===")
        print("All configuration demos completed successfully!")
        print("The configuration system supports:")
        print("- Default configurations with sensible defaults")
        print("- Loading from YAML and JSON files")
        print("- Environment variable overrides")
        print("- Integration with ToolManager")
        print("- Saving and loading custom configurations")
        print("- Global configuration functions")
        
    except Exception as e:
        print(f"Error during demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
