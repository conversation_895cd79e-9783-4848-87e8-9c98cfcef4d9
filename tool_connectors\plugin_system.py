"""
Plugin system for tool connectors.

This module provides a plugin architecture that allows easy addition of new tool connectors
without modifying core code, including auto-discovery and registration.
"""

import importlib
import importlib.util
import inspect
import logging
import os
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Type, Union

from .base import BaseToolConnector


class PluginError(Exception):
    """Exception raised for plugin-related errors."""
    pass


class PluginInfo:
    """Information about a plugin."""
    
    def __init__(self, name: str, connector_class: Type[BaseToolConnector], 
                 module_path: str, metadata: Optional[Dict[str, Any]] = None):
        """
        Initialize plugin information.
        
        Args:
            name: Plugin name
            connector_class: Connector class
            module_path: Path to the plugin module
            metadata: Optional plugin metadata
        """
        self.name = name
        self.connector_class = connector_class
        self.module_path = module_path
        self.metadata = {}

        # Extract metadata from class first
        if hasattr(connector_class, '__plugin_metadata__'):
            self.metadata.update(connector_class.__plugin_metadata__)

        # Then update with provided metadata (this takes precedence)
        if metadata:
            self.metadata.update(metadata)
    
    def __repr__(self):
        return f"PluginInfo(name='{self.name}', class={self.connector_class.__name__})"


class PluginManager:
    """
    Plugin manager for tool connectors.
    
    Handles discovery, loading, and registration of plugin connectors.
    """
    
    def __init__(self):
        """Initialize the plugin manager."""
        self.logger = logging.getLogger(self.__class__.__name__)
        self._plugins: Dict[str, PluginInfo] = {}
        self._plugin_directories: List[str] = []
        
        # Add default plugin directories
        self._add_default_plugin_directories()
    
    def _add_default_plugin_directories(self):
        """Add default plugin directories."""
        # Current package plugins directory
        package_plugins = Path(__file__).parent / 'plugins'
        if package_plugins.exists():
            self.add_plugin_directory(str(package_plugins))
        
        # User plugins directory
        user_plugins = Path.home() / '.tool-connectors' / 'plugins'
        if user_plugins.exists():
            self.add_plugin_directory(str(user_plugins))
        
        # System plugins directory
        system_plugins = Path('/usr/local/share/tool-connectors/plugins')
        if system_plugins.exists():
            self.add_plugin_directory(str(system_plugins))
    
    def add_plugin_directory(self, directory: str):
        """
        Add a directory to search for plugins.
        
        Args:
            directory: Path to plugin directory
        """
        if directory not in self._plugin_directories:
            self._plugin_directories.append(directory)
            self.logger.debug(f"Added plugin directory: {directory}")
    
    def discover_plugins(self, auto_load: bool = True) -> List[PluginInfo]:
        """
        Discover plugins in registered directories.
        
        Args:
            auto_load: Whether to automatically load discovered plugins
            
        Returns:
            List of discovered plugin information
        """
        discovered = []
        
        for directory in self._plugin_directories:
            try:
                plugins = self._discover_plugins_in_directory(directory)
                discovered.extend(plugins)
                
                if auto_load:
                    for plugin in plugins:
                        self.register_plugin(plugin)
                        
            except Exception as e:
                self.logger.warning(f"Error discovering plugins in {directory}: {e}")
        
        return discovered
    
    def _discover_plugins_in_directory(self, directory: str) -> List[PluginInfo]:
        """Discover plugins in a specific directory."""
        plugins = []
        directory_path = Path(directory)
        
        if not directory_path.exists():
            return plugins
        
        # Look for Python files
        for file_path in directory_path.glob('*.py'):
            if file_path.name.startswith('_'):
                continue  # Skip private files
            
            try:
                plugin_info = self._load_plugin_from_file(file_path)
                if plugin_info:
                    plugins.append(plugin_info)
            except Exception as e:
                self.logger.warning(f"Error loading plugin from {file_path}: {e}")
        
        # Look for Python packages
        for package_path in directory_path.iterdir():
            if package_path.is_dir() and (package_path / '__init__.py').exists():
                try:
                    plugin_info = self._load_plugin_from_package(package_path)
                    if plugin_info:
                        plugins.append(plugin_info)
                except Exception as e:
                    self.logger.warning(f"Error loading plugin from {package_path}: {e}")
        
        return plugins
    
    def _load_plugin_from_file(self, file_path: Path) -> Optional[PluginInfo]:
        """Load a plugin from a Python file."""
        module_name = file_path.stem
        spec = importlib.util.spec_from_file_location(module_name, file_path)
        
        if spec is None or spec.loader is None:
            return None
        
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        return self._extract_plugin_from_module(module, str(file_path))
    
    def _load_plugin_from_package(self, package_path: Path) -> Optional[PluginInfo]:
        """Load a plugin from a Python package."""
        package_name = package_path.name
        
        # Add parent directory to sys.path temporarily
        parent_dir = str(package_path.parent)
        if parent_dir not in sys.path:
            sys.path.insert(0, parent_dir)
            try:
                module = importlib.import_module(package_name)
                return self._extract_plugin_from_module(module, str(package_path))
            finally:
                sys.path.remove(parent_dir)
        else:
            module = importlib.import_module(package_name)
            return self._extract_plugin_from_module(module, str(package_path))
    
    def _extract_plugin_from_module(self, module, module_path: str) -> Optional[PluginInfo]:
        """Extract plugin information from a loaded module."""
        connector_classes = []
        
        # Look for BaseToolConnector subclasses
        for name, obj in inspect.getmembers(module, inspect.isclass):
            if (issubclass(obj, BaseToolConnector) and 
                obj is not BaseToolConnector and 
                obj.__module__ == module.__name__):
                connector_classes.append((name, obj))
        
        if not connector_classes:
            return None
        
        if len(connector_classes) > 1:
            self.logger.warning(f"Multiple connector classes found in {module_path}, using first one")
        
        class_name, connector_class = connector_classes[0]
        
        # Determine plugin name
        plugin_name = getattr(module, '__plugin_name__', None)
        if not plugin_name:
            plugin_name = getattr(connector_class, 'tool_name', class_name.lower())
        
        # Extract metadata
        metadata = {}
        if hasattr(module, '__plugin_metadata__'):
            metadata.update(module.__plugin_metadata__)
        
        return PluginInfo(plugin_name, connector_class, module_path, metadata)
    
    def register_plugin(self, plugin_info: PluginInfo):
        """
        Register a plugin.
        
        Args:
            plugin_info: Plugin information to register
        """
        if plugin_info.name in self._plugins:
            existing = self._plugins[plugin_info.name]
            self.logger.warning(f"Plugin '{plugin_info.name}' already registered from {existing.module_path}, "
                              f"replacing with {plugin_info.module_path}")
        
        self._plugins[plugin_info.name] = plugin_info
        self.logger.info(f"Registered plugin: {plugin_info.name} ({plugin_info.connector_class.__name__})")
    
    def register_plugin_class(self, name: str, connector_class: Type[BaseToolConnector], 
                            metadata: Optional[Dict[str, Any]] = None):
        """
        Register a plugin class directly.
        
        Args:
            name: Plugin name
            connector_class: Connector class
            metadata: Optional plugin metadata
        """
        plugin_info = PluginInfo(name, connector_class, '<direct>', metadata)
        self.register_plugin(plugin_info)
    
    def get_plugin(self, name: str) -> Optional[PluginInfo]:
        """
        Get plugin information by name.
        
        Args:
            name: Plugin name
            
        Returns:
            Plugin information or None if not found
        """
        return self._plugins.get(name)
    
    def get_all_plugins(self) -> Dict[str, PluginInfo]:
        """Get all registered plugins."""
        return self._plugins.copy()
    
    def create_connector(self, name: str, **kwargs) -> Optional[BaseToolConnector]:
        """
        Create a connector instance from a plugin.
        
        Args:
            name: Plugin name
            **kwargs: Arguments to pass to connector constructor
            
        Returns:
            Connector instance or None if plugin not found
        """
        plugin_info = self.get_plugin(name)
        if not plugin_info:
            return None
        
        try:
            return plugin_info.connector_class(**kwargs)
        except Exception as e:
            self.logger.error(f"Error creating connector for plugin '{name}': {e}")
            return None
    
    def unregister_plugin(self, name: str) -> bool:
        """
        Unregister a plugin.
        
        Args:
            name: Plugin name
            
        Returns:
            True if plugin was unregistered, False if not found
        """
        if name in self._plugins:
            del self._plugins[name]
            self.logger.info(f"Unregistered plugin: {name}")
            return True
        return False
    
    def list_plugins(self) -> List[str]:
        """Get list of registered plugin names."""
        return list(self._plugins.keys())
    
    def get_plugin_metadata(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Get metadata for a plugin.
        
        Args:
            name: Plugin name
            
        Returns:
            Plugin metadata or None if not found
        """
        plugin_info = self.get_plugin(name)
        return plugin_info.metadata if plugin_info else None


# Global plugin manager instance
_plugin_manager: Optional[PluginManager] = None


def get_plugin_manager() -> PluginManager:
    """
    Get the global plugin manager instance.
    
    Returns:
        PluginManager instance
    """
    global _plugin_manager
    
    if _plugin_manager is None:
        _plugin_manager = PluginManager()
    
    return _plugin_manager


def discover_plugins(auto_load: bool = True) -> List[PluginInfo]:
    """
    Discover plugins using the global plugin manager.
    
    Args:
        auto_load: Whether to automatically load discovered plugins
        
    Returns:
        List of discovered plugin information
    """
    return get_plugin_manager().discover_plugins(auto_load)


def register_plugin_class(name: str, connector_class: Type[BaseToolConnector], 
                         metadata: Optional[Dict[str, Any]] = None):
    """
    Register a plugin class using the global plugin manager.
    
    Args:
        name: Plugin name
        connector_class: Connector class
        metadata: Optional plugin metadata
    """
    get_plugin_manager().register_plugin_class(name, connector_class, metadata)


def get_plugin(name: str) -> Optional[PluginInfo]:
    """
    Get plugin information using the global plugin manager.
    
    Args:
        name: Plugin name
        
    Returns:
        Plugin information or None if not found
    """
    return get_plugin_manager().get_plugin(name)
