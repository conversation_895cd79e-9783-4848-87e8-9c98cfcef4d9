{"metadata": {"title": "Security Scan Report", "description": "Automated security scan results", "author": "Tool Connectors Framework", "created_at": "2025-07-24T07:38:41.284507", "version": "1.0", "tags": [], "custom_fields": {}}, "sections": [{"title": "Tool Execution Analysis", "content": "Executed 4 tools with an average execution time of 1.06 seconds.", "data": {"total_tools": 4, "successful_tools": 4, "failed_tools": 0, "success_rate": 100.0, "total_time": 4.222268104553223, "avg_time": 1.0555670261383057, "fastest_tool": "sqlmap", "slowest_tool": "nikto"}, "charts": []}, {"title": "Findings Analysis", "content": "Discovered 2 findings across 1 tools.", "data": {"total_findings": 2, "affected_tools": 1, "severity_distribution": {"low": 2}, "finding_types": {"open_port": 2}, "tools_with_findings": ["nmap"]}, "charts": []}, {"title": "Risk Assessment", "content": "Overall risk level: MINIMAL", "data": {"overall_risk": "MINIMAL", "risk_score": 2, "critical_findings": 0, "high_findings": 0, "medium_findings": 0, "low_findings": 2, "info_findings": 0}, "charts": []}], "results": [{"tool_name": "nmap", "command": "nmap -sS -sV -T3 -oX - demo.example.com", "status": "completed", "exit_code": 0, "execution_time": 1.0729119777679443, "timestamp": 1753335516.6007316, "findings_count": 2, "findings": [{"type": "open_port", "severity": "low", "host": "demo.example.com", "port": "80", "protocol": "tcp", "service": "http", "version": "1.0", "description": "Open tcp port 80 running http", "recommendation": "Consider using HTTPS instead of HTTP"}, {"type": "open_port", "severity": "low", "host": "demo.example.com", "port": "443", "protocol": "tcp", "service": "https", "version": "1.0", "description": "Open tcp port 443 running https", "recommendation": "Review if this service needs to be publicly accessible"}], "error_message": null}, {"tool_name": "nikto", "command": "nikto -h demo.example.com -timeout 10 -nointeractive", "status": "completed", "exit_code": 0, "execution_time": 1.1165378093719482, "timestamp": 1753335517.735193, "findings_count": 0, "findings": [], "error_message": null}, {"tool_name": "sqlmap", "command": "sqlmap -u demo.example.com --threads 1 --level 1 --risk 1 --batch --output-dir /tmp/sqlmap --flush-session", "status": "completed", "exit_code": 0, "execution_time": 1.014922857284546, "timestamp": 1753335519.2318454, "findings_count": 0, "findings": [], "error_message": null}, {"tool_name": "gobuster", "command": "gobuster dir -u demo.example.com -w /usr/share/wordlists/dirb/common.txt -x php,html,txt -t 10 --timeout 10s -q", "status": "completed", "exit_code": 0, "execution_time": 1.0178954601287842, "timestamp": 1753335520.2514174, "findings_count": 0, "findings": [], "error_message": null}], "summary": {"total_tools_executed": 4, "total_findings": 2, "status_distribution": {"completed": 4}, "severity_distribution": {"low": 2}, "tool_distribution": {"nmap": 1, "nikto": 1, "sqlmap": 1, "gobuster": 1}, "execution_time_total": 4.222268104553223, "execution_time_average": 1.0555670261383057}}