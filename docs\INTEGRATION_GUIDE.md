# Tool Connectors Framework - Integration Guide

This guide provides comprehensive examples and documentation for integrating the Tool Connectors Framework with broader ethical hacking AI systems, including API endpoints, workflow orchestration, and advanced usage patterns.

## Table of Contents

1. [Quick Start](#quick-start)
2. [API Integration](#api-integration)
3. [Workflow Orchestration](#workflow-orchestration)
4. [Security Integration](#security-integration)
5. [Monitoring and Reporting](#monitoring-and-reporting)
6. [Advanced Examples](#advanced-examples)
7. [Best Practices](#best-practices)

## Quick Start

### Basic Usage

```python
from tool_connectors import ToolManager

# Initialize with security and monitoring enabled
manager = ToolManager(
    mock_mode=False,  # Set to True for testing without actual tools
    secure_mode=True,  # Enable security features
    monitoring_enabled=True,  # Enable performance monitoring
    cache_enabled=True  # Enable result caching
)

# Execute a single tool
result = manager.execute_tool('nmap', 'example.com', scan_type='tcp')
print(f"Status: {result.status.value}")
print(f"Findings: {len(result.findings)}")

# Generate a report
report = manager.generate_advanced_report([result], 'html')
manager.save_advanced_report([result], 'scan_report.html', 'html')
```

### Configuration

```python
# Using configuration file
manager = ToolManager(config_file='config/security_tools.yaml')

# Custom security policy
from tool_connectors.security import SecurityPolicy

policy = SecurityPolicy(
    command_timeout=600,
    max_memory_mb=2048,
    use_sandbox=True,
    log_all_commands=True
)

manager = ToolManager(
    secure_mode=True,
    security_policy=policy
)
```

## API Integration

### REST API Wrapper

Create a REST API wrapper for the tool connectors:

```python
from flask import Flask, request, jsonify
from tool_connectors import ToolManager
from tool_connectors.security import SecurityViolation

app = Flask(__name__)
manager = ToolManager(secure_mode=True, monitoring_enabled=True)

@app.route('/api/scan', methods=['POST'])
def execute_scan():
    """Execute a security scan."""
    try:
        data = request.get_json()
        
        # Validate input
        tool = manager.validate_input(data.get('tool', ''), 'tool')
        target = manager.validate_input(data.get('target', ''), 'target')
        
        # Check authorization
        if not manager.is_command_allowed(f"{tool} {target}"):
            return jsonify({'error': 'Command not authorized'}), 403
        
        # Execute scan
        result = manager.execute_tool(tool, target, **data.get('params', {}))
        
        return jsonify({
            'status': result.status.value,
            'tool': result.tool_name,
            'execution_time': result.execution_time,
            'findings_count': len(result.findings),
            'findings': result.findings[:10],  # Limit for API response
            'timestamp': result.timestamp
        })
        
    except SecurityViolation as e:
        return jsonify({'error': f'Security violation: {e.violation_type}'}), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/workflow', methods=['POST'])
def execute_workflow():
    """Execute a security workflow."""
    try:
        workflow_config = request.get_json()
        
        # Execute workflow
        result = manager.workflow_engine.execute_workflow(workflow_config)
        
        return jsonify({
            'workflow_name': result.workflow_name,
            'status': result.status.value,
            'execution_time': result.execution_time,
            'total_steps': len(result.step_results),
            'failed_steps': len(result.failed_steps),
            'total_findings': result.total_findings,
            'step_results': {
                name: {
                    'status': step.status.value,
                    'findings_count': len(step.findings)
                }
                for name, step in result.step_results.items()
            }
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/report', methods=['POST'])
def generate_report():
    """Generate a security report."""
    try:
        data = request.get_json()
        
        # Get results from cache or database
        results = get_scan_results(data.get('scan_ids', []))
        
        # Generate report
        report_format = data.get('format', 'html')
        template = data.get('template', 'security_assessment')
        
        report_content = manager.generate_advanced_report(
            results, report_format, template
        )
        
        return jsonify({
            'report': report_content,
            'format': report_format,
            'results_count': len(results)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status', methods=['GET'])
def get_status():
    """Get system status and health."""
    return jsonify({
        'security': manager.get_security_status(),
        'health': manager.get_health_status(),
        'resources': manager.get_resource_usage(),
        'monitoring': manager.get_monitoring_summary()
    })

if __name__ == '__main__':
    app.run(debug=False, host='0.0.0.0', port=5000)
```

### GraphQL API

```python
import graphene
from graphene import ObjectType, String, List, Field, Mutation, Schema

class ScanResult(ObjectType):
    tool_name = String()
    status = String()
    execution_time = graphene.Float()
    findings_count = graphene.Int()
    findings = List(String)

class ExecuteScan(Mutation):
    class Arguments:
        tool = String(required=True)
        target = String(required=True)
        params = String()
    
    result = Field(ScanResult)
    
    def mutate(self, info, tool, target, params=None):
        try:
            # Parse params
            scan_params = json.loads(params) if params else {}
            
            # Execute scan
            result = manager.execute_tool(tool, target, **scan_params)
            
            return ExecuteScan(result=ScanResult(
                tool_name=result.tool_name,
                status=result.status.value,
                execution_time=result.execution_time,
                findings_count=len(result.findings),
                findings=[str(f) for f in result.findings[:10]]
            ))
        except Exception as e:
            raise Exception(f"Scan failed: {str(e)}")

class Query(ObjectType):
    health_status = Field(String)
    
    def resolve_health_status(self, info):
        status = manager.get_health_status()
        return status['overall_status']

class Mutation(ObjectType):
    execute_scan = ExecuteScan.Field()

schema = Schema(query=Query, mutation=Mutation)
```

## Workflow Orchestration

### Complex Security Assessment Workflow

```python
# Define a comprehensive security assessment workflow
security_assessment_workflow = {
    "name": "comprehensive_security_assessment",
    "description": "Full security assessment with multiple tools",
    "steps": [
        {
            "name": "network_discovery",
            "tool": "nmap",
            "target": "${target}",
            "parameters": {
                "scan_type": "discovery",
                "ports": "1-1000"
            },
            "timeout": 300
        },
        {
            "name": "port_scan",
            "tool": "nmap",
            "target": "${target}",
            "parameters": {
                "scan_type": "tcp",
                "ports": "1-65535"
            },
            "depends_on": ["network_discovery"],
            "condition": "network_discovery.findings_count > 0",
            "timeout": 600
        },
        {
            "name": "web_scan",
            "tool": "nikto",
            "target": "http://${target}",
            "depends_on": ["port_scan"],
            "condition": "port_scan.has_web_ports",
            "timeout": 900
        },
        {
            "name": "directory_enumeration",
            "tool": "gobuster",
            "target": "http://${target}",
            "parameters": {
                "wordlist": "/usr/share/wordlists/dirb/common.txt",
                "extensions": "php,html,txt,js"
            },
            "depends_on": ["web_scan"],
            "parallel": True,
            "timeout": 1200
        },
        {
            "name": "sql_injection_test",
            "tool": "sqlmap",
            "target": "http://${target}/login.php",
            "parameters": {
                "data": "username=admin&password=admin",
                "risk": 2,
                "level": 3
            },
            "depends_on": ["directory_enumeration"],
            "condition": "directory_enumeration.has_login_forms",
            "timeout": 1800
        }
    ],
    "reporting": {
        "template": "security_assessment",
        "formats": ["html", "json", "pdf"],
        "include_raw_output": False
    },
    "notifications": {
        "on_completion": ["email", "webhook"],
        "on_critical_findings": ["slack", "email"]
    }
}

# Execute the workflow
result = manager.workflow_engine.execute_workflow(security_assessment_workflow)

# Generate comprehensive report
report = manager.generate_workflow_report(result, 'html', 'security_assessment')
```

### Parallel Execution Example

```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

async def parallel_security_scan(targets):
    """Execute security scans on multiple targets in parallel."""
    
    async def scan_target(target):
        """Scan a single target."""
        try:
            # Network scan
            nmap_result = manager.execute_tool('nmap', target, scan_type='tcp')
            
            # Web scan (if web ports found)
            web_results = []
            if any(f.get('port') in [80, 443, 8080] for f in nmap_result.findings):
                nikto_result = manager.execute_tool('nikto', f'http://{target}')
                gobuster_result = manager.execute_tool('gobuster', f'http://{target}')
                web_results = [nikto_result, gobuster_result]
            
            return {
                'target': target,
                'network_scan': nmap_result,
                'web_scans': web_results,
                'total_findings': len(nmap_result.findings) + sum(len(r.findings) for r in web_results)
            }
            
        except Exception as e:
            return {
                'target': target,
                'error': str(e)
            }
    
    # Execute scans in parallel
    with ThreadPoolExecutor(max_workers=5) as executor:
        loop = asyncio.get_event_loop()
        tasks = [
            loop.run_in_executor(executor, lambda t=target: asyncio.run(scan_target(t)))
            for target in targets
        ]
        
        results = await asyncio.gather(*tasks)
    
    return results

# Usage
targets = ['example.com', 'test.com', 'demo.com']
results = asyncio.run(parallel_security_scan(targets))
```

## Security Integration

### Custom Security Policies

```python
from tool_connectors.security import SecurityPolicy, SecurityViolation

# Create custom security policy for high-security environment
high_security_policy = SecurityPolicy(
    max_input_length=500,
    allowed_characters=r'[a-zA-Z0-9\.\-_]',  # More restrictive
    blocked_patterns=[
        r'[;&|`$(){}[\]\\]',  # Shell metacharacters
        r'\.\./|\.\.\\',       # Directory traversal
        r'rm\s+',             # Any rm command
        r'sudo|su\s',         # Privilege escalation
        r'nc\s+',             # Netcat
        r'bash|sh\s+',        # Shell execution
    ],
    allowed_commands={'nmap', 'nikto'},  # Only specific tools
    command_timeout=120,  # Shorter timeout
    max_memory_mb=512,    # Lower memory limit
    max_cpu_percent=50,   # Lower CPU limit
    use_sandbox=True,
    restrict_network=True,
    restrict_filesystem=True,
    log_all_commands=True,
    monitor_resources=True,
    alert_on_violations=True
)

# Apply custom policy
manager = ToolManager(
    secure_mode=True,
    security_policy=high_security_policy
)

# Add custom security checks
def check_target_whitelist():
    """Custom security check for target whitelist."""
    # Implementation here
    return True

manager.add_health_check(
    "target_whitelist",
    check_target_whitelist,
    "Target is in approved whitelist",
    critical=True
)
```

### Security Event Handling

```python
import logging
from tool_connectors.security import SecurityViolation

# Set up security event logging
security_logger = logging.getLogger('security_events')
security_handler = logging.FileHandler('security_events.log')
security_handler.setFormatter(logging.Formatter(
    '%(asctime)s - %(levelname)s - %(message)s'
))
security_logger.addHandler(security_handler)

def handle_security_violation(violation: SecurityViolation):
    """Handle security violations with custom logic."""
    
    # Log the violation
    security_logger.error(f"Security violation: {violation.violation_type} - {violation}")
    
    # Send alert based on violation type
    if violation.violation_type in ['command_injection', 'unauthorized_command']:
        send_critical_alert(violation)
    elif violation.violation_type in ['input_length', 'invalid_characters']:
        send_warning_alert(violation)
    
    # Update security metrics
    manager.monitoring.metrics.increment_counter(
        'security_violations',
        1,
        {'type': violation.violation_type}
    )

def send_critical_alert(violation):
    """Send critical security alert."""
    # Implementation for critical alerts (email, Slack, etc.)
    pass

def send_warning_alert(violation):
    """Send warning security alert."""
    # Implementation for warning alerts
    pass

# Integrate with tool execution
def secure_execute_tool(tool, target, **kwargs):
    """Execute tool with enhanced security handling."""
    try:
        return manager.execute_tool(tool, target, **kwargs)
    except SecurityViolation as e:
        handle_security_violation(e)
        raise
```
