#!/usr/bin/env python3
"""
Basic Usage Example for Tool Connectors

This script demonstrates how to use the tool connector framework
to perform security scans using various tools.
"""

import asyncio
import json
import logging
import sys
from pathlib import Path

# Add the parent directory to the path so we can import tool_connectors
sys.path.insert(0, str(Path(__file__).parent.parent))

from tool_connectors import ToolManager, NmapConnector, NiktoConnector
from tool_connectors.base import ToolStatus


def setup_logging():
    """Set up logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def example_single_tool():
    """Example: Using a single tool connector"""
    print("=== Single Tool Example ===")
    
    # Create an Nmap connector
    nmap = NmapConnector()
    
    # Execute a basic scan
    target = "scanme.nmap.org"  # Safe target for testing
    print(f"Scanning {target} with Nmap...")
    
    result = nmap.execute(target, ports="80,443", timing=4)
    
    print(f"Status: {result.status.value}")
    print(f"Execution time: {result.execution_time:.2f} seconds")
    print(f"Findings: {len(result.findings)}")
    
    if result.findings:
        print("\nFindings:")
        for finding in result.findings[:3]:  # Show first 3 findings
            print(f"  - {finding.get('description', 'No description')}")
    
    return result


def example_tool_manager():
    """Example: Using the tool manager"""
    print("\n=== Tool Manager Example ===")
    
    # Create tool manager
    manager = ToolManager()
    
    # List available tools
    print(f"Available tools: {manager.get_available_tools()}")
    
    # Execute a single tool through the manager
    target = "scanme.nmap.org"
    print(f"Scanning {target} with Nmap through manager...")
    
    result = manager.execute_tool('nmap', target, ports="22,80,443")
    
    print(f"Status: {result.status.value}")
    print(f"Findings: {len(result.findings)}")
    
    return result


async def example_async_execution():
    """Example: Asynchronous tool execution"""
    print("\n=== Async Execution Example ===")
    
    manager = ToolManager()
    target = "scanme.nmap.org"
    
    # Execute multiple tools asynchronously
    tools = ['nmap']  # Start with just nmap for testing
    tool_configs = {
        'nmap': {
            'ports': '22,80,443',
            'timing': 4,
            'service_detection': True
        }
    }
    
    print(f"Running async scan on {target}...")
    results = await manager.execute_multiple_tools_async(tools, target, tool_configs)
    
    for tool_name, result in results.items():
        print(f"{tool_name}: {result.status.value} ({len(result.findings)} findings)")
    
    return results


def example_workflow():
    """Example: Using predefined workflows"""
    print("\n=== Workflow Example ===")
    
    manager = ToolManager()
    target = "scanme.nmap.org"
    
    # Execute a quick workflow
    print(f"Running quick workflow on {target}...")
    results = manager.execute_scan_workflow(target, workflow_type='quick')
    
    # Generate aggregated report
    aggregated = manager.aggregate_findings(results)
    
    print(f"Total findings: {aggregated['total_findings']}")
    print("Findings by severity:")
    for severity, count in aggregated['findings_by_severity'].items():
        if count > 0:
            print(f"  {severity.upper()}: {count}")
    
    return results, aggregated


def example_custom_configuration():
    """Example: Custom tool configuration"""
    print("\n=== Custom Configuration Example ===")
    
    # Create Nmap connector with custom settings
    nmap = NmapConnector(timeout=600)  # 10 minute timeout
    
    target = "scanme.nmap.org"
    
    # Custom scan configuration
    result = nmap.execute(
        target,
        scan_type='tcp_syn',
        ports='1-1000',
        service_detection=True,
        scripts=['banner', 'http-title'],
        timing=3,
        output_format='xml'
    )
    
    print(f"Custom scan completed: {result.status.value}")
    print(f"Command executed: {result.command}")
    
    return result


def example_error_handling():
    """Example: Error handling and validation"""
    print("\n=== Error Handling Example ===")
    
    manager = ToolManager()
    
    # Test with invalid target
    invalid_target = "invalid..target..name"
    
    try:
        result = manager.execute_tool('nmap', invalid_target)
        print(f"Result status: {result.status.value}")
        if result.error_message:
            print(f"Error: {result.error_message}")
    except Exception as e:
        print(f"Exception caught: {e}")
    
    # Test with non-existent tool
    try:
        result = manager.execute_tool('nonexistent_tool', 'example.com')
    except ValueError as e:
        print(f"Expected error: {e}")


def save_results_to_file(results, filename="scan_results.json"):
    """Save scan results to a JSON file"""
    print(f"\n=== Saving Results to {filename} ===")
    
    # Convert results to serializable format
    serializable_results = {}
    
    if isinstance(results, dict):
        for tool_name, result in results.items():
            serializable_results[tool_name] = {
                'tool_name': result.tool_name,
                'command': result.command,
                'status': result.status.value,
                'exit_code': result.exit_code,
                'execution_time': result.execution_time,
                'findings': result.findings,
                'error_message': result.error_message
            }
    
    with open(filename, 'w') as f:
        json.dump(serializable_results, f, indent=2)
    
    print(f"Results saved to {filename}")


def main():
    """Main function to run all examples"""
    setup_logging()
    
    print("Tool Connectors Framework - Basic Usage Examples")
    print("=" * 50)
    
    try:
        # Run synchronous examples
        single_result = example_single_tool()
        manager_result = example_tool_manager()
        
        # Run asynchronous example
        async_results = asyncio.run(example_async_execution())
        
        # Run workflow example
        workflow_results, aggregated = example_workflow()
        
        # Custom configuration
        custom_result = example_custom_configuration()
        
        # Error handling
        example_error_handling()
        
        # Save results
        if workflow_results:
            save_results_to_file(workflow_results)
        
        print("\n=== Summary ===")
        print("All examples completed successfully!")
        print("Check the generated scan_results.json file for detailed results.")
        
    except KeyboardInterrupt:
        print("\nExecution interrupted by user")
    except Exception as e:
        print(f"\nError during execution: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
