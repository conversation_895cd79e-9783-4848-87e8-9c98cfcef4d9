"""
Configuration management for tool connectors.

This module provides configuration management capabilities for the tool connector framework,
allowing users to specify tool paths, default parameters, and environment-specific settings.
"""

import json
import os
import yaml
from pathlib import Path
from typing import Any, Dict, Optional, Union
import logging


class ConfigurationError(Exception):
    """Exception raised for configuration-related errors."""
    pass


class ConfigManager:
    """
    Configuration manager for tool connectors.
    
    Supports loading configuration from JSON, YAML, and environment variables.
    Configuration is loaded in the following order (later sources override earlier ones):
    1. Default configuration
    2. System-wide configuration file
    3. User configuration file
    4. Project-specific configuration file
    5. Environment variables
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize configuration manager.
        
        Args:
            config_file: Optional path to specific configuration file
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self._config: Dict[str, Any] = {}
        self._config_sources: list = []
        
        # Load configuration
        self._load_default_config()
        self._load_config_files(config_file)
        self._load_environment_config()
    
    def _load_default_config(self):
        """Load default configuration."""
        default_config = {
            'tools': {
                'nmap': {
                    'path': 'nmap',
                    'timeout': 300,
                    'default_params': {
                        'scan_type': 'tcp_syn',
                        'service_detection': True,
                        'timing': 4
                    }
                },
                'nikto': {
                    'path': 'nikto',
                    'timeout': 600,
                    'default_params': {
                        'ssl': False,
                        'plugins': '@@ALL',
                        'timeout': 30
                    }
                },
                'sqlmap': {
                    'path': 'sqlmap',
                    'timeout': 1800,
                    'default_params': {
                        'level': 1,
                        'risk': 1,
                        'threads': 1
                    }
                },
                'gobuster': {
                    'path': 'gobuster',
                    'timeout': 300,
                    'default_params': {
                        'mode': 'dir',
                        'threads': 10,
                        'wordlist': '/usr/share/wordlists/dirb/common.txt'
                    }
                },
                'openvas': {
                    'path': 'gvm-cli',
                    'timeout': 3600,
                    'default_params': {
                        'host': 'localhost',
                        'port': 9390
                    }
                }
            },
            'general': {
                'mock_mode': False,
                'max_concurrent_tools': 5,
                'output_directory': './scan_results',
                'log_level': 'INFO'
            },
            'workflows': {
                'quick': ['nmap', 'nikto'],
                'comprehensive': ['nmap', 'nikto', 'gobuster', 'sqlmap'],
                'web': ['nikto', 'gobuster', 'sqlmap'],
                'network': ['nmap']
            }
        }
        
        self._config = default_config
        self._config_sources.append('default')
    
    def _load_config_files(self, config_file: Optional[str] = None):
        """Load configuration from files."""
        config_files = []
        
        if config_file:
            # Specific config file provided
            config_files.append(config_file)
        else:
            # Standard config file locations
            config_files.extend([
                '/etc/tool-connectors/config.yaml',  # System-wide
                '/etc/tool-connectors/config.json',
                os.path.expanduser('~/.tool-connectors/config.yaml'),  # User
                os.path.expanduser('~/.tool-connectors/config.json'),
                './tool-connectors.yaml',  # Project-specific
                './tool-connectors.json',
                './.tool-connectors.yaml',
                './.tool-connectors.json'
            ])
        
        for config_path in config_files:
            if os.path.exists(config_path):
                try:
                    loaded_config = self._load_config_file(config_path)
                    self._merge_config(loaded_config)
                    self._config_sources.append(config_path)
                    self.logger.info(f"Loaded configuration from {config_path}")
                except Exception as e:
                    self.logger.warning(f"Failed to load config from {config_path}: {e}")
    
    def _load_config_file(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from a specific file."""
        path = Path(config_path)
        
        with open(path, 'r', encoding='utf-8') as f:
            if path.suffix.lower() in ['.yaml', '.yml']:
                return yaml.safe_load(f) or {}
            elif path.suffix.lower() == '.json':
                return json.load(f)
            else:
                raise ConfigurationError(f"Unsupported config file format: {path.suffix}")
    
    def _load_environment_config(self):
        """Load configuration from environment variables."""
        env_config = {}
        
        # Tool-specific environment variables
        for tool_name in self._config.get('tools', {}):
            tool_env_prefix = f"TOOLCONNECTOR_{tool_name.upper()}_"
            
            # Tool path
            path_var = f"{tool_env_prefix}PATH"
            if path_var in os.environ:
                if 'tools' not in env_config:
                    env_config['tools'] = {}
                if tool_name not in env_config['tools']:
                    env_config['tools'][tool_name] = {}
                env_config['tools'][tool_name]['path'] = os.environ[path_var]
            
            # Tool timeout
            timeout_var = f"{tool_env_prefix}TIMEOUT"
            if timeout_var in os.environ:
                if 'tools' not in env_config:
                    env_config['tools'] = {}
                if tool_name not in env_config['tools']:
                    env_config['tools'][tool_name] = {}
                try:
                    env_config['tools'][tool_name]['timeout'] = int(os.environ[timeout_var])
                except ValueError:
                    self.logger.warning(f"Invalid timeout value in {timeout_var}")
        
        # General environment variables
        general_vars = {
            'TOOLCONNECTOR_MOCK_MODE': ('mock_mode', lambda x: x.lower() in ['true', '1', 'yes']),
            'TOOLCONNECTOR_MAX_CONCURRENT': ('max_concurrent_tools', int),
            'TOOLCONNECTOR_OUTPUT_DIR': ('output_directory', str),
            'TOOLCONNECTOR_LOG_LEVEL': ('log_level', str)
        }
        
        for env_var, (config_key, converter) in general_vars.items():
            if env_var in os.environ:
                if 'general' not in env_config:
                    env_config['general'] = {}
                try:
                    env_config['general'][config_key] = converter(os.environ[env_var])
                except (ValueError, TypeError) as e:
                    self.logger.warning(f"Invalid value for {env_var}: {e}")
        
        if env_config:
            self._merge_config(env_config)
            self._config_sources.append('environment')
    
    def _merge_config(self, new_config: Dict[str, Any]):
        """Merge new configuration into existing configuration."""
        def deep_merge(base: Dict[str, Any], update: Dict[str, Any]):
            for key, value in update.items():
                if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                    deep_merge(base[key], value)
                else:
                    base[key] = value
        
        deep_merge(self._config, new_config)
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation.
        
        Args:
            key: Configuration key (e.g., 'tools.nmap.path')
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """
        Set configuration value using dot notation.
        
        Args:
            key: Configuration key (e.g., 'tools.nmap.path')
            value: Value to set
        """
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def get_tool_config(self, tool_name: str) -> Dict[str, Any]:
        """
        Get configuration for a specific tool.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Tool configuration dictionary
        """
        return self.get(f'tools.{tool_name}', {})
    
    def get_tool_path(self, tool_name: str) -> str:
        """
        Get path for a specific tool.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Tool path
        """
        return self.get(f'tools.{tool_name}.path', tool_name)
    
    def get_tool_timeout(self, tool_name: str) -> int:
        """
        Get timeout for a specific tool.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Tool timeout in seconds
        """
        return self.get(f'tools.{tool_name}.timeout', 300)
    
    def get_tool_defaults(self, tool_name: str) -> Dict[str, Any]:
        """
        Get default parameters for a specific tool.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Default parameters dictionary
        """
        return self.get(f'tools.{tool_name}.default_params', {})
    
    def get_workflow_tools(self, workflow_name: str) -> list:
        """
        Get tools for a specific workflow.
        
        Args:
            workflow_name: Name of the workflow
            
        Returns:
            List of tool names
        """
        return self.get(f'workflows.{workflow_name}', [])
    
    def is_mock_mode(self) -> bool:
        """Check if mock mode is enabled."""
        return self.get('general.mock_mode', False)
    
    def get_output_directory(self) -> str:
        """Get output directory path."""
        return self.get('general.output_directory', './scan_results')
    
    def get_max_concurrent_tools(self) -> int:
        """Get maximum number of concurrent tools."""
        return self.get('general.max_concurrent_tools', 5)
    
    def save_config(self, config_path: str, format: str = 'yaml'):
        """
        Save current configuration to file.
        
        Args:
            config_path: Path to save configuration
            format: Format to save ('yaml' or 'json')
        """
        path = Path(config_path)
        path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(path, 'w', encoding='utf-8') as f:
            if format.lower() == 'yaml':
                yaml.dump(self._config, f, default_flow_style=False, indent=2)
            elif format.lower() == 'json':
                json.dump(self._config, f, indent=2)
            else:
                raise ConfigurationError(f"Unsupported format: {format}")
    
    def get_config_sources(self) -> list:
        """Get list of configuration sources that were loaded."""
        return self._config_sources.copy()
    
    def get_full_config(self) -> Dict[str, Any]:
        """Get the full configuration dictionary."""
        return self._config.copy()


# Global configuration instance
_config_manager: Optional[ConfigManager] = None


def get_config_manager(config_file: Optional[str] = None) -> ConfigManager:
    """
    Get the global configuration manager instance.
    
    Args:
        config_file: Optional path to specific configuration file
        
    Returns:
        ConfigManager instance
    """
    global _config_manager
    
    if _config_manager is None or config_file is not None:
        _config_manager = ConfigManager(config_file)
    
    return _config_manager


def get_config(key: str, default: Any = None) -> Any:
    """
    Get configuration value using dot notation.
    
    Args:
        key: Configuration key (e.g., 'tools.nmap.path')
        default: Default value if key not found
        
    Returns:
        Configuration value
    """
    return get_config_manager().get(key, default)


def get_tool_config(tool_name: str) -> Dict[str, Any]:
    """
    Get configuration for a specific tool.
    
    Args:
        tool_name: Name of the tool
        
    Returns:
        Tool configuration dictionary
    """
    return get_config_manager().get_tool_config(tool_name)
