# Tool Connectors Framework Requirements
# 
# Core dependencies for the tool connector framework
# All dependencies are free and open-source to maintain $0 API cost

# No external API dependencies required
# The framework uses only standard library modules and free tools

# Optional dependencies for enhanced functionality:

# For XML parsing (included in Python standard library)
# xml.etree.ElementTree

# For async operations (included in Python standard library)
# asyncio

# For networking utilities (included in Python standard library)
# socket
# ipaddress
# urllib.parse

# For testing (optional)
pytest>=6.0.0
pytest-cov>=2.0.0
pytest-asyncio>=0.18.0

# For development (optional)
black>=22.0.0
flake8>=4.0.0
mypy>=0.950

# Note: The actual security tools need to be installed separately:
# - nmap: sudo apt install nmap
# - nikto: sudo apt install nikto  
# - gobuster: sudo apt install gobuster
# - sqlmap: pip install sqlmap
# - openvas: sudo apt install openvas (requires additional setup)
