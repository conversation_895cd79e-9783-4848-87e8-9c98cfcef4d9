"""
Utility functions for tool connectors.

This module provides common utility functions used across different
tool connectors for validation, parsing, and data processing.
"""

import ipaddress
import re
import socket
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse


def validate_ip_address(ip: str) -> bool:
    """
    Validate if a string is a valid IP address.
    
    Args:
        ip: IP address string to validate
        
    Returns:
        True if valid IP address, False otherwise
    """
    try:
        ipaddress.ip_address(ip)
        return True
    except ValueError:
        return False


def validate_ip_network(network: str) -> bool:
    """
    Validate if a string is a valid IP network (CIDR notation).
    
    Args:
        network: Network string to validate (e.g., "***********/24")
        
    Returns:
        True if valid network, False otherwise
    """
    try:
        ipaddress.ip_network(network, strict=False)
        return True
    except ValueError:
        return False


def validate_hostname(hostname: str) -> bool:
    """
    Validate if a string is a valid hostname.
    
    Args:
        hostname: Hostname string to validate
        
    Returns:
        True if valid hostname, False otherwise
    """
    if not hostname or len(hostname) > 253:
        return False
    
    # Remove trailing dot if present
    if hostname.endswith('.'):
        hostname = hostname[:-1]
    
    # Check each label
    labels = hostname.split('.')
    for label in labels:
        if not label or len(label) > 63:
            return False
        if not re.match(r'^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?$', label):
            return False
    
    return True


def validate_url(url: str) -> bool:
    """
    Validate if a string is a valid URL.

    Args:
        url: URL string to validate

    Returns:
        True if valid URL, False otherwise
    """
    try:
        parsed = urlparse(url)
        # Only accept http and https schemes for security tools
        return bool(parsed.scheme in ['http', 'https'] and parsed.netloc)
    except Exception:
        return False


def validate_port(port: Union[str, int]) -> bool:
    """
    Validate if a value is a valid port number.
    
    Args:
        port: Port number to validate
        
    Returns:
        True if valid port, False otherwise
    """
    try:
        port_num = int(port)
        return 1 <= port_num <= 65535
    except (ValueError, TypeError):
        return False


def parse_port_range(port_range: str) -> List[int]:
    """
    Parse a port range string into a list of port numbers.

    Args:
        port_range: Port range string (e.g., "80,443,8080-8090")

    Returns:
        List of port numbers
    """
    ports = []

    for part in port_range.split(','):
        part = part.strip()

        if '-' in part:
            # Range
            try:
                start, end = part.split('-', 1)
                start_port = int(start.strip())
                end_port = int(end.strip())

                if validate_port(start_port) and validate_port(end_port) and start_port <= end_port:
                    ports.extend(range(start_port, end_port + 1))
                elif validate_port(start_port):
                    # If end port is invalid, just add the start port
                    ports.append(start_port)
            except ValueError:
                continue
        else:
            # Single port
            try:
                port = int(part)
                if validate_port(port):
                    ports.append(port)
            except ValueError:
                continue

    return sorted(list(set(ports)))


def normalize_target(target: str) -> Dict[str, Any]:
    """
    Normalize a target string into structured information.
    
    Args:
        target: Target string (IP, hostname, URL, etc.)
        
    Returns:
        Dictionary with normalized target information
    """
    result = {
        'original': target,
        'type': 'unknown',
        'host': '',
        'port': None,
        'scheme': '',
        'path': '',
        'is_valid': False
    }
    
    # Try URL first
    if target.startswith(('http://', 'https://')):
        try:
            parsed = urlparse(target)
            result.update({
                'type': 'url',
                'host': parsed.hostname or '',
                'port': parsed.port,
                'scheme': parsed.scheme,
                'path': parsed.path,
                'is_valid': bool(parsed.hostname)
            })
            return result
        except Exception:
            pass
    
    # Try IP address
    if validate_ip_address(target):
        result.update({
            'type': 'ip',
            'host': target,
            'is_valid': True
        })
        return result
    
    # Try IP network
    if validate_ip_network(target):
        result.update({
            'type': 'network',
            'host': target,
            'is_valid': True
        })
        return result
    
    # Try hostname
    if validate_hostname(target):
        result.update({
            'type': 'hostname',
            'host': target,
            'is_valid': True
        })
        return result
    
    return result


def extract_ips_from_text(text: str) -> List[str]:
    """
    Extract IP addresses from text.
    
    Args:
        text: Text to search for IP addresses
        
    Returns:
        List of found IP addresses
    """
    ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
    potential_ips = re.findall(ip_pattern, text)
    
    valid_ips = []
    for ip in potential_ips:
        if validate_ip_address(ip):
            valid_ips.append(ip)
    
    return list(set(valid_ips))


def extract_urls_from_text(text: str) -> List[str]:
    """
    Extract URLs from text.
    
    Args:
        text: Text to search for URLs
        
    Returns:
        List of found URLs
    """
    url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
    potential_urls = re.findall(url_pattern, text)
    
    valid_urls = []
    for url in potential_urls:
        if validate_url(url):
            valid_urls.append(url)
    
    return list(set(valid_urls))


def sanitize_filename(filename: str) -> str:
    """
    Sanitize a filename by removing/replacing invalid characters.
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    # Remove or replace invalid characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    # Remove leading/trailing whitespace and dots
    filename = filename.strip(' .')
    
    # Ensure it's not empty
    if not filename:
        filename = 'unnamed'
    
    return filename


def format_bytes(bytes_value: int) -> str:
    """
    Format bytes into human-readable string.
    
    Args:
        bytes_value: Number of bytes
        
    Returns:
        Formatted string (e.g., "1.5 KB", "2.3 MB")
    """
    if bytes_value == 0:
        return "0 B"
    
    units = ['B', 'KB', 'MB', 'GB', 'TB']
    unit_index = 0
    size = float(bytes_value)
    
    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1
    
    return f"{size:.1f} {units[unit_index]}"


def format_duration(seconds: float) -> str:
    """
    Format duration in seconds into human-readable string.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted string (e.g., "1m 30s", "2h 15m")
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    
    minutes = int(seconds // 60)
    remaining_seconds = seconds % 60
    
    if minutes < 60:
        return f"{minutes}m {remaining_seconds:.0f}s"
    
    hours = minutes // 60
    remaining_minutes = minutes % 60
    
    return f"{hours}h {remaining_minutes}m"


def merge_findings(findings_list: List[List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
    """
    Merge findings from multiple sources, removing duplicates.
    
    Args:
        findings_list: List of findings lists from different tools
        
    Returns:
        Merged and deduplicated findings list
    """
    merged = []
    seen_findings = set()
    
    for findings in findings_list:
        for finding in findings:
            # Create a simple hash for deduplication
            finding_key = (
                finding.get('type', ''),
                finding.get('host', ''),
                finding.get('port', ''),
                finding.get('description', '')[:100]  # First 100 chars
            )
            
            if finding_key not in seen_findings:
                seen_findings.add(finding_key)
                merged.append(finding)
    
    return merged


def resolve_hostname(hostname: str) -> Optional[str]:
    """
    Resolve hostname to IP address.
    
    Args:
        hostname: Hostname to resolve
        
    Returns:
        IP address string or None if resolution fails
    """
    try:
        return socket.gethostbyname(hostname)
    except socket.gaierror:
        return None


def reverse_dns_lookup(ip: str) -> Optional[str]:
    """
    Perform reverse DNS lookup for an IP address.
    
    Args:
        ip: IP address to lookup
        
    Returns:
        Hostname string or None if lookup fails
    """
    try:
        return socket.gethostbyaddr(ip)[0]
    except socket.herror:
        return None
