#!/usr/bin/env python3
"""
Advanced Reporting and Visualization Demo

This script demonstrates the advanced reporting capabilities of the tool 
connector framework. It shows how to:
1. Generate reports in multiple formats (HTML, JSON, XML, CSV)
2. Use report templates for consistent formatting
3. Create custom report sections and metadata
4. Integrate reporting with tool execution
5. Generate workflow reports
"""

import sys
import time
from pathlib import Path

# Add the parent directory to the path so we can import tool_connectors
sys.path.insert(0, str(Path(__file__).parent.parent))

from tool_connectors import ToolManager
from tool_connectors.reporting import (
    ReportGenerator, ReportTemplate, ReportSection, ReportMetadata,
    get_report_generator
)
from tool_connectors.base import ToolResult, ToolStatus


def demo_basic_reporting():
    """Demonstrate basic report generation"""
    print("=== Basic Reporting Demo ===")
    
    # Create ToolManager and execute some tools
    manager = ToolManager(mock_mode=True)
    
    print("Executing tools to generate sample data...")
    
    # Execute various tools
    results = []
    tools_and_targets = [
        ('nmap', 'demo.example.com'),
        ('nikto', 'demo.example.com'),
        ('sqlmap', 'demo.example.com'),
        ('gobuster', 'demo.example.com')
    ]
    
    for tool, target in tools_and_targets:
        result = manager.execute_tool(tool, target)
        results.append(result)
        print(f"  Executed {tool} on {target}: {result.status.value}")
    
    print(f"Generated {len(results)} tool results")
    
    # Generate reports in different formats
    formats = ['html', 'json', 'xml', 'csv']

    print("\nGenerating reports in multiple formats:")
    for fmt in formats:
        report_content = manager.generate_advanced_report(results, fmt)
        file_path = f"./demo_report.{fmt}"

        saved_path = manager.save_advanced_report(results, file_path, fmt)
        print(f"  {fmt.upper()} report saved to: {saved_path}")
    
    print()


def demo_report_templates():
    """Demonstrate report templates"""
    print("=== Report Templates Demo ===")
    
    manager = ToolManager(mock_mode=True)
    
    # Show available templates
    templates = manager.get_available_report_templates()
    print(f"Available templates: {', '.join(templates)}")
    
    # Execute some tools
    results = [
        manager.execute_tool('nmap', 'template-demo.com'),
        manager.execute_tool('nikto', 'template-demo.com')
    ]
    
    print(f"\nGenerated {len(results)} tool results")
    
    # Generate reports using different templates
    for template_name in templates:
        print(f"\nGenerating report with '{template_name}' template:")
        
        report_content = manager.generate_advanced_report(
            results,
            'html',
            template_name=template_name
        )

        file_path = f"./template_report_{template_name}.html"
        saved_path = manager.save_advanced_report(results, file_path, 'html', template_name)
        print(f"  Report saved to: {saved_path}")
    
    print()


def demo_custom_templates():
    """Demonstrate creating custom templates"""
    print("=== Custom Templates Demo ===")
    
    manager = ToolManager(mock_mode=True)
    
    # Create a custom template
    custom_template = ReportTemplate(
        "custom_security_audit",
        "Custom security audit report template"
    )
    
    # Set custom metadata
    custom_template.set_metadata_template(
        title="Custom Security Audit Report",
        description="Comprehensive security audit with custom analysis",
        tags=["security", "audit", "custom", "comprehensive"],
        custom_fields={
            "audit_type": "comprehensive",
            "compliance_framework": "NIST",
            "risk_tolerance": "low"
        }
    )
    
    # Add custom sections
    custom_template.add_section(ReportSection(
        title="Audit Scope and Objectives",
        content="This security audit was conducted to assess the overall security posture and identify potential vulnerabilities in the target systems."
    ))
    
    custom_template.add_section(ReportSection(
        title="Compliance Assessment",
        content="The audit includes assessment against NIST Cybersecurity Framework guidelines and industry best practices."
    ))
    
    custom_template.add_section(ReportSection(
        title="Risk Analysis Methodology",
        content="Risk assessment follows a structured approach considering likelihood, impact, and existing controls.",
        data={
            "Risk Levels": "Critical, High, Medium, Low, Informational",
            "Assessment Criteria": "CVSS v3.1 scoring system",
            "Remediation Priority": "Based on risk score and business impact"
        }
    ))
    
    # Add the template to the manager
    manager.add_report_template(custom_template)
    
    print("Created custom template: 'custom_security_audit'")
    
    # Execute tools
    results = [
        manager.execute_tool('nmap', 'audit-target.com'),
        manager.execute_tool('nikto', 'audit-target.com'),
        manager.execute_tool('sqlmap', 'audit-target.com')
    ]
    
    # Generate report using custom template
    report_content = manager.generate_advanced_report(
        results,
        'html',
        template_name='custom_security_audit'
    )

    saved_path = manager.save_advanced_report(
        results,
        './custom_audit_report.html',
        'html',
        'custom_security_audit'
    )
    
    print(f"Custom template report saved to: {saved_path}")
    print()


def demo_custom_sections():
    """Demonstrate custom report sections"""
    print("=== Custom Report Sections Demo ===")
    
    manager = ToolManager(mock_mode=True)
    
    # Execute tools
    results = [
        manager.execute_tool('nmap', 'sections-demo.com'),
        manager.execute_tool('gobuster', 'sections-demo.com')
    ]
    
    # Create custom sections
    custom_sections = []
    
    # Executive summary section
    exec_summary = manager.create_report_section(
        "Executive Summary",
        "This assessment identified several security concerns that require immediate attention. "
        "The target system shows signs of outdated software and potential misconfigurations.",
        data={
            "Assessment Date": "2024-01-15",
            "Target Systems": "sections-demo.com",
            "Tools Used": "nmap, gobuster",
            "Overall Risk Rating": "MEDIUM",
            "Critical Issues": 0,
            "High Issues": 1,
            "Medium Issues": 3,
            "Low Issues": 5
        }
    )
    custom_sections.append(exec_summary)
    
    # Technical details section
    tech_details = manager.create_report_section(
        "Technical Analysis",
        "Detailed technical analysis of discovered services and potential vulnerabilities.",
        data={
            "Open Ports": "22, 80, 443",
            "Web Server": "Apache 2.4.41",
            "SSL/TLS": "TLS 1.2, 1.3 supported",
            "Directory Enumeration": "Multiple directories discovered",
            "Security Headers": "Partially implemented"
        }
    )
    custom_sections.append(tech_details)
    
    # Recommendations section
    recommendations = manager.create_report_section(
        "Recommendations",
        "Based on the assessment findings, the following recommendations should be implemented:",
        data={
            "Immediate Actions": "Update web server software, implement security headers",
            "Short-term (1-3 months)": "Conduct penetration testing, implement WAF",
            "Long-term (3-6 months)": "Security awareness training, regular vulnerability assessments",
            "Monitoring": "Implement continuous security monitoring and logging"
        }
    )
    custom_sections.append(recommendations)
    
    print(f"Created {len(custom_sections)} custom sections")
    
    # Generate report with custom sections
    metadata_overrides = {
        'title': 'Security Assessment with Custom Analysis',
        'description': 'Comprehensive security assessment with custom analysis sections',
        'tags': ['security', 'assessment', 'custom-analysis'],
        'custom_fields': {
            'assessment_type': 'external',
            'methodology': 'OWASP Testing Guide',
            'duration': '4 hours'
        }
    }
    
    report_content = manager.generate_advanced_report(
        results,
        'html',
        metadata_overrides=metadata_overrides,
        custom_sections=custom_sections
    )

    saved_path = manager.save_advanced_report(
        results,
        './custom_sections_report.html',
        'html',
        metadata_overrides=metadata_overrides,
        custom_sections=custom_sections
    )
    
    print(f"Custom sections report saved to: {saved_path}")
    print()


def demo_workflow_reporting():
    """Demonstrate workflow reporting"""
    print("=== Workflow Reporting Demo ===")
    
    manager = ToolManager(mock_mode=True)
    
    # Create and execute a workflow
    workflow_config = {
        "name": "web_security_scan",
        "description": "Comprehensive web application security scan",
        "steps": [
            {
                "name": "port_scan",
                "tool": "nmap",
                "target": "workflow-demo.com",
                "parameters": {"scan_type": "tcp"}
            },
            {
                "name": "web_scan",
                "tool": "nikto",
                "target": "workflow-demo.com",
                "depends_on": ["port_scan"]
            },
            {
                "name": "directory_scan",
                "tool": "gobuster",
                "target": "workflow-demo.com",
                "depends_on": ["port_scan"]
            },
            {
                "name": "sql_injection_test",
                "tool": "sqlmap",
                "target": "workflow-demo.com",
                "depends_on": ["web_scan"],
                "parameters": {"url": "http://workflow-demo.com/login"}
            }
        ]
    }
    
    print("Executing workflow: 'web_security_scan'")
    workflow_result = manager.workflow_engine.execute_workflow(workflow_config)
    
    print(f"Workflow completed with status: {workflow_result.status.value}")
    print(f"Total steps: {len(workflow_result.step_results)}")
    print(f"Failed steps: {len(workflow_result.failed_steps)}")
    print(f"Total findings: {workflow_result.total_findings}")
    
    # Generate workflow report
    workflow_report = manager.generate_workflow_report(
        workflow_result,
        'html',
        template_name='security_assessment'
    )
    
    # Save workflow report
    with open('./workflow_report.html', 'w', encoding='utf-8') as f:
        f.write(workflow_report)
    
    print("Workflow report saved to: ./workflow_report.html")
    print()


def demo_report_analysis():
    """Demonstrate report analysis features"""
    print("=== Report Analysis Demo ===")
    
    manager = ToolManager(mock_mode=True)
    
    # Create results with different severities for analysis
    print("Creating diverse results for analysis...")
    
    # Simulate different types of findings
    results = []
    
    # High severity findings
    nmap_result = manager.execute_tool('nmap', 'analysis-demo.com')
    # Manually add high severity findings for demo
    nmap_result.findings.extend([
        {"type": "service", "port": 22, "service": "ssh", "severity": "info"},
        {"type": "service", "port": 80, "service": "http", "severity": "info"},
        {"type": "vulnerability", "description": "Outdated SSH version", "severity": "medium"}
    ])
    results.append(nmap_result)
    
    # Web vulnerabilities
    nikto_result = manager.execute_tool('nikto', 'analysis-demo.com')
    nikto_result.findings.extend([
        {"type": "vulnerability", "description": "Missing security headers", "severity": "low"},
        {"type": "vulnerability", "description": "Directory traversal possible", "severity": "high"},
        {"type": "information", "description": "Server version disclosed", "severity": "info"}
    ])
    results.append(nikto_result)
    
    # SQL injection findings
    sqlmap_result = manager.execute_tool('sqlmap', 'analysis-demo.com')
    sqlmap_result.findings.extend([
        {"type": "vulnerability", "description": "SQL injection in login form", "severity": "critical"},
        {"type": "vulnerability", "description": "Database information disclosure", "severity": "high"}
    ])
    results.append(sqlmap_result)
    
    print(f"Created {len(results)} results with various findings")
    
    # Generate comprehensive analysis report
    report_content = manager.generate_advanced_report(
        results,
        'json',
        template_name='security_assessment',
        metadata_overrides={
            'title': 'Comprehensive Security Analysis Report',
            'description': 'Detailed analysis of security findings with risk assessment',
            'tags': ['analysis', 'security', 'risk-assessment']
        }
    )
    
    # Parse and display analysis
    import json
    report_data = json.loads(report_content)
    
    print("\nReport Analysis Summary:")
    summary = report_data['summary']
    print(f"  Total tools executed: {summary['total_tools_executed']}")
    print(f"  Total findings: {summary['total_findings']}")
    print(f"  Average execution time: {summary['execution_time_average']:.2f}s")
    
    print("\n  Severity distribution:")
    for severity, count in summary['severity_distribution'].items():
        print(f"    {severity.upper()}: {count}")
    
    print("\n  Tool distribution:")
    for tool, count in summary['tool_distribution'].items():
        print(f"    {tool}: {count}")
    
    # Find risk assessment section
    risk_section = None
    for section in report_data['sections']:
        if section['title'] == 'Risk Assessment':
            risk_section = section
            break
    
    if risk_section and risk_section['data']:
        print(f"\n  Overall risk level: {risk_section['data']['overall_risk']}")
        print(f"  Risk score: {risk_section['data']['risk_score']}")
        print(f"  Critical findings: {risk_section['data']['critical_findings']}")
        print(f"  High findings: {risk_section['data']['high_findings']}")
    
    # Save analysis report
    saved_path = manager.save_advanced_report(
        results,
        './analysis_report.html',
        'html',
        'security_assessment',
        metadata_overrides={
            'title': 'Comprehensive Security Analysis Report',
            'description': 'Detailed analysis of security findings with risk assessment'
        }
    )
    
    print(f"\nAnalysis report saved to: {saved_path}")
    print()


def main():
    """Main function to run all reporting demos"""
    print("Tool Connectors Framework - Advanced Reporting Demo")
    print("=" * 70)
    print("This demo shows advanced reporting capabilities including:")
    print("- Multiple output formats (HTML, JSON, XML, CSV)")
    print("- Report templates for consistent formatting")
    print("- Custom sections and metadata")
    print("- Workflow reporting")
    print("- Automated analysis and risk assessment")
    print()
    
    try:
        demo_basic_reporting()
        demo_report_templates()
        demo_custom_templates()
        demo_custom_sections()
        demo_workflow_reporting()
        demo_report_analysis()
        
        print("=== Reporting Demo Summary ===")
        print("All reporting demos completed successfully!")
        print("The reporting system provides:")
        print("- Multiple output formats with consistent data")
        print("- Flexible template system for different report types")
        print("- Custom sections and metadata support")
        print("- Automated analysis and risk assessment")
        print("- Workflow-specific reporting capabilities")
        print("- Professional HTML reports with styling")
        print("- Machine-readable formats (JSON, XML, CSV)")
        
        print("\nGenerated files:")
        print("- ./demo_report.* - Basic reports in all formats")
        print("- ./template_report_*.html - Template-based reports")
        print("- ./custom_audit_report.html - Custom template report")
        print("- ./custom_sections_report.html - Custom sections report")
        print("- ./workflow_report.html - Workflow execution report")
        print("- ./analysis_report.html - Comprehensive analysis report")
        
    except Exception as e:
        print(f"Error during demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
