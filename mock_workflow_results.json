{"nmap": {"tool_name": "nmap", "command": "nmap -sS -p 1-1000 -sV -T5 -oX - workflow-test.com", "status": "completed", "exit_code": 0, "execution_time": 1.0155344009399414, "findings": [], "mock_mode": true}, "nikto": {"tool_name": "nikto", "command": "nikto -h workflow-test.com -timeout 10 -nointeractive", "status": "completed", "exit_code": 0, "execution_time": 1.0028936862945557, "findings": [], "mock_mode": true}}