"""
Unit tests for NmapConnector.
"""

import unittest
from unittest.mock import patch, Mock
import xml.etree.ElementTree as ET

from tool_connectors.nmap_connector import NmapConnector
from tool_connectors.base import Tool<PERSON>tatus, Severity


class TestNmapConnector(unittest.TestCase):
    """Test cases for NmapConnector"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.connector = NmapConnector(mock_mode=True)
    
    def test_initialization(self):
        """Test connector initialization"""
        self.assertEqual(self.connector.tool_name, "nmap")
        self.assertEqual(self.connector.default_command, "nmap")
        self.assertTrue(self.connector.mock_mode)
    
    def test_build_command_basic(self):
        """Test basic command building"""
        cmd = self.connector.build_command("example.com")
        
        self.assertIn("nmap", cmd[0])
        self.assertIn("example.com", cmd)
        self.assertIn("-sS", cmd)  # Default TCP SYN scan
        self.assertIn("-sV", cmd)  # Service detection enabled by default
    
    def test_build_command_with_ports(self):
        """Test command building with port specification"""
        cmd = self.connector.build_command("example.com", ports="80,443")
        
        self.assertIn("-p", cmd)
        port_index = cmd.index("-p")
        self.assertEqual(cmd[port_index + 1], "80,443")
    
    def test_build_command_scan_types(self):
        """Test different scan types"""
        # TCP SYN scan
        cmd = self.connector.build_command("example.com", scan_type="tcp_syn")
        self.assertIn("-sS", cmd)
        
        # TCP connect scan
        cmd = self.connector.build_command("example.com", scan_type="tcp_connect")
        self.assertIn("-sT", cmd)
        
        # UDP scan
        cmd = self.connector.build_command("example.com", scan_type="udp")
        self.assertIn("-sU", cmd)
    
    def test_build_command_timing(self):
        """Test timing template options"""
        cmd = self.connector.build_command("example.com", timing=5)
        self.assertIn("-T5", cmd)
    
    def test_build_command_scripts(self):
        """Test NSE script options"""
        # Single script
        cmd = self.connector.build_command("example.com", scripts="vuln")
        self.assertIn("--script", cmd)
        script_index = cmd.index("--script")
        self.assertEqual(cmd[script_index + 1], "vuln")
        
        # Multiple scripts
        cmd = self.connector.build_command("example.com", scripts=["vuln", "safe"])
        script_index = cmd.index("--script")
        self.assertEqual(cmd[script_index + 1], "vuln,safe")
    
    def test_build_command_advanced_options(self):
        """Test advanced command options"""
        cmd = self.connector.build_command(
            "example.com",
            aggressive=True,
            os_detection=True,
            stealth=True,
            no_ping=True,
            no_dns=True
        )
        
        self.assertIn("-A", cmd)  # Aggressive
        self.assertIn("-O", cmd)  # OS detection
        self.assertIn("-f", cmd)  # Stealth fragmentation
        self.assertIn("-Pn", cmd)  # No ping
        self.assertIn("-n", cmd)  # No DNS
    
    def test_validate_target(self):
        """Test target validation"""
        # Valid targets
        self.assertTrue(self.connector.validate_target("***********"))
        self.assertTrue(self.connector.validate_target("example.com"))
        self.assertTrue(self.connector.validate_target("sub.example.com"))
        
        # Invalid targets
        self.assertFalse(self.connector.validate_target(""))
        self.assertFalse(self.connector.validate_target(None))
    
    def test_parse_xml_output(self):
        """Test XML output parsing"""
        xml_output = '''<?xml version="1.0" encoding="UTF-8"?>
        <nmaprun>
            <host>
                <status state="up" reason="echo-reply"/>
                <address addr="***********" addrtype="ipv4"/>
                <hostnames>
                    <hostname name="router.local" type="PTR"/>
                </hostnames>
                <ports>
                    <port protocol="tcp" portid="80">
                        <state state="open" reason="syn-ack"/>
                        <service name="http" product="Apache" version="2.4"/>
                    </port>
                    <port protocol="tcp" portid="443">
                        <state state="open" reason="syn-ack"/>
                        <service name="https" product="Apache" version="2.4"/>
                    </port>
                </ports>
            </host>
        </nmaprun>'''
        
        parsed = self.connector._parse_xml_output(xml_output)
        
        self.assertIn('hosts', parsed)
        self.assertEqual(len(parsed['hosts']), 1)
        
        host = parsed['hosts'][0]
        self.assertEqual(host['status']['state'], 'up')
        self.assertEqual(len(host['addresses']), 1)
        self.assertEqual(host['addresses'][0]['addr'], '***********')
        self.assertEqual(len(host['ports']), 2)
        
        # Check port details
        port_80 = next(p for p in host['ports'] if p['portid'] == '80')
        self.assertEqual(port_80['state']['state'], 'open')
        self.assertEqual(port_80['service']['name'], 'http')
        self.assertEqual(port_80['service']['product'], 'Apache')
    
    def test_parse_text_output(self):
        """Test text output parsing (fallback)"""
        text_output = '''
        Nmap scan report for example.com (***********)
        Host is up (0.001s latency).
        
        PORT    STATE SERVICE VERSION
        22/tcp  open  ssh     OpenSSH 7.4
        80/tcp  open  http    Apache httpd 2.4.6
        443/tcp open  https   Apache httpd 2.4.6
        '''
        
        parsed = self.connector._parse_text_output(text_output)
        
        self.assertIn('hosts', parsed)
        self.assertEqual(len(parsed['hosts']), 1)
        
        host = parsed['hosts'][0]
        self.assertEqual(len(host['ports']), 3)
        
        # Check that ports were parsed
        port_numbers = [p['portid'] for p in host['ports']]
        self.assertIn('22', port_numbers)
        self.assertIn('80', port_numbers)
        self.assertIn('443', port_numbers)
    
    def test_extract_findings(self):
        """Test findings extraction"""
        parsed_data = {
            'hosts': [{
                'addresses': [{'addr': '***********', 'addrtype': 'ipv4'}],
                'ports': [
                    {
                        'portid': '22',
                        'protocol': 'tcp',
                        'state': {'state': 'open'},
                        'service': {'name': 'ssh', 'version': 'OpenSSH 7.4'}
                    },
                    {
                        'portid': '80',
                        'protocol': 'tcp',
                        'state': {'state': 'open'},
                        'service': {'name': 'http', 'version': 'Apache 2.4'}
                    },
                    {
                        'portid': '8080',
                        'protocol': 'tcp',
                        'state': {'state': 'closed'},
                        'service': {'name': 'http-proxy'}
                    }
                ]
            }]
        }
        
        findings = self.connector._extract_findings(parsed_data)
        
        # Should only have findings for open ports
        self.assertEqual(len(findings), 2)
        
        # Check finding structure
        for finding in findings:
            self.assertIn('type', finding)
            self.assertIn('severity', finding)
            self.assertIn('host', finding)
            self.assertIn('port', finding)
            self.assertIn('description', finding)
            self.assertEqual(finding['type'], 'open_port')
            self.assertEqual(finding['host'], '***********')
    
    def test_assess_port_severity(self):
        """Test port severity assessment"""
        # High severity ports
        telnet_port = {'portid': '23', 'service': {'name': 'telnet'}}
        self.assertEqual(self.connector._assess_port_severity(telnet_port), Severity.HIGH.value)
        
        # Medium severity ports
        ssh_port = {'portid': '22', 'service': {'name': 'ssh'}}
        self.assertEqual(self.connector._assess_port_severity(ssh_port), Severity.MEDIUM.value)
        
        # Low severity ports
        http_port = {'portid': '80', 'service': {'name': 'http'}}
        self.assertEqual(self.connector._assess_port_severity(http_port), Severity.LOW.value)
        
        # Info severity ports
        unknown_port = {'portid': '9999', 'service': {'name': 'unknown'}}
        self.assertEqual(self.connector._assess_port_severity(unknown_port), Severity.INFO.value)
    
    def test_get_port_recommendation(self):
        """Test port recommendations"""
        telnet_port = {'service': {'name': 'telnet'}}
        rec = self.connector._get_port_recommendation(telnet_port)
        self.assertIn('SSH', rec)
        
        ftp_port = {'service': {'name': 'ftp'}}
        rec = self.connector._get_port_recommendation(ftp_port)
        self.assertIn('SFTP', rec)
    
    def test_is_vulnerability_script(self):
        """Test vulnerability script detection"""
        self.assertTrue(self.connector._is_vulnerability_script('http-vuln-cve2017-5638'))
        self.assertTrue(self.connector._is_vulnerability_script('smb-vuln-ms17-010'))
        self.assertFalse(self.connector._is_vulnerability_script('http-title'))
        self.assertFalse(self.connector._is_vulnerability_script('banner'))
    
    def test_mock_output_generation(self):
        """Test mock output generation"""
        mock_output = self.connector._generate_mock_output("testhost.com", ports="22,80,443")
        
        self.assertIn('stdout', mock_output)
        self.assertIn('stderr', mock_output)
        self.assertIn('findings', mock_output)
        
        # Check XML structure
        self.assertIn('<?xml', mock_output['stdout'])
        self.assertIn('testhost.com', mock_output['stdout'])
        
        # Check findings
        self.assertIsInstance(mock_output['findings'], list)
        if mock_output['findings']:
            finding = mock_output['findings'][0]
            self.assertIn('type', finding)
            self.assertIn('host', finding)
            self.assertEqual(finding['host'], 'testhost.com')
    
    def test_get_mock_service_name(self):
        """Test mock service name mapping"""
        self.assertEqual(self.connector._get_mock_service_name(22), 'ssh')
        self.assertEqual(self.connector._get_mock_service_name(80), 'http')
        self.assertEqual(self.connector._get_mock_service_name(443), 'https')
        self.assertEqual(self.connector._get_mock_service_name(9999), 'unknown')


if __name__ == '__main__':
    unittest.main()
