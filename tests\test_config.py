"""
Unit tests for configuration management system.
"""

import json
import os
import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch

import yaml

from tool_connectors.config import Config<PERSON><PERSON><PERSON>, get_config_manager, get_config, get_tool_config


class TestConfigManager(unittest.TestCase):
    """Test cases for ConfigManager"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create a temporary directory for test configs
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, 'test_config.yaml')
    
    def tearDown(self):
        """Clean up test fixtures"""
        # Clean up temporary files
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_default_configuration(self):
        """Test default configuration loading"""
        config = ConfigManager()
        
        # Check that default tools are configured
        self.assertIn('nmap', config.get('tools', {}))
        self.assertIn('nikto', config.get('tools', {}))
        self.assertIn('sqlmap', config.get('tools', {}))
        self.assertIn('gobuster', config.get('tools', {}))
        self.assertIn('openvas', config.get('tools', {}))
        
        # Check default general settings
        self.assertFalse(config.get('general.mock_mode'))
        self.assertEqual(config.get('general.max_concurrent_tools'), 5)
        self.assertEqual(config.get('general.output_directory'), './scan_results')
        
        # Check default workflows
        self.assertEqual(config.get('workflows.quick'), ['nmap', 'nikto'])
        self.assertEqual(config.get('workflows.comprehensive'), ['nmap', 'nikto', 'gobuster', 'sqlmap'])
    
    def test_yaml_config_loading(self):
        """Test loading configuration from YAML file"""
        test_config = {
            'general': {
                'mock_mode': True,
                'max_concurrent_tools': 10
            },
            'tools': {
                'nmap': {
                    'path': '/custom/path/nmap',
                    'timeout': 600
                }
            }
        }
        
        with open(self.config_file, 'w') as f:
            yaml.dump(test_config, f)
        
        config = ConfigManager(self.config_file)
        
        self.assertTrue(config.get('general.mock_mode'))
        self.assertEqual(config.get('general.max_concurrent_tools'), 10)
        self.assertEqual(config.get('tools.nmap.path'), '/custom/path/nmap')
        self.assertEqual(config.get('tools.nmap.timeout'), 600)
    
    def test_json_config_loading(self):
        """Test loading configuration from JSON file"""
        json_file = os.path.join(self.temp_dir, 'test_config.json')
        test_config = {
            'general': {
                'log_level': 'DEBUG'
            },
            'tools': {
                'nikto': {
                    'path': '/usr/local/bin/nikto',
                    'default_params': {
                        'timeout': 45
                    }
                }
            }
        }
        
        with open(json_file, 'w') as f:
            json.dump(test_config, f)
        
        config = ConfigManager(json_file)
        
        self.assertEqual(config.get('general.log_level'), 'DEBUG')
        self.assertEqual(config.get('tools.nikto.path'), '/usr/local/bin/nikto')
        self.assertEqual(config.get('tools.nikto.default_params.timeout'), 45)
    
    def test_environment_variable_override(self):
        """Test environment variable configuration override"""
        with patch.dict(os.environ, {
            'TOOLCONNECTOR_NMAP_PATH': '/env/path/nmap',
            'TOOLCONNECTOR_NMAP_TIMEOUT': '900',
            'TOOLCONNECTOR_MOCK_MODE': 'true',
            'TOOLCONNECTOR_MAX_CONCURRENT': '8'
        }):
            config = ConfigManager()
            
            self.assertEqual(config.get('tools.nmap.path'), '/env/path/nmap')
            self.assertEqual(config.get('tools.nmap.timeout'), 900)
            self.assertTrue(config.get('general.mock_mode'))
            self.assertEqual(config.get('general.max_concurrent_tools'), 8)
    
    def test_config_merging(self):
        """Test configuration merging from multiple sources"""
        # Create a config file
        test_config = {
            'tools': {
                'nmap': {
                    'path': '/file/path/nmap',
                    'timeout': 400
                }
            },
            'general': {
                'output_directory': '/file/output'
            }
        }
        
        with open(self.config_file, 'w') as f:
            yaml.dump(test_config, f)
        
        # Set environment variables
        with patch.dict(os.environ, {
            'TOOLCONNECTOR_NMAP_TIMEOUT': '500',  # Should override file
            'TOOLCONNECTOR_OUTPUT_DIR': '/env/output'  # Should override file
        }):
            config = ConfigManager(self.config_file)
            
            # File path should be preserved
            self.assertEqual(config.get('tools.nmap.path'), '/file/path/nmap')
            # Environment timeout should override file
            self.assertEqual(config.get('tools.nmap.timeout'), 500)
            # Environment output dir should override file
            self.assertEqual(config.get('general.output_directory'), '/env/output')
    
    def test_get_set_methods(self):
        """Test get and set methods with dot notation"""
        config = ConfigManager()
        
        # Test getting existing values
        self.assertEqual(config.get('tools.nmap.path'), 'nmap')
        
        # Test getting non-existent values with default
        self.assertEqual(config.get('nonexistent.key', 'default'), 'default')
        self.assertIsNone(config.get('nonexistent.key'))
        
        # Test setting values
        config.set('custom.setting', 'test_value')
        self.assertEqual(config.get('custom.setting'), 'test_value')
        
        # Test setting nested values
        config.set('custom.nested.deep', 'deep_value')
        self.assertEqual(config.get('custom.nested.deep'), 'deep_value')
    
    def test_tool_specific_methods(self):
        """Test tool-specific configuration methods"""
        config = ConfigManager()
        
        # Test get_tool_config
        nmap_config = config.get_tool_config('nmap')
        self.assertIn('path', nmap_config)
        self.assertIn('timeout', nmap_config)
        self.assertIn('default_params', nmap_config)
        
        # Test get_tool_path
        self.assertEqual(config.get_tool_path('nmap'), 'nmap')
        self.assertEqual(config.get_tool_path('nonexistent'), 'nonexistent')
        
        # Test get_tool_timeout
        self.assertEqual(config.get_tool_timeout('nmap'), 300)
        self.assertEqual(config.get_tool_timeout('nonexistent'), 300)
        
        # Test get_tool_defaults
        nmap_defaults = config.get_tool_defaults('nmap')
        self.assertIn('scan_type', nmap_defaults)
        self.assertIn('service_detection', nmap_defaults)
    
    def test_workflow_methods(self):
        """Test workflow configuration methods"""
        config = ConfigManager()
        
        # Test get_workflow_tools
        quick_tools = config.get_workflow_tools('quick')
        self.assertEqual(quick_tools, ['nmap', 'nikto'])
        
        comprehensive_tools = config.get_workflow_tools('comprehensive')
        self.assertEqual(comprehensive_tools, ['nmap', 'nikto', 'gobuster', 'sqlmap'])
        
        # Test non-existent workflow
        self.assertEqual(config.get_workflow_tools('nonexistent'), [])
    
    def test_utility_methods(self):
        """Test utility methods"""
        config = ConfigManager()
        
        # Test is_mock_mode
        self.assertFalse(config.is_mock_mode())
        config.set('general.mock_mode', True)
        self.assertTrue(config.is_mock_mode())
        
        # Test get_output_directory
        self.assertEqual(config.get_output_directory(), './scan_results')
        
        # Test get_max_concurrent_tools
        self.assertEqual(config.get_max_concurrent_tools(), 5)
    
    def test_save_config(self):
        """Test saving configuration to file"""
        config = ConfigManager()
        config.set('custom.test', 'save_test')
        
        # Test YAML save
        yaml_file = os.path.join(self.temp_dir, 'saved_config.yaml')
        config.save_config(yaml_file, 'yaml')
        
        self.assertTrue(os.path.exists(yaml_file))
        
        # Load and verify
        with open(yaml_file, 'r') as f:
            saved_config = yaml.safe_load(f)
        
        self.assertEqual(saved_config['custom']['test'], 'save_test')
        
        # Test JSON save
        json_file = os.path.join(self.temp_dir, 'saved_config.json')
        config.save_config(json_file, 'json')
        
        self.assertTrue(os.path.exists(json_file))
        
        # Load and verify
        with open(json_file, 'r') as f:
            saved_config = json.load(f)
        
        self.assertEqual(saved_config['custom']['test'], 'save_test')
    
    def test_config_sources(self):
        """Test configuration sources tracking"""
        config = ConfigManager()
        sources = config.get_config_sources()
        
        self.assertIn('default', sources)
    
    def test_invalid_config_file(self):
        """Test handling of invalid configuration files"""
        # Create invalid YAML file
        invalid_file = os.path.join(self.temp_dir, 'invalid.yaml')
        with open(invalid_file, 'w') as f:
            f.write('invalid: yaml: content: [')
        
        # Should not raise exception, just log warning
        config = ConfigManager(invalid_file)
        self.assertIsNotNone(config)


class TestGlobalConfigFunctions(unittest.TestCase):
    """Test global configuration functions"""
    
    def test_get_config_manager(self):
        """Test global config manager function"""
        manager1 = get_config_manager()
        manager2 = get_config_manager()
        
        # Should return same instance
        self.assertIs(manager1, manager2)
    
    def test_get_config(self):
        """Test global get_config function"""
        value = get_config('tools.nmap.path', 'default')
        self.assertEqual(value, 'nmap')
        
        default_value = get_config('nonexistent.key', 'default')
        self.assertEqual(default_value, 'default')
    
    def test_get_tool_config(self):
        """Test global get_tool_config function"""
        nmap_config = get_tool_config('nmap')
        self.assertIn('path', nmap_config)
        self.assertIn('timeout', nmap_config)


if __name__ == '__main__':
    unittest.main()
