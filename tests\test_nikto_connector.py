"""
Unit tests for NiktoConnector.
"""

import unittest
from unittest.mock import patch, Mock

from tool_connectors.nikto_connector import NiktoConnector
from tool_connectors.base import Tool<PERSON>tatus, Severity


class TestNiktoConnector(unittest.TestCase):
    """Test cases for NiktoConnector"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.connector = NiktoConnector(mock_mode=True)
    
    def test_initialization(self):
        """Test connector initialization"""
        self.assertEqual(self.connector.tool_name, "nikto")
        self.assertEqual(self.connector.default_command, "nikto")
        self.assertTrue(self.connector.mock_mode)
    
    def test_build_command_basic(self):
        """Test basic command building"""
        cmd = self.connector.build_command("http://example.com")
        
        self.assertIn("nikto", cmd[0])
        self.assertIn("-h", cmd)
        host_index = cmd.index("-h")
        self.assertEqual(cmd[host_index + 1], "http://example.com")
        self.assertIn("-nointeractive", cmd)
    
    def test_build_command_with_port(self):
        """Test command building with port specification"""
        cmd = self.connector.build_command("example.com", port=8080)
        
        self.assertIn("-p", cmd)
        port_index = cmd.index("-p")
        self.assertEqual(cmd[port_index + 1], "8080")
    
    def test_build_command_with_ssl(self):
        """Test command building with SSL"""
        cmd = self.connector.build_command("example.com", ssl=True)
        self.assertIn("-ssl", cmd)
    
    def test_build_command_with_plugins(self):
        """Test command building with plugins"""
        cmd = self.connector.build_command("example.com", plugins="@@ALL")
        
        self.assertIn("-Plugins", cmd)
        plugins_index = cmd.index("-Plugins")
        self.assertEqual(cmd[plugins_index + 1], "@@ALL")
    
    def test_build_command_with_tuning(self):
        """Test command building with tuning options"""
        cmd = self.connector.build_command("example.com", tuning="1,2,3")
        
        self.assertIn("-Tuning", cmd)
        tuning_index = cmd.index("-Tuning")
        self.assertEqual(cmd[tuning_index + 1], "1,2,3")
    
    def test_build_command_with_timeout(self):
        """Test command building with timeout"""
        cmd = self.connector.build_command("example.com", timeout=30)
        
        self.assertIn("-timeout", cmd)
        timeout_index = cmd.index("-timeout")
        self.assertEqual(cmd[timeout_index + 1], "30")
    
    def test_build_command_with_user_agent(self):
        """Test command building with custom user agent"""
        user_agent = "Mozilla/5.0 (Test Browser)"
        cmd = self.connector.build_command("example.com", user_agent=user_agent)
        
        self.assertIn("-useragent", cmd)
        ua_index = cmd.index("-useragent")
        self.assertEqual(cmd[ua_index + 1], user_agent)
    
    def test_build_command_output_formats(self):
        """Test different output formats"""
        # XML output
        cmd = self.connector.build_command("example.com", output_format="xml")
        self.assertIn("-Format", cmd)
        format_index = cmd.index("-Format")
        self.assertEqual(cmd[format_index + 1], "xml")
        
        # CSV output
        cmd = self.connector.build_command("example.com", output_format="csv")
        format_index = cmd.index("-Format")
        self.assertEqual(cmd[format_index + 1], "csv")
    
    def test_validate_target(self):
        """Test target validation"""
        # Valid URLs
        self.assertTrue(self.connector.validate_target("http://example.com"))
        self.assertTrue(self.connector.validate_target("https://example.com"))
        self.assertTrue(self.connector.validate_target("http://***********"))

        # Valid hostnames
        self.assertTrue(self.connector.validate_target("example.com"))
        self.assertTrue(self.connector.validate_target("***********"))

        # Invalid targets
        self.assertFalse(self.connector.validate_target(""))
        self.assertFalse(self.connector.validate_target(None))
    
    def test_parse_output_basic(self):
        """Test basic output parsing functionality"""
        # Test that parse_output method exists and returns a dict
        stdout = "Mock Nikto output"
        stderr = ""

        result = self.connector.parse_output(stdout, stderr)
        self.assertIsInstance(result, dict)
    
    def test_extract_findings(self):
        """Test findings extraction"""
        parsed_data = {
            'target': 'example.com',
            'vulnerabilities': [
                {
                    'description': 'Directory indexing found.',
                    'url_path': '/admin/',
                    'osvdb_id': 'OSVDB-3268'
                },
                {
                    'description': 'This might be interesting...',
                    'url_path': '/admin/',
                    'osvdb_id': 'OSVDB-3092'
                }
            ]
        }

        findings = self.connector._extract_findings(parsed_data)

        self.assertEqual(len(findings), 2)

        # Check finding structure
        for finding in findings:
            self.assertIn('type', finding)
            self.assertIn('severity', finding)
            self.assertIn('target', finding)
            self.assertIn('description', finding)
            self.assertEqual(finding['type'], 'web_vulnerability')
            self.assertEqual(finding['target'], 'example.com')
    
    def test_assess_vulnerability_severity(self):
        """Test vulnerability severity assessment"""
        # High severity
        self.assertEqual(self.connector._assess_vulnerability_severity('Remote code execution'), Severity.CRITICAL.value)
        self.assertEqual(self.connector._assess_vulnerability_severity('SQL injection possible'), Severity.HIGH.value)

        # Medium severity
        self.assertEqual(self.connector._assess_vulnerability_severity('Information disclosure'), Severity.MEDIUM.value)

        # Low severity
        self.assertEqual(self.connector._assess_vulnerability_severity('Server banner revealed'), Severity.LOW.value)

        # Info severity
        self.assertEqual(self.connector._assess_vulnerability_severity('Directory indexing found'), Severity.INFO.value)
    
    def test_get_vulnerability_recommendation(self):
        """Test vulnerability recommendations"""
        rec = self.connector._get_vulnerability_recommendation('Directory indexing found')
        self.assertIn('directory', rec.lower())

        rec = self.connector._get_vulnerability_recommendation('Server version revealed')
        self.assertIn('version', rec.lower())

        rec = self.connector._get_vulnerability_recommendation('Some other vulnerability')
        self.assertIn('Review', rec)
    
    def test_mock_execution(self):
        """Test mock execution"""
        result = self.connector.execute("http://example.com", timeout=10)
        
        self.assertEqual(result.status, ToolStatus.COMPLETED)
        self.assertEqual(result.tool_name, "nikto")
        self.assertGreater(result.execution_time, 0)
        self.assertIsInstance(result.findings, list)
    



if __name__ == '__main__':
    unittest.main()
