# Tool Connectors Framework - API Reference

This document provides comprehensive API reference documentation for the Tool Connectors Framework, including all classes, methods, and configuration options.

## Table of Contents

1. [Core Classes](#core-classes)
2. [Tool Connectors](#tool-connectors)
3. [Security System](#security-system)
4. [Monitoring System](#monitoring-system)
5. [Reporting System](#reporting-system)
6. [Workflow Engine](#workflow-engine)
7. [Configuration](#configuration)

## Core Classes

### ToolManager

The main entry point for the Tool Connectors Framework.

```python
class ToolManager:
    def __init__(self, mock_mode: bool = False, config_file: Optional[str] = None,
                 cache_enabled: bool = True, cache_backend: str = "memory",
                 monitoring_enabled: bool = True, log_level: str = "INFO",
                 secure_mode: bool = True, security_policy: Optional[SecurityPolicy] = None)
```

#### Parameters

- `mock_mode` (bool): Enable mock mode for testing without actual tools
- `config_file` (str, optional): Path to configuration file
- `cache_enabled` (bool): Enable result caching
- `cache_backend` (str): Cache backend type ('memory', 'file', 'sqlite')
- `monitoring_enabled` (bool): Enable performance monitoring
- `log_level` (str): Logging level for monitoring
- `secure_mode` (bool): Enable secure execution mode
- `security_policy` (SecurityPolicy, optional): Custom security policy

#### Methods

##### execute_tool(tool_name, target, **kwargs)

Execute a specific tool synchronously.

```python
def execute_tool(self, tool_name: str, target: str, **kwargs) -> ToolResult
```

**Parameters:**
- `tool_name` (str): Name of the tool to execute
- `target` (str): Target to scan/test
- `**kwargs`: Tool-specific parameters

**Returns:** `ToolResult` containing execution results

**Example:**
```python
result = manager.execute_tool('nmap', 'example.com', scan_type='tcp')
```

##### get_available_tools()

Get list of available tools.

```python
def get_available_tools(self) -> List[str]
```

**Returns:** List of available tool names

##### generate_advanced_report(results, output_format, template_name, metadata_overrides, custom_sections)

Generate an advanced report from tool results.

```python
def generate_advanced_report(self, results: List[ToolResult], 
                           output_format: str = 'html',
                           template_name: Optional[str] = None,
                           metadata_overrides: Optional[Dict[str, Any]] = None,
                           custom_sections: Optional[List[ReportSection]] = None) -> str
```

**Parameters:**
- `results` (List[ToolResult]): List of tool results
- `output_format` (str): Output format ('html', 'json', 'xml', 'csv')
- `template_name` (str, optional): Template to use
- `metadata_overrides` (dict, optional): Metadata overrides
- `custom_sections` (list, optional): Custom sections to add

**Returns:** Formatted report as string

##### get_security_status()

Get current security status and configuration.

```python
def get_security_status(self) -> Dict[str, Any]
```

**Returns:** Dictionary with security status information

##### get_health_status()

Get current health status.

```python
def get_health_status(self) -> Dict[str, Any]
```

**Returns:** Dictionary with health check results

### ToolResult

Represents the result of a tool execution.

```python
@dataclass
class ToolResult:
    tool_name: str
    command: str
    status: ToolStatus
    exit_code: Optional[int] = None
    stdout: str = ""
    stderr: str = ""
    execution_time: float = 0.0
    timestamp: float = field(default_factory=time.time)
    findings: List[Dict[str, Any]] = field(default_factory=list)
    raw_output: str = ""
    parsed_data: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
```

#### Attributes

- `tool_name` (str): Name of the executed tool
- `command` (str): Command that was executed
- `status` (ToolStatus): Execution status
- `exit_code` (int, optional): Process exit code
- `stdout` (str): Standard output
- `stderr` (str): Standard error
- `execution_time` (float): Execution time in seconds
- `timestamp` (float): Execution timestamp
- `findings` (List[Dict]): Structured findings
- `raw_output` (str): Raw tool output
- `parsed_data` (Dict): Parsed structured data
- `error_message` (str, optional): Error message if execution failed

### ToolStatus

Enumeration of tool execution statuses.

```python
class ToolStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"
```

## Tool Connectors

### BaseToolConnector

Abstract base class for all tool connectors.

```python
class BaseToolConnector(ABC):
    def __init__(self, tool_path: Optional[str] = None, timeout: int = 300,
                 mock_mode: bool = False, mock_data: Optional[Dict[str, Any]] = None,
                 secure_mode: bool = True)
```

#### Methods

##### execute(target, **kwargs)

Execute the tool against a target.

```python
@abstractmethod
def execute(self, target: str, **kwargs) -> ToolResult
```

##### is_available()

Check if the tool is available on the system.

```python
def is_available(self) -> bool
```

### NmapConnector

Nmap network scanner connector.

```python
class NmapConnector(BaseToolConnector):
    def execute(self, target: str, scan_type: str = "tcp", 
                ports: str = "1-1000", **kwargs) -> ToolResult
```

**Parameters:**
- `target` (str): Target to scan
- `scan_type` (str): Type of scan ('tcp', 'udp', 'syn', 'discovery')
- `ports` (str): Port range to scan
- `**kwargs`: Additional nmap options

### NiktoConnector

Nikto web vulnerability scanner connector.

```python
class NiktoConnector(BaseToolConnector):
    def execute(self, target: str, **kwargs) -> ToolResult
```

**Parameters:**
- `target` (str): Target URL to scan
- `**kwargs`: Additional nikto options

### GobusterConnector

Gobuster directory/file brute-forcer connector.

```python
class GobusterConnector(BaseToolConnector):
    def execute(self, target: str, wordlist: str = None, 
                extensions: str = None, **kwargs) -> ToolResult
```

**Parameters:**
- `target` (str): Target URL
- `wordlist` (str, optional): Path to wordlist file
- `extensions` (str, optional): File extensions to search for
- `**kwargs`: Additional gobuster options

### SqlmapConnector

SQLMap SQL injection testing connector.

```python
class SqlmapConnector(BaseToolConnector):
    def execute(self, target: str, data: str = None, 
                risk: int = 1, level: int = 1, **kwargs) -> ToolResult
```

**Parameters:**
- `target` (str): Target URL
- `data` (str, optional): POST data
- `risk` (int): Risk level (1-3)
- `level` (int): Test level (1-5)
- `**kwargs`: Additional sqlmap options

## Security System

### SecurityPolicy

Configuration for security policies.

```python
@dataclass
class SecurityPolicy:
    max_input_length: int = 1000
    allowed_characters: str = r'[a-zA-Z0-9\.\-_:/\s]+'
    blocked_patterns: List[str] = field(default_factory=list)
    allowed_commands: Set[str] = field(default_factory=set)
    command_timeout: int = 300
    max_processes: int = 5
    max_memory_mb: int = 1024
    max_cpu_percent: int = 80
    use_sandbox: bool = True
    log_all_commands: bool = True
    monitor_resources: bool = True
```

### SecurityViolation

Exception raised when security policy is violated.

```python
class SecurityViolation(Exception):
    def __init__(self, message: str, violation_type: str, details: Dict[str, Any] = None)
```

#### Attributes

- `violation_type` (str): Type of violation
- `details` (Dict): Additional violation details

### InputValidator

Input validation and sanitization.

```python
class InputValidator:
    def __init__(self, policy: SecurityPolicy)
    
    def validate_input(self, input_value: str, input_name: str = "input") -> str
    def validate_command(self, command: str, tool_name: str) -> List[str]
```

## Monitoring System

### MonitoringManager

Central monitoring manager.

```python
class MonitoringManager:
    def __init__(self, log_level: str = "INFO", log_file: Optional[str] = None)
    
    def start_tool_execution(self, tool_name: str, target: str) -> Dict[str, Any]
    def end_tool_execution(self, context: Dict[str, Any], result: ToolResult, 
                          cache_hit: bool = False)
    def get_monitoring_summary(self) -> Dict[str, Any]
```

### HealthMonitor

Health monitoring system.

```python
class HealthMonitor:
    def add_check(self, check: HealthCheck)
    def remove_check(self, name: str)
    def run_check(self, name: str) -> Dict[str, Any]
    def run_all_checks(self) -> Dict[str, Any]
```

### HealthCheck

Health check definition.

```python
@dataclass
class HealthCheck:
    name: str
    check_function: Callable[[], bool]
    description: str
    timeout: float = 5.0
    critical: bool = True
```

## Reporting System

### ReportGenerator

Main report generator.

```python
class ReportGenerator:
    def generate_report(self, results: List[ToolResult], 
                       output_format: str = 'html',
                       template_name: Optional[str] = None,
                       metadata_overrides: Optional[Dict[str, Any]] = None,
                       custom_sections: Optional[List[ReportSection]] = None) -> str
    
    def save_report(self, report_content: str, file_path: str, output_format: str)
    def get_available_formats(self) -> List[str]
    def get_available_templates(self) -> List[str]
```

### ReportTemplate

Template for generating reports.

```python
class ReportTemplate:
    def __init__(self, name: str, description: str = "")
    
    def add_section(self, section: ReportSection) -> 'ReportTemplate'
    def set_metadata_template(self, **kwargs) -> 'ReportTemplate'
    def generate_metadata(self, **overrides) -> ReportMetadata
```

### ReportSection

A section within a report.

```python
@dataclass
class ReportSection:
    title: str
    content: str
    subsections: List['ReportSection'] = field(default_factory=list)
    data: Optional[Dict[str, Any]] = None
    charts: List[Dict[str, Any]] = field(default_factory=list)
```

## Workflow Engine

### WorkflowEngine

Workflow execution engine.

```python
class WorkflowEngine:
    def __init__(self, tool_manager: ToolManager)
    
    def execute_workflow(self, workflow_config: Dict[str, Any]) -> WorkflowResult
    def validate_workflow(self, workflow_config: Dict[str, Any]) -> bool
```

### WorkflowResult

Result of workflow execution.

```python
@dataclass
class WorkflowResult:
    workflow_name: str
    status: WorkflowStatus
    execution_time: float
    step_results: Dict[str, ToolResult]
    failed_steps: List[str]
    skipped_steps: List[str]
    total_findings: int
```

## Configuration

### ConfigManager

Configuration management system.

```python
class ConfigManager:
    def __init__(self, config_file: Optional[str] = None)
    
    def get(self, key: str, default: Any = None) -> Any
    def get_tool_config(self, tool_name: str) -> Dict[str, Any]
    def is_mock_mode(self) -> bool
```

### Configuration File Format

Configuration files use YAML format:

```yaml
general:
  mock_mode: false
  cache_ttl: 3600
  log_level: INFO
  log_file: /var/log/tool_connectors.log

security:
  command_timeout: 300
  max_memory_mb: 1024
  max_cpu_percent: 80
  use_sandbox: true
  log_all_commands: true

tools:
  nmap:
    path: /usr/bin/nmap
    timeout: 600
    default_params:
      scan_type: tcp
      ports: "1-1000"
  
  nikto:
    path: /usr/bin/nikto
    timeout: 900
    
  gobuster:
    path: /usr/bin/gobuster
    timeout: 1200
    default_params:
      wordlist: /usr/share/wordlists/dirb/common.txt
      
  sqlmap:
    path: /usr/bin/sqlmap
    timeout: 1800
    default_params:
      risk: 1
      level: 1

cache:
  backend: sqlite
  sqlite_path: ./cache.db
  file_cache_dir: ./cache
  ttl: 3600

monitoring:
  enabled: true
  dashboard_port: 8080
  metrics_retention_hours: 24
```

## Error Handling

### Common Exceptions

- `SecurityViolation`: Security policy violation
- `ToolNotAvailableError`: Tool not found or not executable
- `ExecutionTimeoutError`: Tool execution timeout
- `ValidationError`: Input validation failure
- `WorkflowError`: Workflow execution error

### Error Response Format

```python
{
    "error": "Error message",
    "error_type": "SecurityViolation",
    "details": {
        "violation_type": "blocked_pattern",
        "input_name": "target",
        "pattern": "[;&|`$(){}[\\]\\\\]"
    },
    "timestamp": 1640995200.0
}
```

## Usage Examples

### Basic Usage

```python
from tool_connectors import ToolManager

# Initialize manager
manager = ToolManager(secure_mode=True, monitoring_enabled=True)

# Execute tool
result = manager.execute_tool('nmap', 'example.com', scan_type='tcp')

# Generate report
report = manager.generate_advanced_report([result], 'html')
```

### Advanced Configuration

```python
from tool_connectors import ToolManager
from tool_connectors.security import SecurityPolicy

# Custom security policy
policy = SecurityPolicy(
    command_timeout=600,
    max_memory_mb=2048,
    allowed_commands={'nmap', 'nikto'},
    use_sandbox=True
)

# Initialize with custom configuration
manager = ToolManager(
    config_file='config/production.yaml',
    secure_mode=True,
    security_policy=policy,
    monitoring_enabled=True,
    cache_backend='sqlite'
)
```

### Workflow Execution

```python
workflow_config = {
    "name": "web_security_scan",
    "steps": [
        {
            "name": "port_scan",
            "tool": "nmap",
            "target": "example.com",
            "parameters": {"scan_type": "tcp"}
        },
        {
            "name": "web_scan",
            "tool": "nikto",
            "target": "http://example.com",
            "depends_on": ["port_scan"]
        }
    ]
}

result = manager.workflow_engine.execute_workflow(workflow_config)
```

This API reference provides comprehensive documentation for integrating and using the Tool Connectors Framework in various scenarios.
