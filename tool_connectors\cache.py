"""
Caching and persistence system for tool connectors.

This module provides caching mechanisms to avoid re-running expensive scans
and implements result persistence to database or file system with query capabilities.
"""

import hashlib
import json
import logging
import os
import sqlite3
import time
from abc import ABC, abstractmethod
from dataclasses import asdict
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from .base import ToolResult, ToolStatus


class CacheBackend(ABC):
    """Abstract base class for cache backends."""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Dict[str, Any]]:
        """Get cached result by key."""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """Set cached result with optional TTL."""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """Delete cached result by key."""
        pass
    
    @abstractmethod
    def clear(self) -> bool:
        """Clear all cached results."""
        pass
    
    @abstractmethod
    def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        pass
    
    @abstractmethod
    def keys(self, pattern: Optional[str] = None) -> List[str]:
        """Get all keys, optionally matching a pattern."""
        pass


class MemoryCache(CacheBackend):
    """In-memory cache backend."""
    
    def __init__(self):
        """Initialize memory cache."""
        self._cache: Dict[str, Dict[str, Any]] = {}
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def get(self, key: str) -> Optional[Dict[str, Any]]:
        """Get cached result by key."""
        if key not in self._cache:
            return None
        
        entry = self._cache[key]
        
        # Check TTL
        if entry.get('expires_at') and time.time() > entry['expires_at']:
            del self._cache[key]
            return None
        
        return entry['data']
    
    def set(self, key: str, value: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """Set cached result with optional TTL."""
        entry = {
            'data': value,
            'created_at': time.time(),
            'expires_at': time.time() + ttl if ttl else None
        }
        
        self._cache[key] = entry
        return True
    
    def delete(self, key: str) -> bool:
        """Delete cached result by key."""
        if key in self._cache:
            del self._cache[key]
            return True
        return False
    
    def clear(self) -> bool:
        """Clear all cached results."""
        self._cache.clear()
        return True
    
    def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        return self.get(key) is not None
    
    def keys(self, pattern: Optional[str] = None) -> List[str]:
        """Get all keys, optionally matching a pattern."""
        keys = list(self._cache.keys())
        
        if pattern:
            import fnmatch
            keys = [k for k in keys if fnmatch.fnmatch(k, pattern)]
        
        return keys


class FileCache(CacheBackend):
    """File-based cache backend."""
    
    def __init__(self, cache_dir: str = "./cache"):
        """Initialize file cache."""
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def _get_file_path(self, key: str) -> Path:
        """Get file path for cache key."""
        # Use hash to avoid filesystem issues with special characters
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{key_hash}.json"
    
    def get(self, key: str) -> Optional[Dict[str, Any]]:
        """Get cached result by key."""
        file_path = self._get_file_path(key)
        
        if not file_path.exists():
            return None
        
        try:
            with open(file_path, 'r') as f:
                entry = json.load(f)
            
            # Check TTL
            if entry.get('expires_at') and time.time() > entry['expires_at']:
                file_path.unlink()
                return None
            
            return entry['data']
            
        except (json.JSONDecodeError, KeyError, OSError) as e:
            self.logger.warning(f"Error reading cache file {file_path}: {e}")
            return None
    
    def set(self, key: str, value: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """Set cached result with optional TTL."""
        file_path = self._get_file_path(key)
        
        entry = {
            'key': key,
            'data': value,
            'created_at': time.time(),
            'expires_at': time.time() + ttl if ttl else None
        }
        
        try:
            with open(file_path, 'w') as f:
                json.dump(entry, f, indent=2, default=str)
            return True
            
        except OSError as e:
            self.logger.error(f"Error writing cache file {file_path}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete cached result by key."""
        file_path = self._get_file_path(key)
        
        if file_path.exists():
            try:
                file_path.unlink()
                return True
            except OSError as e:
                self.logger.error(f"Error deleting cache file {file_path}: {e}")
        
        return False
    
    def clear(self) -> bool:
        """Clear all cached results."""
        try:
            for file_path in self.cache_dir.glob("*.json"):
                file_path.unlink()
            return True
        except OSError as e:
            self.logger.error(f"Error clearing cache directory: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        return self.get(key) is not None
    
    def keys(self, pattern: Optional[str] = None) -> List[str]:
        """Get all keys, optionally matching a pattern."""
        keys = []
        
        for file_path in self.cache_dir.glob("*.json"):
            try:
                with open(file_path, 'r') as f:
                    entry = json.load(f)
                    key = entry.get('key')
                    if key:
                        keys.append(key)
            except (json.JSONDecodeError, OSError):
                continue
        
        if pattern:
            import fnmatch
            keys = [k for k in keys if fnmatch.fnmatch(k, pattern)]
        
        return keys


class SQLiteCache(CacheBackend):
    """SQLite-based cache backend with persistence."""

    def __init__(self, db_path: str = "./cache.db"):
        """Initialize SQLite cache."""
        self.db_path = db_path
        self.logger = logging.getLogger(self.__class__.__name__)
        self._init_db()

    def close(self):
        """Close database connections (for cleanup)."""
        # SQLite connections are closed automatically when context managers exit
        pass
    
    def _init_db(self):
        """Initialize database schema."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS cache_entries (
                    key TEXT PRIMARY KEY,
                    data TEXT NOT NULL,
                    created_at REAL NOT NULL,
                    expires_at REAL,
                    tool_name TEXT,
                    target TEXT,
                    status TEXT
                )
            """)
            
            # Create indexes for better query performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_tool_name ON cache_entries(tool_name)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_target ON cache_entries(target)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_status ON cache_entries(status)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_expires_at ON cache_entries(expires_at)")
            
            conn.commit()
    
    def get(self, key: str) -> Optional[Dict[str, Any]]:
        """Get cached result by key."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT data, expires_at FROM cache_entries WHERE key = ?",
                (key,)
            )
            row = cursor.fetchone()
            
            if not row:
                return None
            
            data_json, expires_at = row
            
            # Check TTL
            if expires_at and time.time() > expires_at:
                conn.execute("DELETE FROM cache_entries WHERE key = ?", (key,))
                conn.commit()
                return None
            
            try:
                return json.loads(data_json)
            except json.JSONDecodeError as e:
                self.logger.warning(f"Error parsing cached data for key {key}: {e}")
                return None
    
    def set(self, key: str, value: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """Set cached result with optional TTL."""
        try:
            data_json = json.dumps(value, default=str)
            created_at = time.time()
            expires_at = created_at + ttl if ttl else None
            
            # Extract metadata for indexing
            tool_name = value.get('tool_name', '')
            target = value.get('target', '')
            status = value.get('status', '')
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO cache_entries 
                    (key, data, created_at, expires_at, tool_name, target, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (key, data_json, created_at, expires_at, tool_name, target, status))
                conn.commit()
            
            return True
            
        except (json.JSONEncodeError, sqlite3.Error) as e:
            self.logger.error(f"Error caching data for key {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete cached result by key."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("DELETE FROM cache_entries WHERE key = ?", (key,))
                conn.commit()
                return cursor.rowcount > 0
        except sqlite3.Error as e:
            self.logger.error(f"Error deleting cache entry {key}: {e}")
            return False
    
    def clear(self) -> bool:
        """Clear all cached results."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("DELETE FROM cache_entries")
                conn.commit()
            return True
        except sqlite3.Error as e:
            self.logger.error(f"Error clearing cache: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        return self.get(key) is not None
    
    def keys(self, pattern: Optional[str] = None) -> List[str]:
        """Get all keys, optionally matching a pattern."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT key FROM cache_entries")
                keys = [row[0] for row in cursor.fetchall()]
            
            if pattern:
                import fnmatch
                keys = [k for k in keys if fnmatch.fnmatch(k, pattern)]
            
            return keys
            
        except sqlite3.Error as e:
            self.logger.error(f"Error getting cache keys: {e}")
            return []
    
    def query(self, tool_name: Optional[str] = None, target: Optional[str] = None,
              status: Optional[str] = None, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Query cached results with filters."""
        conditions = []
        params = []
        
        if tool_name:
            conditions.append("tool_name = ?")
            params.append(tool_name)
        
        if target:
            conditions.append("target LIKE ?")
            params.append(f"%{target}%")
        
        if status:
            conditions.append("status = ?")
            params.append(status)
        
        # Filter out expired entries
        conditions.append("(expires_at IS NULL OR expires_at > ?)")
        params.append(time.time())
        
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        limit_clause = f" LIMIT {limit}" if limit else ""
        
        query = f"""
            SELECT key, data, created_at, tool_name, target, status
            FROM cache_entries 
            WHERE {where_clause}
            ORDER BY created_at DESC
            {limit_clause}
        """
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(query, params)
                results = []
                
                for row in cursor.fetchall():
                    key, data_json, created_at, tool_name, target, status = row
                    try:
                        data = json.loads(data_json)
                        results.append({
                            'key': key,
                            'data': data,
                            'created_at': created_at,
                            'tool_name': tool_name,
                            'target': target,
                            'status': status
                        })
                    except json.JSONDecodeError:
                        continue
                
                return results
                
        except sqlite3.Error as e:
            self.logger.error(f"Error querying cache: {e}")
            return []
    
    def cleanup_expired(self) -> int:
        """Remove expired cache entries."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "DELETE FROM cache_entries WHERE expires_at IS NOT NULL AND expires_at <= ?",
                    (time.time(),)
                )
                conn.commit()
                return cursor.rowcount
        except sqlite3.Error as e:
            self.logger.error(f"Error cleaning up expired entries: {e}")
            return 0


class CacheManager:
    """
    Cache manager for tool results with configurable backends and policies.
    """

    def __init__(self, backend: Optional[CacheBackend] = None,
                 default_ttl: int = 3600, enabled: bool = True):
        """
        Initialize cache manager.

        Args:
            backend: Cache backend to use (defaults to MemoryCache)
            default_ttl: Default TTL in seconds (1 hour)
            enabled: Whether caching is enabled
        """
        self.backend = backend or MemoryCache()
        self.default_ttl = default_ttl
        self.enabled = enabled
        self.logger = logging.getLogger(self.__class__.__name__)

        # Cache policies for different tools
        self.tool_policies = {
            'nmap': {'ttl': 1800, 'cache_on_error': False},  # 30 minutes
            'nikto': {'ttl': 3600, 'cache_on_error': False},  # 1 hour
            'sqlmap': {'ttl': 7200, 'cache_on_error': True},  # 2 hours
            'gobuster': {'ttl': 1800, 'cache_on_error': False},  # 30 minutes
            'openvas': {'ttl': 14400, 'cache_on_error': False},  # 4 hours
        }

    def generate_cache_key(self, tool_name: str, target: str, **kwargs) -> str:
        """
        Generate a cache key for tool execution.

        Args:
            tool_name: Name of the tool
            target: Target being scanned
            **kwargs: Tool parameters

        Returns:
            Cache key string
        """
        # Create a deterministic key based on tool, target, and parameters
        key_data = {
            'tool': tool_name,
            'target': target,
            'params': sorted(kwargs.items())
        }

        key_string = json.dumps(key_data, sort_keys=True, default=str)
        key_hash = hashlib.sha256(key_string.encode()).hexdigest()

        return f"{tool_name}:{target}:{key_hash[:16]}"

    def should_cache_result(self, tool_name: str, result: ToolResult) -> bool:
        """
        Determine if a result should be cached.

        Args:
            tool_name: Name of the tool
            result: Tool execution result

        Returns:
            True if result should be cached
        """
        if not self.enabled:
            return False

        policy = self.tool_policies.get(tool_name, {})
        cache_on_error = policy.get('cache_on_error', False)

        # Don't cache failed results unless policy allows it
        if result.status == ToolStatus.FAILED and not cache_on_error:
            return False

        return True

    def get_cached_result(self, tool_name: str, target: str, **kwargs) -> Optional[ToolResult]:
        """
        Get cached result for tool execution.

        Args:
            tool_name: Name of the tool
            target: Target being scanned
            **kwargs: Tool parameters

        Returns:
            Cached ToolResult or None if not found
        """
        if not self.enabled:
            return None

        cache_key = self.generate_cache_key(tool_name, target, **kwargs)

        try:
            cached_data = self.backend.get(cache_key)
            if cached_data:
                self.logger.info(f"Cache hit for {tool_name} on {target}")
                return self._deserialize_result(cached_data)
        except Exception as e:
            self.logger.warning(f"Error retrieving cached result: {e}")

        return None

    def cache_result(self, tool_name: str, target: str, result: ToolResult, **kwargs) -> bool:
        """
        Cache a tool execution result.

        Args:
            tool_name: Name of the tool
            target: Target being scanned
            result: Tool execution result
            **kwargs: Tool parameters

        Returns:
            True if result was cached successfully
        """
        if not self.should_cache_result(tool_name, result):
            return False

        cache_key = self.generate_cache_key(tool_name, target, **kwargs)
        policy = self.tool_policies.get(tool_name, {})
        ttl = policy.get('ttl', self.default_ttl)

        try:
            serialized_data = self._serialize_result(result, tool_name, target)
            success = self.backend.set(cache_key, serialized_data, ttl)

            if success:
                self.logger.info(f"Cached result for {tool_name} on {target} (TTL: {ttl}s)")

            return success

        except Exception as e:
            self.logger.error(f"Error caching result: {e}")
            return False

    def _serialize_result(self, result: ToolResult, tool_name: str, target: str) -> Dict[str, Any]:
        """Serialize ToolResult for caching."""
        data = {
            'tool_name': result.tool_name,
            'command': result.command,
            'status': result.status.value,
            'exit_code': result.exit_code,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'execution_time': result.execution_time,
            'timestamp': result.timestamp,
            'findings': result.findings,
            'raw_output': result.raw_output,
            'parsed_data': result.parsed_data,
            'error_message': result.error_message,
            'target': target,
            'cached_at': time.time()
        }
        return data

    def _deserialize_result(self, data: Dict[str, Any]) -> ToolResult:
        """Deserialize cached data to ToolResult."""
        return ToolResult(
            tool_name=data['tool_name'],
            command=data['command'],
            status=ToolStatus(data['status']),
            exit_code=data.get('exit_code'),
            stdout=data.get('stdout', ''),
            stderr=data.get('stderr', ''),
            execution_time=data.get('execution_time', 0.0),
            timestamp=data.get('timestamp', time.time()),
            findings=data.get('findings', []),
            raw_output=data.get('raw_output', ''),
            parsed_data=data.get('parsed_data', {}),
            error_message=data.get('error_message')
        )

    def invalidate_cache(self, tool_name: Optional[str] = None,
                        target: Optional[str] = None) -> int:
        """
        Invalidate cached results.

        Args:
            tool_name: Tool name pattern (optional)
            target: Target pattern (optional)

        Returns:
            Number of entries invalidated
        """
        if not self.enabled:
            return 0

        pattern = None
        if tool_name and target:
            pattern = f"{tool_name}:{target}:*"
        elif tool_name:
            pattern = f"{tool_name}:*"
        elif target:
            pattern = f"*:{target}:*"

        try:
            keys = self.backend.keys(pattern)
            count = 0

            for key in keys:
                if self.backend.delete(key):
                    count += 1

            self.logger.info(f"Invalidated {count} cache entries")
            return count

        except Exception as e:
            self.logger.error(f"Error invalidating cache: {e}")
            return 0

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            all_keys = self.backend.keys()
            stats = {
                'total_entries': len(all_keys),
                'backend_type': self.backend.__class__.__name__,
                'enabled': self.enabled,
                'default_ttl': self.default_ttl,
                'tool_breakdown': {}
            }

            # Count entries by tool
            for key in all_keys:
                tool_name = key.split(':', 1)[0] if ':' in key else 'unknown'
                stats['tool_breakdown'][tool_name] = stats['tool_breakdown'].get(tool_name, 0) + 1

            return stats

        except Exception as e:
            self.logger.error(f"Error getting cache stats: {e}")
            return {'error': str(e)}

    def clear_cache(self) -> bool:
        """Clear all cached results."""
        if not self.enabled:
            return False

        try:
            success = self.backend.clear()
            if success:
                self.logger.info("Cache cleared successfully")
            return success
        except Exception as e:
            self.logger.error(f"Error clearing cache: {e}")
            return False


# Global cache manager instance
_cache_manager: Optional[CacheManager] = None


def get_cache_manager(backend: Optional[CacheBackend] = None,
                     default_ttl: int = 3600, enabled: bool = True) -> CacheManager:
    """
    Get the global cache manager instance.

    Args:
        backend: Cache backend to use (only used on first call)
        default_ttl: Default TTL in seconds (only used on first call)
        enabled: Whether caching is enabled (only used on first call)

    Returns:
        CacheManager instance
    """
    global _cache_manager

    if _cache_manager is None:
        _cache_manager = CacheManager(backend, default_ttl, enabled)

    return _cache_manager
