"""
Nmap Tool Connector

This module provides integration with Nmap (Network Mapper) for network
discovery and security auditing.
"""

import json
import re
import xml.etree.ElementTree as ET
from typing import Any, Dict, List
from urllib.parse import urlparse

from .base import BaseToolConnector, Severity


class NmapConnector(BaseToolConnector):
    """Connector for Nmap network scanning tool"""
    
    @property
    def tool_name(self) -> str:
        return "nmap"
    
    @property
    def default_command(self) -> str:
        return "nmap"
    
    def build_command(self, target: str, **kwargs) -> List[str]:
        """
        Build Nmap command with specified options.
        
        Args:
            target: Target host/network to scan
            **kwargs: Nmap options
                - scan_type: Type of scan (tcp_syn, tcp_connect, udp, etc.)
                - ports: Port specification (e.g., "1-1000", "80,443,8080")
                - scripts: NSE scripts to run
                - timing: Timing template (0-5)
                - output_format: Output format (xml, json, normal)
                - aggressive: Enable aggressive scanning
                - service_detection: Enable service version detection
                - os_detection: Enable OS detection
                - stealth: Enable stealth scanning options
        """
        cmd = [self.tool_path or self.default_command]
        
        # Scan type options
        scan_type = kwargs.get('scan_type', 'tcp_syn')
        if scan_type == 'tcp_syn':
            cmd.append('-sS')
        elif scan_type == 'tcp_connect':
            cmd.append('-sT')
        elif scan_type == 'udp':
            cmd.append('-sU')
        elif scan_type == 'tcp_ack':
            cmd.append('-sA')
        elif scan_type == 'tcp_window':
            cmd.append('-sW')
        elif scan_type == 'tcp_maimon':
            cmd.append('-sM')
        
        # Port specification
        ports = kwargs.get('ports')
        if ports:
            cmd.extend(['-p', str(ports)])
        
        # Service detection
        if kwargs.get('service_detection', True):
            cmd.append('-sV')
        
        # OS detection
        if kwargs.get('os_detection', False):
            cmd.append('-O')
        
        # Aggressive scanning
        if kwargs.get('aggressive', False):
            cmd.append('-A')
        
        # Timing template
        timing = kwargs.get('timing', 3)
        cmd.append(f'-T{timing}')
        
        # NSE scripts
        scripts = kwargs.get('scripts')
        if scripts:
            if isinstance(scripts, list):
                scripts = ','.join(scripts)
            cmd.extend(['--script', scripts])
        
        # Stealth options
        if kwargs.get('stealth', False):
            cmd.extend(['-f', '-D', 'RND:10'])
        
        # Output format
        output_format = kwargs.get('output_format', 'xml')
        if output_format == 'xml':
            cmd.append('-oX')
            cmd.append('-')  # Output to stdout
        elif output_format == 'json':
            cmd.append('-oJ')
            cmd.append('-')
        else:
            cmd.append('-oN')
            cmd.append('-')
        
        # Additional options
        if kwargs.get('no_ping', False):
            cmd.append('-Pn')
        
        if kwargs.get('no_dns', False):
            cmd.append('-n')
        
        # Target
        cmd.append(target)
        
        return cmd
    
    def parse_output(self, stdout: str, stderr: str) -> Dict[str, Any]:
        """
        Parse Nmap output into standardized format.
        
        Args:
            stdout: Nmap stdout output
            stderr: Nmap stderr output
            
        Returns:
            Parsed data dictionary
        """
        parsed_data = {
            'hosts': [],
            'scan_stats': {},
            'warnings': [],
            'errors': []
        }
        
        # Try to parse XML output first
        if stdout.strip().startswith('<?xml'):
            try:
                parsed_data = self._parse_xml_output(stdout)
            except ET.ParseError:
                # Fall back to text parsing
                parsed_data = self._parse_text_output(stdout)
        else:
            parsed_data = self._parse_text_output(stdout)
        
        # Parse stderr for warnings and errors
        if stderr:
            parsed_data['warnings'].extend(self._extract_warnings(stderr))
            parsed_data['errors'].extend(self._extract_errors(stderr))
        
        return parsed_data
    
    def _parse_xml_output(self, xml_output: str) -> Dict[str, Any]:
        """Parse XML output from Nmap"""
        root = ET.fromstring(xml_output)
        
        parsed_data = {
            'hosts': [],
            'scan_stats': {},
            'warnings': [],
            'errors': []
        }
        
        # Parse scan info
        scaninfo = root.find('scaninfo')
        if scaninfo is not None:
            parsed_data['scan_stats'] = {
                'type': scaninfo.get('type'),
                'protocol': scaninfo.get('protocol'),
                'numservices': scaninfo.get('numservices'),
                'services': scaninfo.get('services')
            }
        
        # Parse hosts
        for host in root.findall('host'):
            host_data = self._parse_host_xml(host)
            if host_data:
                parsed_data['hosts'].append(host_data)
        
        # Parse run stats
        runstats = root.find('runstats')
        if runstats is not None:
            finished = runstats.find('finished')
            if finished is not None:
                parsed_data['scan_stats'].update({
                    'elapsed': finished.get('elapsed'),
                    'summary': finished.get('summary'),
                    'exit': finished.get('exit')
                })
        
        return parsed_data
    
    def _parse_host_xml(self, host_elem) -> Dict[str, Any]:
        """Parse individual host from XML"""
        host_data = {
            'addresses': [],
            'hostnames': [],
            'ports': [],
            'os': {},
            'status': {},
            'scripts': []
        }
        
        # Host status
        status = host_elem.find('status')
        if status is not None:
            host_data['status'] = {
                'state': status.get('state'),
                'reason': status.get('reason')
            }
        
        # Addresses
        for address in host_elem.findall('address'):
            host_data['addresses'].append({
                'addr': address.get('addr'),
                'addrtype': address.get('addrtype')
            })
        
        # Hostnames
        hostnames = host_elem.find('hostnames')
        if hostnames is not None:
            for hostname in hostnames.findall('hostname'):
                host_data['hostnames'].append({
                    'name': hostname.get('name'),
                    'type': hostname.get('type')
                })
        
        # Ports
        ports = host_elem.find('ports')
        if ports is not None:
            for port in ports.findall('port'):
                port_data = self._parse_port_xml(port)
                if port_data:
                    host_data['ports'].append(port_data)
        
        # OS detection
        os_elem = host_elem.find('os')
        if os_elem is not None:
            host_data['os'] = self._parse_os_xml(os_elem)
        
        return host_data
    
    def _parse_port_xml(self, port_elem) -> Dict[str, Any]:
        """Parse individual port from XML"""
        port_data = {
            'portid': port_elem.get('portid'),
            'protocol': port_elem.get('protocol'),
            'state': {},
            'service': {},
            'scripts': []
        }
        
        # Port state
        state = port_elem.find('state')
        if state is not None:
            port_data['state'] = {
                'state': state.get('state'),
                'reason': state.get('reason')
            }
        
        # Service info
        service = port_elem.find('service')
        if service is not None:
            port_data['service'] = {
                'name': service.get('name'),
                'product': service.get('product'),
                'version': service.get('version'),
                'extrainfo': service.get('extrainfo'),
                'method': service.get('method'),
                'conf': service.get('conf')
            }
        
        # Scripts
        for script in port_elem.findall('script'):
            port_data['scripts'].append({
                'id': script.get('id'),
                'output': script.get('output')
            })
        
        return port_data
    
    def _parse_os_xml(self, os_elem) -> Dict[str, Any]:
        """Parse OS detection from XML"""
        os_data = {
            'portused': [],
            'osmatch': [],
            'osfingerprint': []
        }
        
        for portused in os_elem.findall('portused'):
            os_data['portused'].append({
                'state': portused.get('state'),
                'proto': portused.get('proto'),
                'portid': portused.get('portid')
            })
        
        for osmatch in os_elem.findall('osmatch'):
            os_data['osmatch'].append({
                'name': osmatch.get('name'),
                'accuracy': osmatch.get('accuracy'),
                'line': osmatch.get('line')
            })
        
        return os_data
    
    def _parse_text_output(self, text_output: str) -> Dict[str, Any]:
        """Parse text output from Nmap (fallback method)"""
        parsed_data = {
            'hosts': [],
            'scan_stats': {},
            'warnings': [],
            'errors': []
        }
        
        lines = text_output.split('\n')
        current_host = None
        
        for line in lines:
            line = line.strip()
            
            # Host detection
            if 'Nmap scan report for' in line:
                if current_host:
                    parsed_data['hosts'].append(current_host)
                current_host = {
                    'addresses': [],
                    'hostnames': [],
                    'ports': [],
                    'status': {'state': 'up'}
                }
                # Extract hostname/IP
                match = re.search(r'Nmap scan report for (.+)', line)
                if match:
                    target = match.group(1)
                    if '(' in target and ')' in target:
                        # Format: hostname (IP)
                        hostname = target.split('(')[0].strip()
                        ip = target.split('(')[1].split(')')[0].strip()
                        current_host['hostnames'].append({'name': hostname, 'type': 'PTR'})
                        current_host['addresses'].append({'addr': ip, 'addrtype': 'ipv4'})
                    else:
                        # Just IP or hostname
                        current_host['addresses'].append({'addr': target, 'addrtype': 'ipv4'})
            
            # Port detection
            elif current_host and re.match(r'^\d+/(tcp|udp)', line):
                port_match = re.match(r'^(\d+)/(tcp|udp)\s+(\w+)\s+(.+)', line)
                if port_match:
                    port_data = {
                        'portid': port_match.group(1),
                        'protocol': port_match.group(2),
                        'state': {'state': port_match.group(3)},
                        'service': {'name': port_match.group(4).split()[0] if port_match.group(4) else ''}
                    }
                    current_host['ports'].append(port_data)
        
        # Add last host
        if current_host:
            parsed_data['hosts'].append(current_host)
        
        return parsed_data
    
    def _extract_warnings(self, stderr: str) -> List[str]:
        """Extract warnings from stderr"""
        warnings = []
        for line in stderr.split('\n'):
            if 'WARNING' in line.upper():
                warnings.append(line.strip())
        return warnings
    
    def _extract_errors(self, stderr: str) -> List[str]:
        """Extract errors from stderr"""
        errors = []
        for line in stderr.split('\n'):
            if 'ERROR' in line.upper() or 'FAILED' in line.upper():
                errors.append(line.strip())
        return errors
    
    def _extract_findings(self, parsed_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract security findings from Nmap results"""
        findings = []
        
        for host in parsed_data.get('hosts', []):
            host_ip = next((addr['addr'] for addr in host.get('addresses', []) 
                           if addr.get('addrtype') == 'ipv4'), 'unknown')
            
            # Open ports are findings
            for port in host.get('ports', []):
                if port.get('state', {}).get('state') == 'open':
                    service_name = port.get('service', {}).get('name', 'unknown')
                    service_version = port.get('service', {}).get('version', '')
                    
                    finding = {
                        'type': 'open_port',
                        'severity': self._assess_port_severity(port),
                        'host': host_ip,
                        'port': port.get('portid'),
                        'protocol': port.get('protocol'),
                        'service': service_name,
                        'version': service_version,
                        'description': f"Open {port.get('protocol')} port {port.get('portid')} running {service_name}",
                        'recommendation': self._get_port_recommendation(port)
                    }
                    findings.append(finding)
                
                # Script results can indicate vulnerabilities
                for script in port.get('scripts', []):
                    if self._is_vulnerability_script(script.get('id', '')):
                        finding = {
                            'type': 'vulnerability',
                            'severity': Severity.MEDIUM.value,
                            'host': host_ip,
                            'port': port.get('portid'),
                            'script': script.get('id'),
                            'description': f"Potential vulnerability detected by {script.get('id')}",
                            'details': script.get('output', ''),
                            'recommendation': 'Review script output and apply appropriate security measures'
                        }
                        findings.append(finding)
        
        return findings
    
    def _assess_port_severity(self, port: Dict[str, Any]) -> str:
        """Assess the severity of an open port"""
        port_num = int(port.get('portid', 0))
        service_name = port.get('service', {}).get('name', '').lower()
        
        # Critical services
        if service_name in ['telnet', 'ftp', 'rsh', 'rlogin'] or port_num in [23, 21, 514, 513]:
            return Severity.HIGH.value
        
        # Common vulnerable services
        if service_name in ['ssh', 'rdp', 'vnc', 'mysql', 'postgresql'] or port_num in [22, 3389, 5900, 3306, 5432]:
            return Severity.MEDIUM.value
        
        # Web services
        if service_name in ['http', 'https'] or port_num in [80, 443, 8080, 8443]:
            return Severity.LOW.value
        
        return Severity.INFO.value
    
    def _get_port_recommendation(self, port: Dict[str, Any]) -> str:
        """Get security recommendation for an open port"""
        service_name = port.get('service', {}).get('name', '').lower()
        
        recommendations = {
            'telnet': 'Replace Telnet with SSH for secure remote access',
            'ftp': 'Use SFTP or FTPS instead of plain FTP',
            'ssh': 'Ensure SSH is properly configured with key-based authentication',
            'http': 'Consider using HTTPS instead of HTTP',
            'mysql': 'Ensure database is not accessible from untrusted networks',
            'postgresql': 'Ensure database is not accessible from untrusted networks'
        }
        
        return recommendations.get(service_name, 'Review if this service needs to be publicly accessible')
    
    def _is_vulnerability_script(self, script_id: str) -> bool:
        """Check if a script ID indicates a vulnerability check"""
        vuln_keywords = ['vuln', 'cve', 'exploit', 'backdoor', 'malware', 'trojan']
        return any(keyword in script_id.lower() for keyword in vuln_keywords)
    
    def validate_target(self, target: str) -> bool:
        """Validate Nmap target format"""
        if not target or not target.strip():
            return False
        
        # Basic validation for IP addresses, hostnames, and CIDR notation
        import socket
        
        try:
            # Try to parse as IP address
            socket.inet_aton(target.split('/')[0])
            return True
        except socket.error:
            pass
        
        # Check if it's a valid hostname
        if re.match(r'^[a-zA-Z0-9.-]+$', target):
            return True
        
        return False

    def _generate_mock_output(self, target: str, **kwargs) -> Dict[str, Any]:
        """Generate realistic mock Nmap output for testing/demo purposes."""
        ports = kwargs.get('ports', '80,443')
        port_list = []

        # Parse port specification
        if isinstance(ports, str):
            if '-' in ports:
                # Range like "1-1000"
                start, end = map(int, ports.split('-'))
                port_list = list(range(start, min(end + 1, start + 10)))  # Limit for demo
            else:
                # Comma-separated like "80,443,8080"
                port_list = [int(p.strip()) for p in ports.split(',')]

        # Generate mock XML output
        xml_output = f'''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE nmaprun>
<nmaprun scanner="nmap" args="nmap -sS -p {ports} -sV -T4 -oX - {target}" start="1642680000" startstr="Thu Jan 20 12:00:00 2022" version="7.92" xmloutputversion="1.05">
<scaninfo type="syn" protocol="tcp" numservices="{len(port_list)}" services="{ports}"/>
<verbose level="0"/>
<debugging level="0"/>
<host starttime="1642680000" endtime="1642680010">
<status state="up" reason="echo-reply" reason_ttl="64"/>
<address addr="{target}" addrtype="ipv4"/>
<hostnames>
<hostname name="{target}" type="PTR"/>
</hostnames>
<ports>'''

        # Add mock ports
        for port in port_list[:5]:  # Limit to first 5 ports for demo
            service_name = self._get_mock_service_name(port)
            state = "open" if port in [22, 80, 443] else "closed"

            xml_output += f'''
<port protocol="tcp" portid="{port}">
<state state="{state}" reason="syn-ack" reason_ttl="64"/>
<service name="{service_name}" product="Mock Service" version="1.0" method="probed" conf="10"/>
</port>'''

        xml_output += '''
</ports>
<times srtt="1000" rttvar="1000" to="100000"/>
</host>
<runstats>
<finished time="1642680010" timestr="Thu Jan 20 12:00:10 2022" elapsed="10.00" summary="Nmap done at Thu Jan 20 12:00:10 2022; 1 IP address (1 host up) scanned in 10.00 seconds" exit="success"/>
<hosts up="1" down="0" total="1"/>
</runstats>
</nmaprun>'''

        # Generate mock findings
        findings = []
        for port in port_list[:3]:  # Generate findings for first 3 ports
            if port in [22, 80, 443]:  # Only open ports
                service_name = self._get_mock_service_name(port)
                findings.append({
                    'type': 'open_port',
                    'severity': self._assess_port_severity({'portid': str(port), 'service': {'name': service_name}}),
                    'host': target,
                    'port': str(port),
                    'protocol': 'tcp',
                    'service': service_name,
                    'version': 'Mock Service 1.0',
                    'description': f"Open tcp port {port} running {service_name}",
                    'recommendation': self._get_port_recommendation({'service': {'name': service_name}})
                })

        return {
            'stdout': xml_output,
            'stderr': '',
            'findings': findings
        }

    def _get_mock_service_name(self, port: int) -> str:
        """Get mock service name for a port."""
        service_map = {
            22: 'ssh',
            23: 'telnet',
            25: 'smtp',
            53: 'domain',
            80: 'http',
            110: 'pop3',
            143: 'imap',
            443: 'https',
            993: 'imaps',
            995: 'pop3s',
            3306: 'mysql',
            5432: 'postgresql',
            8080: 'http-proxy',
            8443: 'https-alt'
        }
        return service_map.get(port, 'unknown')
