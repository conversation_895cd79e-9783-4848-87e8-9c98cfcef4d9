#!/usr/bin/env python3
"""
Integration Examples for Tool Connectors Framework

This module provides comprehensive examples showing how to integrate the 
Tool Connectors Framework with broader ethical hacking AI systems, including:
- REST API endpoints
- Real-time monitoring
- Automated reporting
- AI-powered analysis
- SIEM integration
- Production deployment patterns
"""

import sys
import time
import json
import asyncio
from pathlib import Path
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# Add the parent directory to the path so we can import tool_connectors
sys.path.insert(0, str(Path(__file__).parent.parent))

from tool_connectors import ToolManager
from tool_connectors.security import SecurityPolicy, SecurityViolation


def demo_rest_api_integration():
    """Demonstrate REST API integration."""
    print("=== REST API Integration Demo ===")
    
    try:
        from flask import Flask, request, jsonify
        
        app = Flask(__name__)
        manager = ToolManager(mock_mode=True, secure_mode=True, monitoring_enabled=True)
        
        @app.route('/api/scan', methods=['POST'])
        def execute_scan():
            """Execute a security scan via API."""
            try:
                data = request.get_json()
                
                # Validate input
                tool = manager.validate_input(data.get('tool', ''), 'tool')
                target = manager.validate_input(data.get('target', ''), 'target')
                
                # Check authorization
                if not manager.is_command_allowed(f"{tool} {target}"):
                    return jsonify({'error': 'Command not authorized'}), 403
                
                # Execute scan
                result = manager.execute_tool(tool, target, **data.get('params', {}))
                
                return jsonify({
                    'status': result.status.value,
                    'tool': result.tool_name,
                    'execution_time': result.execution_time,
                    'findings_count': len(result.findings),
                    'findings': result.findings[:10],  # Limit for API response
                    'timestamp': result.timestamp
                })
                
            except SecurityViolation as e:
                return jsonify({'error': f'Security violation: {e.violation_type}'}), 400
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @app.route('/api/status', methods=['GET'])
        def get_status():
            """Get system status and health."""
            return jsonify({
                'security': manager.get_security_status(),
                'health': manager.get_health_status(),
                'resources': manager.get_resource_usage(),
                'monitoring': manager.get_monitoring_summary()
            })
        
        print("Flask REST API endpoints defined:")
        print("  POST /api/scan - Execute security scan")
        print("  GET /api/status - Get system status")
        print("  (Run with: app.run(debug=False, host='0.0.0.0', port=5000))")
        
    except ImportError:
        print("Flask not available - install with: pip install flask")
    
    print()


def demo_workflow_orchestration():
    """Demonstrate advanced workflow orchestration."""
    print("=== Workflow Orchestration Demo ===")
    
    manager = ToolManager(mock_mode=True, secure_mode=True)
    
    # Define comprehensive security assessment workflow
    security_assessment_workflow = {
        "name": "comprehensive_security_assessment",
        "description": "Full security assessment with multiple tools",
        "steps": [
            {
                "name": "network_discovery",
                "tool": "nmap",
                "target": "demo-target.com",
                "parameters": {"scan_type": "discovery", "ports": "1-1000"},
                "timeout": 300
            },
            {
                "name": "port_scan",
                "tool": "nmap",
                "target": "demo-target.com",
                "parameters": {"scan_type": "tcp", "ports": "1-65535"},
                "depends_on": ["network_discovery"],
                "timeout": 600
            },
            {
                "name": "web_scan",
                "tool": "nikto",
                "target": "http://demo-target.com",
                "depends_on": ["port_scan"],
                "timeout": 900
            },
            {
                "name": "directory_enumeration",
                "tool": "gobuster",
                "target": "http://demo-target.com",
                "parameters": {
                    "wordlist": "/usr/share/wordlists/dirb/common.txt",
                    "extensions": "php,html,txt,js"
                },
                "depends_on": ["web_scan"],
                "timeout": 1200
            }
        ]
    }
    
    print("Executing comprehensive security assessment workflow...")
    
    try:
        # Execute the workflow
        result = manager.workflow_engine.execute_workflow(security_assessment_workflow)
        
        print(f"Workflow completed:")
        print(f"  Name: {result.workflow_name}")
        print(f"  Status: {result.status.value}")
        print(f"  Execution time: {result.execution_time:.2f}s")
        print(f"  Total steps: {len(result.step_results)}")
        print(f"  Failed steps: {len(result.failed_steps)}")
        print(f"  Total findings: {result.total_findings}")
        
        # Generate workflow report
        report = manager.generate_workflow_report(result, 'html', 'security_assessment')
        print(f"  Generated workflow report ({len(report)} characters)")
        
    except Exception as e:
        print(f"Workflow execution failed: {e}")
    
    print()


def demo_parallel_execution():
    """Demonstrate parallel execution of security scans."""
    print("=== Parallel Execution Demo ===")
    
    manager = ToolManager(mock_mode=True, secure_mode=True)
    
    async def parallel_security_scan(targets):
        """Execute security scans on multiple targets in parallel."""
        
        def scan_target(target):
            """Scan a single target."""
            try:
                # Network scan
                nmap_result = manager.execute_tool('nmap', target, scan_type='tcp')
                
                # Web scan (if web ports found)
                web_results = []
                if any(f.get('port') in [80, 443, 8080] for f in nmap_result.findings):
                    nikto_result = manager.execute_tool('nikto', f'http://{target}')
                    web_results = [nikto_result]
                
                return {
                    'target': target,
                    'network_scan': nmap_result,
                    'web_scans': web_results,
                    'total_findings': len(nmap_result.findings) + sum(len(r.findings) for r in web_results)
                }
                
            except Exception as e:
                return {
                    'target': target,
                    'error': str(e)
                }
        
        # Execute scans in parallel
        with ThreadPoolExecutor(max_workers=3) as executor:
            loop = asyncio.get_event_loop()
            tasks = [
                loop.run_in_executor(executor, scan_target, target)
                for target in targets
            ]
            
            results = await asyncio.gather(*tasks)
        
        return results
    
    # Execute parallel scans
    targets = ['demo1.com', 'demo2.com', 'demo3.com']
    print(f"Executing parallel scans on {len(targets)} targets...")
    
    try:
        results = asyncio.run(parallel_security_scan(targets))
        
        print("Parallel scan results:")
        for result in results:
            if 'error' in result:
                print(f"  {result['target']}: ERROR - {result['error']}")
            else:
                print(f"  {result['target']}: {result['total_findings']} findings")
        
    except Exception as e:
        print(f"Parallel execution failed: {e}")
    
    print()


def demo_security_integration():
    """Demonstrate advanced security integration."""
    print("=== Security Integration Demo ===")
    
    # Create custom security policy for high-security environment
    high_security_policy = SecurityPolicy(
        max_input_length=500,
        allowed_characters=r'[a-zA-Z0-9\.\-_]+',  # More restrictive
        blocked_patterns=[
            r'[;&|`$(){}[\]\\]',  # Shell metacharacters
            r'\.\./|\.\.\\',       # Directory traversal
            r'rm\s+',             # Any rm command
            r'sudo|su\s',         # Privilege escalation
            r'nc\s+',             # Netcat
            r'bash|sh\s+',        # Shell execution
        ],
        allowed_commands={'nmap', 'nikto'},  # Only specific tools
        command_timeout=120,  # Shorter timeout
        max_memory_mb=512,    # Lower memory limit
        max_cpu_percent=50,   # Lower CPU limit
        use_sandbox=True,
        log_all_commands=True,
        monitor_resources=True,
        alert_on_violations=True
    )
    
    # Apply custom policy
    manager = ToolManager(
        mock_mode=True,
        secure_mode=True,
        security_policy=high_security_policy
    )
    
    print("High-security policy applied:")
    security_status = manager.get_security_status()
    for key, value in security_status.items():
        print(f"  {key}: {value}")
    
    # Test security validation
    print("\nTesting security validation:")
    
    test_inputs = [
        ("valid_target.com", "Valid target"),
        ("target.com; rm -rf /", "Command injection attempt"),
        ("../../../etc/passwd", "Directory traversal attempt"),
        ("a" * 600, "Input too long")
    ]
    
    for test_input, description in test_inputs:
        try:
            validated = manager.validate_input(test_input, "target")
            print(f"  ✓ {description}: ALLOWED - '{validated}'")
        except SecurityViolation as e:
            print(f"  ✗ {description}: BLOCKED - {e.violation_type}")
        except Exception as e:
            print(f"  ? {description}: ERROR - {e}")
    
    # Test command authorization
    print("\nTesting command authorization:")
    test_commands = [
        "nmap -sS example.com",
        "nikto -h http://example.com",
        "rm -rf /tmp",
        "gobuster dir -u http://example.com"
    ]
    
    for command in test_commands:
        allowed = manager.is_command_allowed(command)
        status = "ALLOWED" if allowed else "BLOCKED"
        icon = "✓" if allowed else "✗"
        print(f"  {icon} '{command}' -> {status}")
    
    print()


def demo_monitoring_integration():
    """Demonstrate monitoring and reporting integration."""
    print("=== Monitoring Integration Demo ===")
    
    manager = ToolManager(
        mock_mode=True,
        secure_mode=True,
        monitoring_enabled=True
    )
    
    # Execute some tools to generate monitoring data
    print("Executing tools to generate monitoring data...")
    
    tools_and_targets = [
        ('nmap', 'monitor-demo1.com'),
        ('nikto', 'monitor-demo2.com'),
        ('gobuster', 'monitor-demo3.com')
    ]
    
    results = []
    for tool, target in tools_and_targets:
        result = manager.execute_tool(tool, target)
        results.append(result)
        print(f"  Executed {tool} on {target}: {result.status.value}")
    
    # Get monitoring data
    print("\nMonitoring data:")
    
    # Health status
    health = manager.get_health_status()
    print(f"  Overall health: {health['overall_status']}")
    
    # Performance metrics
    performance = manager.get_performance_metrics()
    if 'total_executions' in performance:
        print(f"  Total executions: {performance['total_executions']}")
        print(f"  Average execution time: {performance['execution_time']['avg']:.3f}s")
        print(f"  Cache hit rate: {performance['cache_hit_rate']:.1%}")
    
    # Resource usage
    resources = manager.get_resource_usage()
    if 'cpu_percent' in resources:
        print(f"  CPU usage: {resources['cpu_percent']:.1f}%")
        print(f"  Memory usage: {resources['memory_percent']:.1f}%")
    
    # Generate monitoring dashboard
    dashboard = manager.get_monitoring_dashboard()
    print(f"\nGenerated monitoring dashboard ({len(dashboard)} characters)")
    
    # Generate comprehensive report
    report = manager.generate_advanced_report(
        results, 'html', 'security_assessment',
        metadata_overrides={
            'title': 'Monitoring Integration Demo Report',
            'description': 'Demonstration of monitoring integration capabilities'
        }
    )
    
    print(f"Generated comprehensive report ({len(report)} characters)")
    
    print()


def demo_custom_reporting():
    """Demonstrate custom reporting capabilities."""
    print("=== Custom Reporting Demo ===")
    
    manager = ToolManager(mock_mode=True)
    
    # Execute some tools
    results = [
        manager.execute_tool('nmap', 'report-demo.com'),
        manager.execute_tool('nikto', 'report-demo.com')
    ]
    
    print(f"Generated {len(results)} scan results")
    
    # Create custom report sections
    custom_sections = [
        manager.create_report_section(
            "Executive Summary",
            "This assessment identified several areas requiring attention. "
            "The target system shows signs of outdated software and potential misconfigurations.",
            data={
                "Assessment Date": datetime.now().strftime("%Y-%m-%d"),
                "Target Systems": "report-demo.com",
                "Tools Used": "nmap, nikto",
                "Overall Risk Rating": "MEDIUM",
                "Critical Issues": 0,
                "High Issues": 1,
                "Medium Issues": 3,
                "Low Issues": 5
            }
        ),
        manager.create_report_section(
            "Recommendations",
            "Based on the assessment findings, the following recommendations should be implemented:",
            data={
                "Immediate Actions": "Update web server software, implement security headers",
                "Short-term (1-3 months)": "Conduct penetration testing, implement WAF",
                "Long-term (3-6 months)": "Security awareness training, regular vulnerability assessments"
            }
        )
    ]
    
    print(f"Created {len(custom_sections)} custom report sections")
    
    # Generate reports in different formats
    formats = ['html', 'json', 'xml', 'csv']
    
    print("\nGenerating reports in multiple formats:")
    for fmt in formats:
        report_content = manager.generate_advanced_report(
            results, fmt,
            metadata_overrides={
                'title': f'Custom Report Demo - {fmt.upper()}',
                'description': 'Demonstration of custom reporting capabilities'
            },
            custom_sections=custom_sections
        )
        print(f"  {fmt.upper()}: {len(report_content)} characters")
    
    print()


def demo_production_configuration():
    """Demonstrate production configuration patterns."""
    print("=== Production Configuration Demo ===")
    
    # Production security policy
    production_policy = SecurityPolicy(
        max_input_length=500,
        allowed_characters=r'[a-zA-Z0-9\.\-_:/]+',
        blocked_patterns=[
            r'[;&|`$(){}[\]\\]',
            r'\.\./|\.\.\\',
            r'rm\s+',
            r'sudo|su\s',
            r'nc\s+',
            r'bash|sh\s+'
        ],
        allowed_commands={'nmap', 'nikto', 'gobuster', 'sqlmap'},
        command_timeout=300,
        max_memory_mb=1024,
        max_cpu_percent=70,
        use_sandbox=True,
        restrict_filesystem=True,
        log_all_commands=True,
        monitor_resources=True,
        alert_on_violations=True
    )
    
    print("Production security policy configured:")
    print(f"  Max input length: {production_policy.max_input_length}")
    print(f"  Command timeout: {production_policy.command_timeout}s")
    print(f"  Max memory: {production_policy.max_memory_mb}MB")
    print(f"  Allowed commands: {', '.join(production_policy.allowed_commands)}")
    print(f"  Sandbox enabled: {production_policy.use_sandbox}")
    print(f"  Resource monitoring: {production_policy.monitor_resources}")
    
    # Initialize production manager
    manager = ToolManager(
        mock_mode=True,  # Would be False in real production
        secure_mode=True,
        security_policy=production_policy,
        monitoring_enabled=True,
        log_level='INFO',
        cache_enabled=True,
        cache_backend='sqlite'
    )
    
    print("\nProduction manager initialized with:")
    print(f"  Security enabled: {manager.secure_mode}")
    print(f"  Monitoring enabled: {manager.monitoring is not None}")
    print(f"  Cache enabled: {manager.cache_manager is not None}")
    
    # Add production health checks
    def check_disk_space():
        """Check if sufficient disk space is available."""
        try:
            import shutil
            total, used, free = shutil.disk_usage('/')
            return (free / total) > 0.1  # At least 10% free
        except:
            return True  # Assume OK if can't check
    
    def check_tool_availability():
        """Check if all required tools are available."""
        tools = ['nmap', 'nikto', 'gobuster', 'sqlmap']
        return all(manager.get_connector(tool) is not None for tool in tools)
    
    manager.add_health_check("disk_space", check_disk_space, "Sufficient disk space")
    manager.add_health_check("tool_availability", check_tool_availability, "All tools available")
    
    print("\nAdded production health checks:")
    health_status = manager.get_health_status()
    print(f"  Overall health: {health_status['overall_status']}")
    for name, check in health_status['checks'].items():
        status_icon = "✓" if check['status'] == 'healthy' else "⚠" if check['status'] == 'degraded' else "✗"
        print(f"  {status_icon} {name}: {check['status']}")
    
    print()


def main():
    """Main function to run all integration demos."""
    print("Tool Connectors Framework - Integration Examples")
    print("=" * 70)
    print("This demo shows comprehensive integration examples including:")
    print("- REST API endpoints")
    print("- Workflow orchestration")
    print("- Parallel execution")
    print("- Security integration")
    print("- Monitoring and reporting")
    print("- Custom reporting")
    print("- Production configuration")
    print()
    
    try:
        demo_rest_api_integration()
        demo_workflow_orchestration()
        demo_parallel_execution()
        demo_security_integration()
        demo_monitoring_integration()
        demo_custom_reporting()
        demo_production_configuration()
        
        print("=== Integration Examples Summary ===")
        print("All integration examples completed successfully!")
        print("The framework provides comprehensive integration capabilities:")
        print("- RESTful API endpoints for external system integration")
        print("- Advanced workflow orchestration with dependencies")
        print("- Parallel execution for improved performance")
        print("- Comprehensive security controls and policies")
        print("- Real-time monitoring and health checks")
        print("- Flexible reporting with multiple formats")
        print("- Production-ready configuration patterns")
        print("- Extensible architecture for custom integrations")
        
    except Exception as e:
        print(f"Error during integration demos: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
