<report>
  <metadata>
    <title>Security Scan Report</title>
    <description>Automated security scan results</description>
    <author>Tool Connectors Framework</author>
    <created_at>2025-07-24T07:38:41.288114</created_at>
    <version>1.0</version>
  </metadata>
  <sections>
    <section>
      <title>Tool Execution Analysis</title>
      <content>Executed 4 tools with an average execution time of 1.06 seconds.</content>
      <data>
        <total_tools>4</total_tools>
        <successful_tools>4</successful_tools>
        <failed_tools>0</failed_tools>
        <success_rate>100.0</success_rate>
        <total_time>4.222268104553223</total_time>
        <avg_time>1.0555670261383057</avg_time>
        <fastest_tool>sqlmap</fastest_tool>
        <slowest_tool>nikto</slowest_tool>
      </data>
    </section>
    <section>
      <title>Findings Analysis</title>
      <content>Discovered 2 findings across 1 tools.</content>
      <data>
        <total_findings>2</total_findings>
        <affected_tools>1</affected_tools>
        <severity_distribution>{'low': 2}</severity_distribution>
        <finding_types>{'open_port': 2}</finding_types>
        <tools_with_findings>['nmap']</tools_with_findings>
      </data>
    </section>
    <section>
      <title>Risk Assessment</title>
      <content>Overall risk level: MINIMAL</content>
      <data>
        <overall_risk>MINIMAL</overall_risk>
        <risk_score>2</risk_score>
        <critical_findings>0</critical_findings>
        <high_findings>0</high_findings>
        <medium_findings>0</medium_findings>
        <low_findings>2</low_findings>
        <info_findings>0</info_findings>
      </data>
    </section>
  </sections>
  <results>
    <tool_result>
      <tool_name>nmap</tool_name>
      <command>nmap -sS -sV -T3 -oX - demo.example.com</command>
      <status>completed</status>
      <exit_code>0</exit_code>
      <execution_time>1.0729119777679443</execution_time>
      <timestamp>1753335516.6007316</timestamp>
      <findings>
        <finding id="0">
          <type>open_port</type>
          <severity>low</severity>
          <host>demo.example.com</host>
          <port>80</port>
          <protocol>tcp</protocol>
          <service>http</service>
          <version>1.0</version>
          <description>Open tcp port 80 running http</description>
          <recommendation>Consider using HTTPS instead of HTTP</recommendation>
        </finding>
        <finding id="1">
          <type>open_port</type>
          <severity>low</severity>
          <host>demo.example.com</host>
          <port>443</port>
          <protocol>tcp</protocol>
          <service>https</service>
          <version>1.0</version>
          <description>Open tcp port 443 running https</description>
          <recommendation>Review if this service needs to be publicly accessible</recommendation>
        </finding>
      </findings>
    </tool_result>
    <tool_result>
      <tool_name>nikto</tool_name>
      <command>nikto -h demo.example.com -timeout 10 -nointeractive</command>
      <status>completed</status>
      <exit_code>0</exit_code>
      <execution_time>1.1165378093719482</execution_time>
      <timestamp>1753335517.735193</timestamp>
    </tool_result>
    <tool_result>
      <tool_name>sqlmap</tool_name>
      <command>sqlmap -u demo.example.com --threads 1 --level 1 --risk 1 --batch --output-dir /tmp/sqlmap --flush-session</command>
      <status>completed</status>
      <exit_code>0</exit_code>
      <execution_time>1.014922857284546</execution_time>
      <timestamp>1753335519.2318454</timestamp>
    </tool_result>
    <tool_result>
      <tool_name>gobuster</tool_name>
      <command>gobuster dir -u demo.example.com -w /usr/share/wordlists/dirb/common.txt -x php,html,txt -t 10 --timeout 10s -q</command>
      <status>completed</status>
      <exit_code>0</exit_code>
      <execution_time>1.0178954601287842</execution_time>
      <timestamp>1753335520.2514174</timestamp>
    </tool_result>
  </results>
  <summary>
    <total_tools_executed>4</total_tools_executed>
    <total_findings>2</total_findings>
    <status_distribution>
      <completed>4</completed>
    </status_distribution>
    <severity_distribution>
      <low>2</low>
    </severity_distribution>
    <tool_distribution>
      <nmap>1</nmap>
      <nikto>1</nikto>
      <sqlmap>1</sqlmap>
      <gobuster>1</gobuster>
    </tool_distribution>
    <execution_time_total>4.222268104553223</execution_time_total>
    <execution_time_average>1.0555670261383057</execution_time_average>
  </summary>
</report>
