"""
Tool Connectors Module

This module provides a unified interface for integrating various security tools
into the Ethical Hacking AI system. It standardizes tool execution, output parsing,
and error handling across different security tools.

Supported Tools:
- Nmap (network scanning)
- OpenVAS (vulnerability scanning)  
- SQLmap (SQL injection testing)
- Nik<PERSON> (web server scanning)
- Go<PERSON> (directory/file brute forcing)
"""

from .base import BaseToolConnector, ToolResult, ToolError
from .nmap_connector import NmapConnector
from .openvas_connector import OpenVASConnector
from .sqlmap_connector import SQLmapConnector
from .nikto_connector import NiktoConnector
from .gobuster_connector import GobusterConnector
from .manager import ToolManager

__version__ = "1.0.0"
__author__ = "Ethical Hacking AI Project"

__all__ = [
    "BaseToolConnector",
    "ToolResult", 
    "ToolError",
    "NmapConnector",
    "OpenVASConnector", 
    "SQLmapConnector",
    "NiktoConnector",
    "GobusterConnector",
    "ToolManager"
]
