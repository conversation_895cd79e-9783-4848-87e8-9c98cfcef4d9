"""
OpenVAS Tool Connector

This module provides integration with OpenVAS for comprehensive vulnerability scanning.
Note: OpenVAS requires a running OpenVAS server and uses the GMP (Greenbone Management Protocol).
"""

import json
import re
import xml.etree.ElementTree as ET
from typing import Any, Dict, List, Optional

from .base import BaseToolConnector, Severity


class OpenVASConnector(BaseToolConnector):
    """Connector for OpenVAS vulnerability scanner"""
    
    def __init__(self, tool_path: str = None, timeout: int = 3600,
                 host: str = 'localhost', port: int = 9390,
                 username: str = None, password: str = None,
                 mock_mode: bool = False, mock_data: Optional[Dict[str, Any]] = None,
                 secure_mode: bool = True):
        """
        Initialize OpenVAS connector.

        Args:
            tool_path: Path to gvm-cli tool
            timeout: Default timeout (OpenVAS scans can take long)
            host: OpenVAS server host
            port: OpenVAS server port
            username: OpenVAS username
            password: OpenVAS password
            mock_mode: Enable mock mode for testing/demo purposes
            mock_data: Mock data to return when in mock mode
            secure_mode: Enable secure execution mode
        """
        super().__init__(tool_path, timeout, mock_mode, mock_data, secure_mode)
        self.host = host
        self.port = port
        self.username = username
        self.password = password
    
    @property
    def tool_name(self) -> str:
        return "openvas"
    
    @property
    def default_command(self) -> str:
        return "gvm-cli"
    
    def build_command(self, target: str, **kwargs) -> List[str]:
        """
        Build OpenVAS command using gvm-cli.
        
        Args:
            target: Target host/network to scan
            **kwargs: OpenVAS options
                - scan_config: Scan configuration ID
                - port_list: Port list ID
                - scanner: Scanner ID
                - alive_test: Alive test method
        """
        cmd = [self.tool_path or self.default_command]
        
        # Connection parameters
        cmd.extend(['--hostname', self.host])
        cmd.extend(['--port', str(self.port)])
        
        if self.username:
            cmd.extend(['--username', self.username])
        if self.password:
            cmd.extend(['--password', self.password])
        
        # Use socket connection for GMP
        cmd.append('socket')
        
        # For this implementation, we'll use a simplified approach
        # In a real implementation, you would need to:
        # 1. Create a target
        # 2. Create a task
        # 3. Start the task
        # 4. Wait for completion
        # 5. Get the report
        
        # This is a placeholder command structure
        cmd.extend(['--xml', f'<get_version/>'])
        
        return cmd
    
    def parse_output(self, stdout: str, stderr: str) -> Dict[str, Any]:
        """
        Parse OpenVAS output into standardized format.
        
        Args:
            stdout: OpenVAS stdout output
            stderr: OpenVAS stderr output
            
        Returns:
            Parsed data dictionary
        """
        parsed_data = {
            'scan_id': '',
            'target': '',
            'vulnerabilities': [],
            'host_details': [],
            'scan_info': {},
            'warnings': [],
            'errors': []
        }
        
        # Try to parse XML output
        if stdout.strip().startswith('<'):
            try:
                parsed_data = self._parse_xml_report(stdout)
            except ET.ParseError as e:
                self.logger.error(f"Failed to parse XML output: {e}")
                parsed_data['errors'].append(f"XML parsing error: {e}")
        else:
            # Parse text output
            parsed_data = self._parse_text_output(stdout)
        
        # Parse stderr for warnings and errors
        if stderr:
            for line in stderr.split('\n'):
                if 'WARNING' in line.upper():
                    parsed_data['warnings'].append(line.strip())
                elif 'ERROR' in line.upper():
                    parsed_data['errors'].append(line.strip())
        
        return parsed_data
    
    def _parse_xml_report(self, xml_output: str) -> Dict[str, Any]:
        """Parse XML report from OpenVAS"""
        root = ET.fromstring(xml_output)
        
        parsed_data = {
            'scan_id': '',
            'target': '',
            'vulnerabilities': [],
            'host_details': [],
            'scan_info': {},
            'warnings': [],
            'errors': []
        }
        
        # Extract scan information
        report = root.find('.//report')
        if report is not None:
            parsed_data['scan_id'] = report.get('id', '')
        
        # Extract results (vulnerabilities)
        for result in root.findall('.//result'):
            vuln = self._parse_vulnerability_xml(result)
            if vuln:
                parsed_data['vulnerabilities'].append(vuln)
        
        # Extract host information
        for host in root.findall('.//host'):
            host_info = self._parse_host_xml(host)
            if host_info:
                parsed_data['host_details'].append(host_info)
        
        return parsed_data
    
    def _parse_vulnerability_xml(self, result_elem) -> Dict[str, Any]:
        """Parse individual vulnerability from XML"""
        vuln = {
            'id': result_elem.get('id', ''),
            'name': '',
            'description': '',
            'severity': '',
            'cvss_score': 0.0,
            'host': '',
            'port': '',
            'nvt_oid': '',
            'cve_refs': [],
            'solution': '',
            'impact': ''
        }
        
        # Extract basic information
        name_elem = result_elem.find('name')
        if name_elem is not None:
            vuln['name'] = name_elem.text or ''
        
        description_elem = result_elem.find('description')
        if description_elem is not None:
            vuln['description'] = description_elem.text or ''
        
        # Extract host information
        host_elem = result_elem.find('host')
        if host_elem is not None:
            vuln['host'] = host_elem.text or ''
        
        port_elem = result_elem.find('port')
        if port_elem is not None:
            vuln['port'] = port_elem.text or ''
        
        # Extract severity and CVSS
        severity_elem = result_elem.find('severity')
        if severity_elem is not None:
            try:
                vuln['cvss_score'] = float(severity_elem.text or 0)
                vuln['severity'] = self._cvss_to_severity(vuln['cvss_score'])
            except ValueError:
                vuln['cvss_score'] = 0.0
                vuln['severity'] = Severity.INFO.value
        
        # Extract NVT OID
        nvt_elem = result_elem.find('nvt')
        if nvt_elem is not None:
            vuln['nvt_oid'] = nvt_elem.get('oid', '')
        
        return vuln
    
    def _parse_host_xml(self, host_elem) -> Dict[str, Any]:
        """Parse host information from XML"""
        host_info = {
            'ip': '',
            'hostname': '',
            'os': '',
            'ports': []
        }
        
        # Extract IP
        ip_elem = host_elem.find('ip')
        if ip_elem is not None:
            host_info['ip'] = ip_elem.text or ''
        
        # Extract hostname
        hostname_elem = host_elem.find('hostname')
        if hostname_elem is not None:
            host_info['hostname'] = hostname_elem.text or ''
        
        return host_info
    
    def _parse_text_output(self, text_output: str) -> Dict[str, Any]:
        """Parse text output from OpenVAS (fallback method)"""
        parsed_data = {
            'scan_id': '',
            'target': '',
            'vulnerabilities': [],
            'host_details': [],
            'scan_info': {},
            'warnings': [],
            'errors': []
        }
        
        lines = text_output.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # Extract basic information from text output
            if 'Target:' in line:
                target_match = re.search(r'Target:\s+(.+)', line)
                if target_match:
                    parsed_data['target'] = target_match.group(1)
            
            # Look for vulnerability indicators
            if any(keyword in line.lower() for keyword in ['vulnerability', 'cve-', 'critical', 'high']):
                # This is a simplified parsing - real implementation would be more complex
                vuln = {
                    'name': line,
                    'description': line,
                    'severity': Severity.MEDIUM.value,
                    'host': parsed_data.get('target', ''),
                    'cvss_score': 5.0
                }
                parsed_data['vulnerabilities'].append(vuln)
        
        return parsed_data
    
    def _cvss_to_severity(self, cvss_score: float) -> str:
        """Convert CVSS score to severity level"""
        if cvss_score >= 9.0:
            return Severity.CRITICAL.value
        elif cvss_score >= 7.0:
            return Severity.HIGH.value
        elif cvss_score >= 4.0:
            return Severity.MEDIUM.value
        elif cvss_score > 0.0:
            return Severity.LOW.value
        else:
            return Severity.INFO.value
    
    def _extract_findings(self, parsed_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract security findings from OpenVAS results"""
        findings = []
        
        for vuln in parsed_data.get('vulnerabilities', []):
            finding = {
                'type': 'vulnerability',
                'severity': vuln.get('severity', Severity.INFO.value),
                'host': vuln.get('host'),
                'port': vuln.get('port'),
                'name': vuln.get('name'),
                'description': vuln.get('description'),
                'cvss_score': vuln.get('cvss_score'),
                'nvt_oid': vuln.get('nvt_oid'),
                'cve_refs': vuln.get('cve_refs', []),
                'solution': vuln.get('solution', ''),
                'recommendation': vuln.get('solution') or 'Apply security patches and follow vendor recommendations'
            }
            findings.append(finding)
        
        return findings
    
    def validate_target(self, target: str) -> bool:
        """Validate OpenVAS target format"""
        if not target or not target.strip():
            return False
        
        # OpenVAS can scan IP addresses, hostnames, and CIDR ranges
        import socket
        
        try:
            # Try to parse as IP address
            socket.inet_aton(target.split('/')[0])
            return True
        except socket.error:
            pass
        
        # Check if it's a valid hostname
        if re.match(r'^[a-zA-Z0-9.-]+$', target):
            return True
        
        return False
