# Tool Connectors Framework

A comprehensive, production-ready framework for integrating and orchestrating ethical hacking and security assessment tools with enterprise-grade features including advanced caching, real-time monitoring, comprehensive security controls, and automated reporting.

## 🚀 Features

### Core Capabilities
- **Multi-tool Integration**: Support for popular security tools (Nmap, Nikto, Gobuster, SQLMap, OpenVAS)
- **Workflow Orchestration**: Complex multi-step security assessments with dependencies and parallel execution
- **Plugin Architecture**: Extensible design for custom tool integrations
- **Mock Mode**: Testing and demonstration without actual tool execution

### Enterprise Features
- **Advanced Caching**: Multiple cache backends (Memory, File, SQLite) with intelligent invalidation
- **Comprehensive Security**: Input validation, command injection prevention, sandboxing, and resource limits
- **Real-time Monitoring**: Performance metrics, health checks, resource monitoring, and live dashboards
- **Professional Reporting**: Multiple output formats (HTML, JSON, XML, CSV) with customizable templates
- **Configuration Management**: Flexible YAML-based configuration with environment-specific settings

### Security & Compliance
- **Sandboxed Execution**: Isolated execution environments with filesystem and network restrictions
- **Input Validation**: Comprehensive input sanitization and command injection prevention
- **Resource Limits**: CPU, memory, and execution time controls
- **Audit Logging**: Complete audit trail of all tool executions and security events
- **Access Controls**: Role-based access and command authorization

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Tool Manager  │────│  Security Layer │────│ Monitoring Core │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Workflow Engine │    │ Cache Manager   │    │ Report Generator│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Tool Connectors                              │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │  Nmap   │ │ Nikto   │ │Gobuster │ │ SQLMap  │ │ OpenVAS │   │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘ └─────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start

### Basic Usage

```python
from tool_connectors import ToolManager

# Initialize with security and monitoring enabled
manager = ToolManager(
    secure_mode=True,
    monitoring_enabled=True,
    cache_enabled=True
)

# Execute a single tool
result = manager.execute_tool('nmap', 'example.com', scan_type='tcp')
print(f"Status: {result.status.value}")
print(f"Findings: {len(result.findings)}")

# Generate professional report
report = manager.generate_advanced_report([result], 'html', 'security_assessment')
manager.save_advanced_report([result], 'security_report.html', 'html')
```

### Workflow Orchestration

```python
# Define complex security assessment workflow
workflow_config = {
    "name": "comprehensive_security_assessment",
    "steps": [
        {
            "name": "network_discovery",
            "tool": "nmap",
            "target": "example.com",
            "parameters": {"scan_type": "discovery"}
        },
        {
            "name": "web_vulnerability_scan",
            "tool": "nikto",
            "target": "http://example.com",
            "depends_on": ["network_discovery"],
            "condition": "network_discovery.has_web_ports"
        },
        {
            "name": "directory_enumeration",
            "tool": "gobuster",
            "target": "http://example.com",
            "depends_on": ["web_vulnerability_scan"],
            "parallel": True
        }
    ]
}

# Execute workflow
result = manager.workflow_engine.execute_workflow(workflow_config)
print(f"Workflow completed: {result.status.value}")
print(f"Total findings: {result.total_findings}")

# Generate workflow report
report = manager.generate_workflow_report(result, 'html')
```

### Security Configuration

```python
from tool_connectors.security import SecurityPolicy

# Create custom security policy
security_policy = SecurityPolicy(
    command_timeout=300,
    max_memory_mb=1024,
    use_sandbox=True,
    allowed_commands={'nmap', 'nikto', 'gobuster'},
    log_all_commands=True
)

# Initialize with custom security
manager = ToolManager(
    secure_mode=True,
    security_policy=security_policy
)

# Validate inputs
try:
    validated_target = manager.validate_input("example.com", "target")
    result = manager.execute_tool('nmap', validated_target)
except SecurityViolation as e:
    print(f"Security violation: {e.violation_type}")
```

## 📦 Installation

### Requirements

- Python 3.8+
- Security tools (nmap, nikto, gobuster, sqlmap) - optional for mock mode
- Additional dependencies in `requirements.txt`

### Install Dependencies

```bash
# Install Python dependencies
pip install -r requirements.txt

# Install security tools (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install nmap nikto gobuster sqlmap

# Install security tools (macOS with Homebrew)
brew install nmap nikto gobuster sqlmap
```

### Development Setup

```bash
# Clone repository
git clone <repository-url>
cd tool-connectors-framework

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run tests
python -m pytest tests/ -v

# Run examples
python examples/basic_usage.py
```
result = manager.execute_tool('nmap', 'scanme.nmap.org', ports='80,443')

print(f"Status: {result.status.value}")
print(f"Findings: {len(result.findings)}")
```

### Using Individual Connectors

```python
from tool_connectors import NmapConnector

# Create connector
nmap = NmapConnector()

# Execute scan
result = nmap.execute(
    'scanme.nmap.org',
    scan_type='tcp_syn',
    ports='1-1000',
    service_detection=True,
    timing=4
)

# Access results
for finding in result.findings:
    print(f"Found: {finding['description']}")
```

### Asynchronous Execution

```python
import asyncio
from tool_connectors import ToolManager

async def async_scan():
    manager = ToolManager()
    
    # Execute multiple tools concurrently
    tools = ['nmap', 'nikto']
    results = await manager.execute_multiple_tools_async(
        tools, 
        'example.com'
    )
    
    return results

# Run async scan
results = asyncio.run(async_scan())
```

### Predefined Workflows

```python
from tool_connectors import ToolManager

manager = ToolManager()

# Execute comprehensive scan workflow
results = manager.execute_scan_workflow(
    'example.com', 
    workflow_type='comprehensive'
)

# Generate report
aggregated = manager.aggregate_findings(results)
report = manager.generate_report(results, format='json')
```

## Configuration

### Tool-Specific Configuration

Each tool connector supports specific configuration options:

#### Nmap Configuration

```python
nmap_config = {
    'scan_type': 'tcp_syn',        # tcp_syn, tcp_connect, udp, etc.
    'ports': '1-65535',            # Port specification
    'service_detection': True,      # Enable service detection
    'os_detection': False,         # Enable OS detection
    'scripts': ['vuln', 'safe'],   # NSE scripts to run
    'timing': 4,                   # Timing template (0-5)
    'stealth': False,              # Enable stealth options
    'output_format': 'xml'         # Output format
}

result = nmap.execute('target.com', **nmap_config)
```

#### SQLmap Configuration

```python
sqlmap_config = {
    'data': 'param1=value1',       # POST data
    'cookie': 'session=abc123',    # Cookie string
    'level': 3,                    # Test level (1-5)
    'risk': 2,                     # Risk level (1-3)
    'technique': 'BEUSTQ',         # SQL injection techniques
    'threads': 5,                  # Number of threads
    'batch': True                  # Non-interactive mode
}

result = sqlmap.execute('http://target.com/page.php?id=1', **sqlmap_config)
```

### Workflow Configuration

```python
# Available workflow types
workflows = {
    'comprehensive': ['nmap', 'nikto', 'gobuster', 'sqlmap'],
    'web': ['nikto', 'gobuster', 'sqlmap'],
    'network': ['nmap', 'openvas'],
    'quick': ['nmap', 'nikto']
}
```

## Architecture

### Base Classes

- **BaseToolConnector**: Abstract base class for all tool connectors
- **ToolResult**: Standardized result structure
- **ToolManager**: Unified interface for managing multiple tools

### Data Structures

```python
@dataclass
class ToolResult:
    tool_name: str
    command: str
    status: ToolStatus
    exit_code: Optional[int]
    stdout: str
    stderr: str
    execution_time: float
    findings: List[Dict[str, Any]]
    parsed_data: Dict[str, Any]
    error_message: Optional[str]
```

### Status Enumeration

```python
class ToolStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"
```

## Examples

See the `examples/` directory for comprehensive usage examples:

- `basic_usage.py`: Basic framework usage
- `advanced_workflows.py`: Advanced scanning workflows
- `custom_connectors.py`: Creating custom tool connectors

## Testing

Run the test suite:

```bash
# Run all tests
python -m pytest tests/

# Run specific test file
python -m pytest tests/test_base_connector.py

# Run with coverage
python -m pytest tests/ --cov=tool_connectors
```

## Development

### Adding New Tool Connectors

1. Create a new connector class inheriting from `BaseToolConnector`
2. Implement required abstract methods
3. Add parsing logic for tool-specific output
4. Register the connector in `ToolManager`

Example:

```python
from tool_connectors.base import BaseToolConnector

class MyToolConnector(BaseToolConnector):
    @property
    def tool_name(self) -> str:
        return "mytool"
    
    @property
    def default_command(self) -> str:
        return "mytool"
    
    def build_command(self, target: str, **kwargs) -> List[str]:
        return [self.default_command, target]
    
    def parse_output(self, stdout: str, stderr: str) -> Dict[str, Any]:
        # Implement parsing logic
        return {"parsed": "data"}
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests for new functionality
5. Submit a pull request

## Security Considerations

- **Validation**: All inputs are validated before execution
- **Sandboxing**: Tools run with limited privileges
- **Timeout Management**: Prevents hanging processes
- **Error Handling**: Comprehensive error handling and logging
- **Safe Defaults**: Conservative default configurations

## Troubleshooting

### Common Issues

1. **Tool Not Found**: Ensure tools are installed and in PATH
2. **Permission Denied**: Some tools require elevated privileges
3. **Timeout Errors**: Increase timeout values for slow targets
4. **Parse Errors**: Check tool output format compatibility

### Logging

Enable debug logging for troubleshooting:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## License

This project is part of the Ethical Hacking AI system and follows the same licensing terms.

## Roadmap

- [ ] Additional tool integrations (Burp Suite, OWASP ZAP)
- [ ] Enhanced reporting capabilities
- [ ] Web-based management interface
- [ ] Integration with vulnerability databases
- [ ] Machine learning-based result analysis

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review existing issues on GitHub
3. Create a new issue with detailed information
