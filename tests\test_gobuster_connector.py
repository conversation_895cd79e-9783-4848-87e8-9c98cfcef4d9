"""
Unit tests for GobusterConnector.
"""

import unittest
from unittest.mock import patch, Mock

from tool_connectors.gobuster_connector import GobusterConnector
from tool_connectors.base import Tool<PERSON>tatus, Severity


class TestGobusterConnector(unittest.TestCase):
    """Test cases for GobusterConnector"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.connector = GobusterConnector(mock_mode=True)
    
    def test_initialization(self):
        """Test connector initialization"""
        self.assertEqual(self.connector.tool_name, "gobuster")
        self.assertEqual(self.connector.default_command, "gobuster")
        self.assertTrue(self.connector.mock_mode)
    
    def test_build_command_basic(self):
        """Test basic command building"""
        cmd = self.connector.build_command("http://example.com")
        
        self.assertIn("gobuster", cmd[0])
        self.assertIn("dir", cmd)  # Default mode
        self.assertIn("-u", cmd)
        url_index = cmd.index("-u")
        self.assertEqual(cmd[url_index + 1], "http://example.com")
    
    def test_build_command_with_mode(self):
        """Test command building with different modes"""
        # Directory mode
        cmd = self.connector.build_command("http://example.com", mode="dir")
        self.assertIn("dir", cmd)
        
        # DNS mode
        cmd = self.connector.build_command("example.com", mode="dns")
        self.assertIn("dns", cmd)
        
        # VHost mode
        cmd = self.connector.build_command("http://example.com", mode="vhost")
        self.assertIn("vhost", cmd)
    
    def test_build_command_with_wordlist(self):
        """Test command building with wordlist"""
        cmd = self.connector.build_command(
            "http://example.com",
            wordlist="/usr/share/wordlists/dirb/common.txt"
        )
        
        self.assertIn("-w", cmd)
        wordlist_index = cmd.index("-w")
        self.assertEqual(cmd[wordlist_index + 1], "/usr/share/wordlists/dirb/common.txt")
    
    def test_build_command_with_extensions(self):
        """Test command building with file extensions"""
        # Single extension
        cmd = self.connector.build_command("http://example.com", extensions="php")
        self.assertIn("-x", cmd)
        ext_index = cmd.index("-x")
        self.assertEqual(cmd[ext_index + 1], "php")
        
        # Multiple extensions
        cmd = self.connector.build_command("http://example.com", extensions=["php", "html", "txt"])
        ext_index = cmd.index("-x")
        self.assertEqual(cmd[ext_index + 1], "php,html,txt")
    
    def test_build_command_with_threads(self):
        """Test command building with thread count"""
        cmd = self.connector.build_command("http://example.com", threads=20)
        
        self.assertIn("-t", cmd)
        threads_index = cmd.index("-t")
        self.assertEqual(cmd[threads_index + 1], "20")
    
    def test_build_command_with_timeout(self):
        """Test command building with timeout"""
        cmd = self.connector.build_command("http://example.com", timeout=30)
        
        self.assertIn("--timeout", cmd)
        timeout_index = cmd.index("--timeout")
        self.assertEqual(cmd[timeout_index + 1], "30s")
    
    def test_build_command_with_user_agent(self):
        """Test command building with custom user agent"""
        user_agent = "Mozilla/5.0 (Test Browser)"
        cmd = self.connector.build_command("http://example.com", user_agent=user_agent)
        
        self.assertIn("-a", cmd)
        ua_index = cmd.index("-a")
        self.assertEqual(cmd[ua_index + 1], user_agent)
    
    def test_build_command_with_cookies(self):
        """Test command building with cookies"""
        cookies = "PHPSESSID=abc123; user=admin"
        cmd = self.connector.build_command("http://example.com", cookies=cookies)
        
        self.assertIn("-c", cmd)
        cookies_index = cmd.index("-c")
        self.assertEqual(cmd[cookies_index + 1], cookies)
    
    def test_build_command_with_auth(self):
        """Test command building with basic authentication"""
        cmd = self.connector.build_command(
            "http://example.com",
            username="admin",
            password="secret"
        )
        
        self.assertIn("-U", cmd)
        username_index = cmd.index("-U")
        self.assertEqual(cmd[username_index + 1], "admin")
        
        self.assertIn("-P", cmd)
        password_index = cmd.index("-P")
        self.assertEqual(cmd[password_index + 1], "secret")
    
    def test_build_command_with_status_codes(self):
        """Test command building with status codes"""
        cmd = self.connector.build_command(
            "http://example.com",
            status_codes="200,204,301,302,307,401,403"
        )
        
        self.assertIn("-s", cmd)
        status_index = cmd.index("-s")
        self.assertEqual(cmd[status_index + 1], "200,204,301,302,307,401,403")
    
    def test_build_command_with_exclude_status(self):
        """Test command building with excluded status codes"""
        cmd = self.connector.build_command(
            "http://example.com",
            exclude_status="404,500"
        )
        
        self.assertIn("-b", cmd)
        exclude_index = cmd.index("-b")
        self.assertEqual(cmd[exclude_index + 1], "404,500")
    
    def test_validate_target(self):
        """Test target validation"""
        # Valid URLs
        self.assertTrue(self.connector.validate_target("http://example.com"))
        self.assertTrue(self.connector.validate_target("https://example.com"))

        # Invalid targets (requires full URL with scheme)
        self.assertFalse(self.connector.validate_target("example.com"))
        self.assertFalse(self.connector.validate_target(""))
        self.assertFalse(self.connector.validate_target(None))
    
    def test_parse_output_basic(self):
        """Test basic output parsing functionality"""
        # Test that parse_output method exists and returns a dict
        stdout = "Mock Gobuster output"
        stderr = ""
        
        result = self.connector.parse_output(stdout, stderr)
        self.assertIsInstance(result, dict)
    
    def test_extract_findings(self):
        """Test findings extraction"""
        parsed_data = {
            'target': 'http://example.com',
            'found_paths': [
                {'path': '/admin', 'status': '200', 'size': '1024'},
                {'path': '/login.php', 'status': '200', 'size': '2048'},
                {'path': '/backup', 'status': '403', 'size': '512'}
            ]
        }
        
        findings = self.connector._extract_findings(parsed_data)
        
        self.assertEqual(len(findings), 3)
        
        # Check finding structure
        for finding in findings:
            self.assertIn('type', finding)
            self.assertIn('severity', finding)
            self.assertIn('target', finding)
            self.assertIn('description', finding)
            self.assertEqual(finding['type'], 'discovered_path')
            self.assertEqual(finding['target'], 'http://example.com')
    
    def test_assess_path_severity(self):
        """Test path severity assessment"""
        # High severity paths
        self.assertEqual(self.connector._assess_path_severity('/admin', 200), Severity.HIGH.value)
        self.assertEqual(self.connector._assess_path_severity('/config.php', 200), Severity.HIGH.value)

        # Medium severity paths
        self.assertEqual(self.connector._assess_path_severity('/login', 200), Severity.MEDIUM.value)

        # Low severity paths
        self.assertEqual(self.connector._assess_path_severity('/images', 200), Severity.LOW.value)

        # Forbidden paths (403 status doesn't change severity, just adds note)
        self.assertEqual(self.connector._assess_path_severity('/secret', 403), Severity.INFO.value)
    
    def test_get_path_recommendation(self):
        """Test path recommendations"""
        rec = self.connector._get_path_recommendation('/admin', 200)
        self.assertIn('sensitive', rec.lower())

        rec = self.connector._get_path_recommendation('/backup.sql', 200)
        self.assertIn('sensitive', rec.lower())
    
    def test_mock_execution(self):
        """Test mock execution"""
        result = self.connector.execute("http://example.com", mode="dir", threads=10)
        
        self.assertEqual(result.status, ToolStatus.COMPLETED)
        self.assertEqual(result.tool_name, "gobuster")
        self.assertGreater(result.execution_time, 0)
        self.assertIsInstance(result.findings, list)


if __name__ == '__main__':
    unittest.main()
