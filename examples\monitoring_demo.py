#!/usr/bin/env python3
"""
Monitoring and Logging Demo

This script demonstrates the comprehensive monitoring and logging capabilities 
of the tool connector framework. It shows how to:
1. Use structured logging with JSON output
2. Collect and analyze performance metrics
3. Set up health checks and monitoring
4. Generate monitoring dashboards
5. Integrate monitoring with tool execution
"""

import sys
import time
from pathlib import Path

# Add the parent directory to the path so we can import tool_connectors
sys.path.insert(0, str(Path(__file__).parent.parent))

from tool_connectors import ToolManager
from tool_connectors.monitoring import (
    StructuredLogger, MetricsCollector, HealthMonitor, PerformanceMonitor,
    MonitoringDashboard, MonitoringManager, HealthCheck
)


def demo_structured_logging():
    """Demonstrate structured logging capabilities"""
    print("=== Structured Logging Demo ===")
    
    # Create logger with file output
    logger = StructuredLogger(
        "demo_logger", 
        log_level="DEBUG",
        log_file="./demo_logs.log"
    )
    
    print("Created structured logger with file output")
    
    # Log different types of messages
    logger.debug("Debug message for troubleshooting", 
                component="demo", action="debug_test")
    
    logger.info("Application started successfully", 
               version="1.0.0", startup_time=2.5)
    
    logger.warning("High memory usage detected", 
                  memory_percent=85.2, threshold=80.0)
    
    logger.error("Failed to connect to database", 
                error_code="DB001", retry_count=3)
    
    logger.critical("System overload detected", 
                   cpu_percent=98.5, load_average=5.2)
    
    print("Logged messages at different levels with structured data")
    print("Check './demo_logs.log' for JSON-formatted log entries")
    print()


def demo_metrics_collection():
    """Demonstrate metrics collection"""
    print("=== Metrics Collection Demo ===")
    
    metrics = MetricsCollector(max_metrics=1000)
    
    # Record different types of metrics
    print("Recording various metrics...")
    
    # Counter metrics
    for i in range(10):
        metrics.increment_counter("http_requests", 1, 
                                {"endpoint": "/api/scan", "method": "POST"})
        metrics.increment_counter("http_requests", 1, 
                                {"endpoint": "/api/status", "method": "GET"})
    
    # Gauge metrics
    metrics.set_gauge("active_connections", 25, {"server": "web1"})
    metrics.set_gauge("memory_usage_percent", 67.3, {"server": "web1"})
    metrics.set_gauge("cpu_usage_percent", 45.8, {"server": "web1"})
    
    # Histogram metrics (response times)
    response_times = [0.1, 0.2, 0.15, 0.3, 0.25, 0.18, 0.22, 0.35, 0.12, 0.28]
    for rt in response_times:
        metrics.record_histogram("response_time", rt, 
                               {"service": "scan_api", "endpoint": "/scan"})
    
    # Get metrics summary
    summary = metrics.get_summary()
    print(f"Total metrics recorded: {summary['total_metrics']}")
    print(f"Counters: {len(summary['counters'])}")
    print(f"Gauges: {len(summary['gauges'])}")
    print(f"Histograms: {len(summary['histogram_counts'])}")
    
    # Get recent metrics
    recent_metrics = metrics.get_metrics(since=time.time() - 60)
    print(f"Recent metrics (last minute): {len(recent_metrics)}")
    
    # Show some example metrics
    print("\nExample metrics:")
    for metric in recent_metrics[:5]:
        print(f"  {metric.name}: {metric.value} {metric.unit} (tags: {metric.tags})")
    
    print()


def demo_health_monitoring():
    """Demonstrate health monitoring"""
    print("=== Health Monitoring Demo ===")
    
    health = HealthMonitor()
    
    # Add custom health checks
    def database_check():
        # Simulate database connectivity check
        import random
        return random.choice([True, True, True, False])  # 75% success rate
    
    def api_endpoint_check():
        # Simulate API endpoint check
        return True
    
    def disk_space_check():
        # Simulate disk space check
        import random
        return random.randint(0, 100) < 95  # Fail if "usage" > 95%
    
    # Add custom checks
    health.add_check(HealthCheck(
        name="database_connectivity",
        check_function=database_check,
        description="Database connection is healthy",
        timeout=5.0,
        critical=True
    ))
    
    health.add_check(HealthCheck(
        name="api_endpoint",
        check_function=api_endpoint_check,
        description="API endpoint is responding",
        timeout=3.0,
        critical=False
    ))
    
    health.add_check(HealthCheck(
        name="custom_disk_space",
        check_function=disk_space_check,
        description="Sufficient disk space available",
        timeout=2.0,
        critical=True
    ))
    
    print("Added custom health checks")
    
    # Run all health checks
    print("Running all health checks...")
    results = health.run_all_checks()
    
    print(f"Overall health status: {results['overall_status'].upper()}")
    print("Individual check results:")
    
    for name, result in results['checks'].items():
        status_icon = "✓" if result['status'] == 'healthy' else "⚠" if result['status'] == 'degraded' else "✗"
        critical_marker = " [CRITICAL]" if result['critical'] else ""
        print(f"  {status_icon} {name}: {result['status']}{critical_marker}")
        print(f"    {result['description']}")
        print(f"    Execution time: {result['execution_time']:.3f}s")
    
    print()


def demo_performance_monitoring():
    """Demonstrate performance monitoring"""
    print("=== Performance Monitoring Demo ===")
    
    metrics = MetricsCollector()
    performance = PerformanceMonitor(metrics)
    
    # Simulate tool executions
    tools = ['nmap', 'nikto', 'sqlmap', 'gobuster']
    targets = ['example.com', 'test.com', 'demo.com']
    
    print("Simulating tool executions...")
    
    for i in range(15):
        tool = tools[i % len(tools)]
        target = targets[i % len(targets)]
        
        # Start monitoring
        context = performance.start_execution(tool, target)
        
        # Simulate execution time
        execution_time = 0.1 + (i * 0.05)  # Varying execution times
        time.sleep(execution_time)
        
        # Create mock result
        from tool_connectors.base import ToolResult, ToolStatus
        import random
        
        status = ToolStatus.COMPLETED if random.random() > 0.1 else ToolStatus.FAILED
        findings_count = random.randint(0, 5) if status == ToolStatus.COMPLETED else 0
        
        result = ToolResult(
            tool_name=tool,
            command=f"{tool} {target}",
            status=status,
            findings=[{"finding": f"result_{j}"} for j in range(findings_count)]
        )
        
        # End monitoring
        performance.end_execution(context, result, cache_hit=random.random() < 0.3)
    
    print(f"Completed {i+1} simulated executions")
    
    # Get performance summary
    summary = performance.get_performance_summary()
    
    print(f"\nPerformance Summary (last 24 hours):")
    print(f"  Total executions: {summary['total_executions']}")
    print(f"  Average execution time: {summary['execution_time']['avg']:.3f}s")
    print(f"  Min/Max execution time: {summary['execution_time']['min']:.3f}s / {summary['execution_time']['max']:.3f}s")
    print(f"  95th percentile: {summary['execution_time']['p95']:.3f}s")
    print(f"  Cache hit rate: {summary['cache_hit_rate']:.1%}")
    print(f"  Total findings: {summary['findings']['total']}")
    print(f"  Average findings per execution: {summary['findings']['avg']:.1f}")
    
    print("  Status distribution:")
    for status, count in summary['status_distribution'].items():
        print(f"    {status}: {count}")
    
    # Tool-specific summary
    print(f"\nNmap-specific performance:")
    nmap_summary = performance.get_performance_summary(tool_name='nmap')
    if 'total_executions' in nmap_summary:
        print(f"  Nmap executions: {nmap_summary['total_executions']}")
        print(f"  Nmap avg time: {nmap_summary['execution_time']['avg']:.3f}s")
    
    print()


def demo_monitoring_dashboard():
    """Demonstrate monitoring dashboard"""
    print("=== Monitoring Dashboard Demo ===")
    
    # Create monitoring components
    metrics = MetricsCollector()
    health = HealthMonitor()
    performance = PerformanceMonitor(metrics)
    dashboard = MonitoringDashboard(metrics, health, performance)
    
    # Add some sample data
    metrics.increment_counter("dashboard_demo", 5)
    metrics.set_gauge("demo_gauge", 42.0)
    
    # Add a custom health check
    health.add_check(HealthCheck(
        name="demo_service",
        check_function=lambda: True,
        description="Demo service is running"
    ))
    
    print("Generated monitoring dashboard:")
    print()
    dashboard.print_dashboard()
    
    # Save dashboard to file
    dashboard_file = "./monitoring_dashboard.txt"
    dashboard.save_dashboard(dashboard_file)
    print(f"\nDashboard saved to: {dashboard_file}")
    
    print()


def demo_tool_manager_integration():
    """Demonstrate monitoring integration with ToolManager"""
    print("=== ToolManager Monitoring Integration Demo ===")
    
    # Create ToolManager with monitoring enabled
    manager = ToolManager(
        mock_mode=True,
        monitoring_enabled=True,
        log_level="INFO"
    )
    
    print("Created ToolManager with monitoring enabled")
    
    # Execute some tools to generate monitoring data
    tools_and_targets = [
        ('nmap', 'monitor-demo.com'),
        ('nikto', 'monitor-demo.com'),
        ('sqlmap', 'monitor-demo.com'),
        ('gobuster', 'monitor-demo.com'),
        ('nmap', 'test.monitor-demo.com')
    ]
    
    print("Executing tools to generate monitoring data...")
    
    for tool, target in tools_and_targets:
        result = manager.execute_tool(tool, target)
        print(f"  {tool} on {target}: {result.status.value}")
        time.sleep(0.1)  # Small delay between executions
    
    # Show monitoring dashboard
    print("\nCurrent monitoring dashboard:")
    print("-" * 50)
    manager.print_monitoring_dashboard()
    
    # Get health status
    print("Health Status:")
    health = manager.get_health_status()
    print(f"  Overall: {health['overall_status']}")
    
    # Get performance metrics
    print("\nPerformance Metrics:")
    perf = manager.get_performance_metrics()
    if 'total_executions' in perf:
        print(f"  Total executions: {perf['total_executions']}")
        print(f"  Average execution time: {perf['execution_time']['avg']:.3f}s")
        print(f"  Cache hit rate: {perf['cache_hit_rate']:.1%}")
    
    # Add custom health check
    def custom_service_check():
        return True
    
    manager.add_health_check(
        "demo_service_check",
        custom_service_check,
        "Demo service is operational"
    )
    
    print("\nAdded custom health check")
    
    # Save monitoring dashboard
    dashboard_file = "./tool_manager_dashboard.txt"
    manager.save_monitoring_dashboard(dashboard_file)
    print(f"Dashboard saved to: {dashboard_file}")
    
    print()


def demo_monitoring_manager():
    """Demonstrate MonitoringManager"""
    print("=== MonitoringManager Demo ===")
    
    # Create monitoring manager
    monitoring = MonitoringManager(log_level="DEBUG", log_file="./monitoring_demo.log")
    
    print("Created MonitoringManager with file logging")
    
    # Simulate tool execution monitoring
    context = monitoring.start_tool_execution("demo_tool", "demo.target.com")
    
    # Simulate some work
    time.sleep(0.2)
    
    # Create result
    from tool_connectors.base import ToolResult, ToolStatus
    result = ToolResult(
        tool_name="demo_tool",
        command="demo_tool demo.target.com",
        status=ToolStatus.COMPLETED,
        findings=[{"type": "demo", "value": "test_finding"}]
    )
    
    # End monitoring
    perf_metrics = monitoring.end_tool_execution(context, result)
    
    print(f"Monitored execution:")
    print(f"  Tool: {perf_metrics.tool_name}")
    print(f"  Target: {perf_metrics.target}")
    print(f"  Execution time: {perf_metrics.execution_time:.3f}s")
    print(f"  Findings: {perf_metrics.findings_count}")
    
    # Get comprehensive summary
    summary = monitoring.get_monitoring_summary()
    print(f"\nMonitoring summary:")
    print(f"  Health checks: {summary['health']['total_checks']}")
    print(f"  Total metrics: {summary['metrics']['total_metrics']}")
    print(f"  Performance executions: {summary['performance']['total_executions']}")
    
    print()


def main():
    """Main function to run all monitoring demos"""
    print("Tool Connectors Framework - Monitoring and Logging Demo")
    print("=" * 70)
    print("This demo shows comprehensive monitoring capabilities including:")
    print("- Structured logging with JSON output")
    print("- Performance metrics collection and analysis")
    print("- Health checks and system monitoring")
    print("- Real-time monitoring dashboards")
    print("- Integration with tool execution")
    print()
    
    try:
        demo_structured_logging()
        demo_metrics_collection()
        demo_health_monitoring()
        demo_performance_monitoring()
        demo_monitoring_dashboard()
        demo_tool_manager_integration()
        demo_monitoring_manager()
        
        print("=== Monitoring Demo Summary ===")
        print("All monitoring demos completed successfully!")
        print("The monitoring system provides:")
        print("- Structured logging with multiple output formats")
        print("- Comprehensive metrics collection (counters, gauges, histograms)")
        print("- Flexible health check system with custom checks")
        print("- Performance monitoring with detailed statistics")
        print("- Real-time dashboards and reporting")
        print("- Seamless integration with tool execution")
        print("- Configurable alerting and monitoring policies")
        
        print("\nGenerated files:")
        print("- ./demo_logs.log - Structured log output")
        print("- ./monitoring_dashboard.txt - Sample dashboard")
        print("- ./tool_manager_dashboard.txt - ToolManager dashboard")
        print("- ./monitoring_demo.log - MonitoringManager logs")
        
    except Exception as e:
        print(f"Error during demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
