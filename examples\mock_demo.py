#!/usr/bin/env python3
"""
Mock Mode Demo for Tool Connectors

This script demonstrates the mock mode functionality of the tool connector framework,
allowing for testing and demonstration without requiring actual security tools to be installed.
"""

import asyncio
import json
import logging
import sys
from pathlib import Path

# Add the parent directory to the path so we can import tool_connectors
sys.path.insert(0, str(Path(__file__).parent.parent))

from tool_connectors import ToolManager, NmapConnector
from tool_connectors.base import ToolStatus


def setup_logging():
    """Set up logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def demo_mock_single_tool():
    """Demonstrate mock mode with a single tool"""
    print("=== Mock Mode - Single Tool Demo ===")
    
    # Create Nmap connector in mock mode
    nmap = NmapConnector(mock_mode=True)
    
    target = "example.com"
    print(f"Mock scanning {target} with Nmap...")
    
    result = nmap.execute(target, ports="22,80,443,8080", timing=4)
    
    print(f"Status: {result.status.value}")
    print(f"Execution time: {result.execution_time:.2f} seconds")
    print(f"Findings: {len(result.findings)}")
    
    if result.findings:
        print("\nMock Findings:")
        for finding in result.findings:
            print(f"  - {finding.get('description', 'No description')}")
            print(f"    Severity: {finding.get('severity', 'unknown')}")
            print(f"    Port: {finding.get('port', 'N/A')}")
    
    return result


def demo_mock_tool_manager():
    """Demonstrate mock mode with tool manager"""
    print("\n=== Mock Mode - Tool Manager Demo ===")
    
    # Create tool manager in mock mode
    manager = ToolManager(mock_mode=True)
    
    # Show tool status
    print("Tool Status:")
    status = manager.get_tool_status()
    for tool_name, info in status.items():
        print(f"  {tool_name}: Available={info['available']}, Mock={info['mock_mode']}")
    
    # Execute a single tool
    target = "testsite.com"
    print(f"\nMock scanning {target} with Nmap through manager...")
    
    result = manager.execute_tool('nmap', target, ports="1-100", timing=5)
    
    print(f"Status: {result.status.value}")
    print(f"Findings: {len(result.findings)}")
    
    return result


async def demo_mock_async_execution():
    """Demonstrate async mock execution"""
    print("\n=== Mock Mode - Async Execution Demo ===")
    
    manager = ToolManager(mock_mode=True)
    target = "asynctest.com"
    
    # Execute multiple tools asynchronously
    tools = ['nmap', 'nikto']
    tool_configs = {
        'nmap': {
            'ports': '22,80,443',
            'timing': 4,
            'service_detection': True,
            'mock_delay': 2.0  # Simulate 2 second execution
        },
        'nikto': {
            'timeout': 15,
            'mock_delay': 1.5  # Simulate 1.5 second execution
        }
    }
    
    print(f"Running async mock scan on {target}...")
    results = await manager.execute_multiple_tools_async(tools, target, tool_configs)
    
    for tool_name, result in results.items():
        print(f"{tool_name}: {result.status.value} ({len(result.findings)} findings)")
    
    return results


def demo_mock_workflow():
    """Demonstrate mock workflow execution"""
    print("\n=== Mock Mode - Workflow Demo ===")
    
    manager = ToolManager(mock_mode=True)
    target = "workflow-test.com"
    
    # Execute a quick workflow
    print(f"Running mock quick workflow on {target}...")
    results = manager.execute_scan_workflow(target, workflow_type='quick')
    
    # Generate aggregated report
    aggregated = manager.aggregate_findings(results)
    
    print(f"Total findings: {aggregated['total_findings']}")
    print("Findings by severity:")
    for severity, count in aggregated['findings_by_severity'].items():
        if count > 0:
            print(f"  {severity.upper()}: {count}")
    
    print("\nExecution summary:")
    for tool, summary in aggregated['execution_summary'].items():
        print(f"  {tool}: {summary['status']} ({summary['execution_time']:.2f}s)")
    
    return results, aggregated


def demo_mixed_mode():
    """Demonstrate mixed mode (some tools in mock, others real)"""
    print("\n=== Mixed Mode Demo ===")
    
    # Create manager without global mock mode
    manager = ToolManager(mock_mode=False)
    
    # Enable mock mode for specific tools
    nmap_connector = manager.get_connector('nmap')
    if nmap_connector:
        nmap_connector.enable_mock_mode()
    
    # Show which tools are available
    available_tools = manager.get_available_tools_detailed()
    print("Available tools:")
    for tool_name, info in available_tools.items():
        mode = "Mock" if info['mock_mode'] else "Real"
        print(f"  {tool_name}: {mode} mode")
    
    # Execute scan
    target = "mixed-test.com"
    result = manager.execute_tool('nmap', target, ports="80,443")
    
    print(f"\nScan result: {result.status.value}")
    print(f"Findings: {len(result.findings)}")
    
    return result


def save_mock_results(results, filename="mock_results.json"):
    """Save mock results to file"""
    print(f"\n=== Saving Mock Results to {filename} ===")
    
    # Convert results to serializable format
    serializable_results = {}
    
    if isinstance(results, dict):
        for tool_name, result in results.items():
            serializable_results[tool_name] = {
                'tool_name': result.tool_name,
                'command': result.command,
                'status': result.status.value,
                'exit_code': result.exit_code,
                'execution_time': result.execution_time,
                'findings': result.findings,
                'mock_mode': True
            }
    
    with open(filename, 'w') as f:
        json.dump(serializable_results, f, indent=2)
    
    print(f"Mock results saved to {filename}")


def main():
    """Main function to run all mock demos"""
    setup_logging()
    
    print("Tool Connectors Framework - Mock Mode Demo")
    print("=" * 50)
    print("This demo shows how the framework works without requiring actual security tools.")
    print()
    
    try:
        # Run synchronous demos
        single_result = demo_mock_single_tool()
        manager_result = demo_mock_tool_manager()
        
        # Run asynchronous demo
        async_results = asyncio.run(demo_mock_async_execution())
        
        # Run workflow demo
        workflow_results, aggregated = demo_mock_workflow()
        
        # Run mixed mode demo
        mixed_result = demo_mixed_mode()
        
        # Save results
        if workflow_results:
            save_mock_results(workflow_results, "mock_workflow_results.json")
        
        print("\n=== Mock Demo Summary ===")
        print("All mock demos completed successfully!")
        print("Mock mode allows testing and demonstration without installing security tools.")
        print("The framework generates realistic mock data for development and testing purposes.")
        
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    except Exception as e:
        print(f"\nError during demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
