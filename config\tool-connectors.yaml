# Tool Connectors Configuration File
# This file contains configuration settings for the tool connector framework

# General settings
general:
  # Enable mock mode for testing/demo purposes
  mock_mode: false
  
  # Maximum number of tools to run concurrently
  max_concurrent_tools: 5
  
  # Directory to store scan results
  output_directory: "./scan_results"
  
  # Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
  log_level: "INFO"

# Tool-specific configurations
tools:
  nmap:
    # Path to nmap executable (use 'nmap' if in PATH)
    path: "nmap"
    
    # Default timeout in seconds
    timeout: 300
    
    # Default parameters for nmap scans
    default_params:
      scan_type: "tcp_syn"
      service_detection: true
      timing: 4
      ports: "1-1000"
      
  nikto:
    path: "nikto"
    timeout: 600
    default_params:
      ssl: false
      plugins: "@@ALL"
      timeout: 30
      tuning: "1,2,3,4,5,6,7,8,9,0"
      
  sqlmap:
    path: "sqlmap"
    timeout: 1800
    default_params:
      level: 1
      risk: 1
      threads: 1
      batch: true
      
  gobuster:
    path: "gobuster"
    timeout: 300
    default_params:
      mode: "dir"
      threads: 10
      # Update this path based on your system
      wordlist: "/usr/share/wordlists/dirb/common.txt"
      extensions: "php,html,txt,js,css"
      
  openvas:
    path: "gvm-cli"
    timeout: 3600
    default_params:
      host: "localhost"
      port: 9390
      # username and password should be set via environment variables
      # TOOLCONNECTOR_OPENVAS_USERNAME and TOOLCONNECTOR_OPENVAS_PASSWORD

# Workflow definitions
workflows:
  # Quick scan - basic reconnaissance
  quick:
    - "nmap"
    - "nikto"
    
  # Comprehensive scan - thorough security assessment
  comprehensive:
    - "nmap"
    - "nikto"
    - "gobuster"
    - "sqlmap"
    
  # Web application focused scan
  web:
    - "nikto"
    - "gobuster"
    - "sqlmap"
    
  # Network focused scan
  network:
    - "nmap"
    
  # Vulnerability assessment
  vuln_assessment:
    - "nmap"
    - "openvas"

# Environment-specific overrides
# These can be used to override settings for different environments
environments:
  development:
    general:
      mock_mode: true
      log_level: "DEBUG"
    tools:
      nmap:
        timeout: 60
      nikto:
        timeout: 120
        
  production:
    general:
      mock_mode: false
      log_level: "WARNING"
      max_concurrent_tools: 10
    tools:
      nmap:
        timeout: 600
      nikto:
        timeout: 1200
        
  testing:
    general:
      mock_mode: true
      log_level: "DEBUG"
      output_directory: "./test_results"

# Custom tool configurations
# You can add configurations for custom tools here
custom_tools:
  # Example custom tool
  custom_scanner:
    path: "/path/to/custom/scanner"
    timeout: 300
    default_params:
      verbose: true
      output_format: "json"

# Reporting settings
reporting:
  # Default report format
  default_format: "json"
  
  # Available formats
  formats:
    - "json"
    - "text"
    - "html"
    - "csv"
    
  # Report templates directory
  templates_directory: "./templates"
  
  # Include raw output in reports
  include_raw_output: false

# Security settings
security:
  # Validate all inputs
  validate_inputs: true
  
  # Sanitize command arguments
  sanitize_commands: true
  
  # Maximum command length
  max_command_length: 1000
  
  # Allowed characters in targets
  allowed_target_chars: "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.-_:/"

# Performance settings
performance:
  # Enable result caching
  enable_caching: true
  
  # Cache directory
  cache_directory: "./cache"
  
  # Cache expiration time in seconds
  cache_expiration: 3600
  
  # Maximum memory usage per tool (MB)
  max_memory_per_tool: 512
