"""
Unit tests for utility functions.
"""

import unittest
from tool_connectors.utils import (
    validate_ip_address, validate_ip_network, validate_hostname,
    validate_url, validate_port, parse_port_range, normalize_target,
    extract_ips_from_text, extract_urls_from_text, sanitize_filename,
    format_bytes, format_duration, merge_findings
)


class TestValidationFunctions(unittest.TestCase):
    """Test cases for validation functions"""
    
    def test_validate_ip_address(self):
        """Test IP address validation"""
        # Valid IPv4 addresses
        self.assertTrue(validate_ip_address("***********"))
        self.assertTrue(validate_ip_address("********"))
        self.assertTrue(validate_ip_address("127.0.0.1"))
        self.assertTrue(validate_ip_address("*******"))
        
        # Valid IPv6 addresses
        self.assertTrue(validate_ip_address("::1"))
        self.assertTrue(validate_ip_address("2001:db8::1"))
        
        # Invalid addresses
        self.assertFalse(validate_ip_address("256.1.1.1"))
        self.assertFalse(validate_ip_address("192.168.1"))
        self.assertFalse(validate_ip_address("not.an.ip"))
        self.assertFalse(validate_ip_address(""))
    
    def test_validate_ip_network(self):
        """Test IP network validation"""
        # Valid networks
        self.assertTrue(validate_ip_network("***********/24"))
        self.assertTrue(validate_ip_network("10.0.0.0/8"))
        self.assertTrue(validate_ip_network("**********/12"))
        
        # Invalid networks
        self.assertFalse(validate_ip_network("***********/33"))
        self.assertFalse(validate_ip_network("not.a.network/24"))
        self.assertFalse(validate_ip_network(""))
    
    def test_validate_hostname(self):
        """Test hostname validation"""
        # Valid hostnames
        self.assertTrue(validate_hostname("example.com"))
        self.assertTrue(validate_hostname("sub.example.com"))
        self.assertTrue(validate_hostname("test-host"))
        self.assertTrue(validate_hostname("host123"))
        
        # Invalid hostnames
        self.assertFalse(validate_hostname(""))
        self.assertFalse(validate_hostname("-invalid"))
        self.assertFalse(validate_hostname("invalid-"))
        self.assertFalse(validate_hostname("too_long_" + "a" * 250))
    
    def test_validate_url(self):
        """Test URL validation"""
        # Valid URLs
        self.assertTrue(validate_url("http://example.com"))
        self.assertTrue(validate_url("https://example.com"))
        self.assertTrue(validate_url("https://sub.example.com/path"))
        self.assertTrue(validate_url("http://***********:8080"))
        
        # Invalid URLs
        self.assertFalse(validate_url("not-a-url"))
        self.assertFalse(validate_url("ftp://example.com"))  # No scheme validation
        self.assertFalse(validate_url(""))
        self.assertFalse(validate_url("http://"))
    
    def test_validate_port(self):
        """Test port validation"""
        # Valid ports
        self.assertTrue(validate_port(80))
        self.assertTrue(validate_port("443"))
        self.assertTrue(validate_port(1))
        self.assertTrue(validate_port(65535))
        
        # Invalid ports
        self.assertFalse(validate_port(0))
        self.assertFalse(validate_port(65536))
        self.assertFalse(validate_port(-1))
        self.assertFalse(validate_port("not-a-port"))
        self.assertFalse(validate_port(""))


class TestParsingFunctions(unittest.TestCase):
    """Test cases for parsing functions"""
    
    def test_parse_port_range(self):
        """Test port range parsing"""
        # Single ports
        self.assertEqual(parse_port_range("80"), [80])
        self.assertEqual(parse_port_range("80,443"), [80, 443])
        
        # Port ranges
        self.assertEqual(parse_port_range("80-82"), [80, 81, 82])
        self.assertEqual(parse_port_range("80,443,8080-8082"), [80, 443, 8080, 8081, 8082])
        
        # Invalid ranges
        self.assertEqual(parse_port_range("invalid"), [])
        self.assertEqual(parse_port_range("80-70000"), [80])  # Invalid end port
    
    def test_normalize_target(self):
        """Test target normalization"""
        # URL
        result = normalize_target("https://example.com:8080/path")
        self.assertEqual(result['type'], 'url')
        self.assertEqual(result['host'], 'example.com')
        self.assertEqual(result['port'], 8080)
        self.assertEqual(result['scheme'], 'https')
        self.assertTrue(result['is_valid'])
        
        # IP address
        result = normalize_target("***********")
        self.assertEqual(result['type'], 'ip')
        self.assertEqual(result['host'], '***********')
        self.assertTrue(result['is_valid'])
        
        # Hostname
        result = normalize_target("example.com")
        self.assertEqual(result['type'], 'hostname')
        self.assertEqual(result['host'], 'example.com')
        self.assertTrue(result['is_valid'])
        
        # Invalid target
        result = normalize_target("invalid..target")
        self.assertEqual(result['type'], 'unknown')
        self.assertFalse(result['is_valid'])
    
    def test_extract_ips_from_text(self):
        """Test IP extraction from text"""
        text = "Server at *********** and backup at ******** are running"
        ips = extract_ips_from_text(text)
        self.assertIn("***********", ips)
        self.assertIn("********", ips)
        
        # Invalid IPs should be filtered out
        text_with_invalid = "Invalid IP 999.999.999.999 and valid 127.0.0.1"
        ips = extract_ips_from_text(text_with_invalid)
        self.assertIn("127.0.0.1", ips)
        self.assertNotIn("999.999.999.999", ips)
    
    def test_extract_urls_from_text(self):
        """Test URL extraction from text"""
        text = "Visit https://example.com or http://test.org for more info"
        urls = extract_urls_from_text(text)
        self.assertIn("https://example.com", urls)
        self.assertIn("http://test.org", urls)


class TestFormattingFunctions(unittest.TestCase):
    """Test cases for formatting functions"""
    
    def test_sanitize_filename(self):
        """Test filename sanitization"""
        self.assertEqual(sanitize_filename("normal_file.txt"), "normal_file.txt")
        self.assertEqual(sanitize_filename("file<with>invalid:chars"), "file_with_invalid_chars")
        self.assertEqual(sanitize_filename(""), "unnamed")
        self.assertEqual(sanitize_filename("   .hidden   "), "hidden")
    
    def test_format_bytes(self):
        """Test byte formatting"""
        self.assertEqual(format_bytes(0), "0 B")
        self.assertEqual(format_bytes(1024), "1.0 KB")
        self.assertEqual(format_bytes(1536), "1.5 KB")
        self.assertEqual(format_bytes(1048576), "1.0 MB")
    
    def test_format_duration(self):
        """Test duration formatting"""
        self.assertEqual(format_duration(30), "30.0s")
        self.assertEqual(format_duration(90), "1m 30s")
        self.assertEqual(format_duration(3661), "1h 1m")
    
    def test_merge_findings(self):
        """Test findings merging"""
        findings1 = [
            {"type": "vuln", "host": "example.com", "description": "Test vulnerability"}
        ]
        findings2 = [
            {"type": "vuln", "host": "example.com", "description": "Test vulnerability"},  # Duplicate
            {"type": "info", "host": "example.com", "description": "Different finding"}
        ]
        
        merged = merge_findings([findings1, findings2])
        self.assertEqual(len(merged), 2)  # Should remove duplicate


if __name__ == '__main__':
    unittest.main()
