"""
Advanced reporting and visualization system for tool connectors.

This module provides enhanced reporting with multiple formats (PDF, CSV, XML),
data visualization capabilities, and customizable report templates.
"""

import csv
import json
import logging
import os
import xml.etree.ElementTree as ET
from abc import ABC, abstractmethod
from collections import defaultdict
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
import time

from .base import ToolResult, ToolStatus, Severity


@dataclass
class ReportMetadata:
    """Metadata for reports."""
    title: str
    description: str = ""
    author: str = "Tool Connectors Framework"
    created_at: datetime = field(default_factory=datetime.now)
    version: str = "1.0"
    tags: List[str] = field(default_factory=list)
    custom_fields: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ReportSection:
    """A section within a report."""
    title: str
    content: str
    subsections: List['ReportSection'] = field(default_factory=list)
    data: Optional[Dict[str, Any]] = None
    charts: List[Dict[str, Any]] = field(default_factory=list)


class ReportFormatter(ABC):
    """Abstract base class for report formatters."""
    
    @abstractmethod
    def format_report(self, metadata: ReportMetadata, sections: List[ReportSection], 
                     results: List[ToolResult]) -> str:
        """Format a report in the specific format."""
        pass
    
    @abstractmethod
    def get_file_extension(self) -> str:
        """Get the file extension for this format."""
        pass


class JSONFormatter(ReportFormatter):
    """JSON report formatter."""
    
    def format_report(self, metadata: ReportMetadata, sections: List[ReportSection], 
                     results: List[ToolResult]) -> str:
        """Format report as JSON."""
        report_data = {
            'metadata': {
                'title': metadata.title,
                'description': metadata.description,
                'author': metadata.author,
                'created_at': metadata.created_at.isoformat(),
                'version': metadata.version,
                'tags': metadata.tags,
                'custom_fields': metadata.custom_fields
            },
            'sections': self._format_sections(sections),
            'results': self._format_results(results),
            'summary': self._generate_summary(results)
        }
        
        return json.dumps(report_data, indent=2, default=str)
    
    def _format_sections(self, sections: List[ReportSection]) -> List[Dict[str, Any]]:
        """Format sections for JSON output."""
        formatted_sections = []
        
        for section in sections:
            section_data = {
                'title': section.title,
                'content': section.content,
                'data': section.data,
                'charts': section.charts
            }
            
            if section.subsections:
                section_data['subsections'] = self._format_sections(section.subsections)
            
            formatted_sections.append(section_data)
        
        return formatted_sections
    
    def _format_results(self, results: List[ToolResult]) -> List[Dict[str, Any]]:
        """Format tool results for JSON output."""
        formatted_results = []
        
        for result in results:
            result_data = {
                'tool_name': result.tool_name,
                'command': result.command,
                'status': result.status.value,
                'exit_code': result.exit_code,
                'execution_time': result.execution_time,
                'timestamp': result.timestamp,
                'findings_count': len(result.findings),
                'findings': result.findings,
                'error_message': result.error_message
            }
            formatted_results.append(result_data)
        
        return formatted_results
    
    def _generate_summary(self, results: List[ToolResult]) -> Dict[str, Any]:
        """Generate summary statistics."""
        if not results:
            return {}
        
        total_findings = sum(len(r.findings) for r in results)
        status_counts = defaultdict(int)
        severity_counts = defaultdict(int)
        tool_counts = defaultdict(int)
        
        for result in results:
            status_counts[result.status.value] += 1
            tool_counts[result.tool_name] += 1
            
            for finding in result.findings:
                severity = finding.get('severity', 'info')
                severity_counts[severity] += 1
        
        return {
            'total_tools_executed': len(results),
            'total_findings': total_findings,
            'status_distribution': dict(status_counts),
            'severity_distribution': dict(severity_counts),
            'tool_distribution': dict(tool_counts),
            'execution_time_total': sum(r.execution_time for r in results),
            'execution_time_average': sum(r.execution_time for r in results) / len(results)
        }
    
    def get_file_extension(self) -> str:
        """Get file extension."""
        return ".json"


class XMLFormatter(ReportFormatter):
    """XML report formatter."""
    
    def format_report(self, metadata: ReportMetadata, sections: List[ReportSection], 
                     results: List[ToolResult]) -> str:
        """Format report as XML."""
        root = ET.Element("report")
        
        # Add metadata
        metadata_elem = ET.SubElement(root, "metadata")
        ET.SubElement(metadata_elem, "title").text = metadata.title
        ET.SubElement(metadata_elem, "description").text = metadata.description
        ET.SubElement(metadata_elem, "author").text = metadata.author
        ET.SubElement(metadata_elem, "created_at").text = metadata.created_at.isoformat()
        ET.SubElement(metadata_elem, "version").text = metadata.version
        
        # Add tags
        if metadata.tags:
            tags_elem = ET.SubElement(metadata_elem, "tags")
            for tag in metadata.tags:
                ET.SubElement(tags_elem, "tag").text = tag
        
        # Add sections
        if sections:
            sections_elem = ET.SubElement(root, "sections")
            for section in sections:
                self._add_section_to_xml(sections_elem, section)
        
        # Add results
        results_elem = ET.SubElement(root, "results")
        for result in results:
            self._add_result_to_xml(results_elem, result)
        
        # Add summary
        summary_data = JSONFormatter()._generate_summary(results)
        if summary_data:
            summary_elem = ET.SubElement(root, "summary")
            for key, value in summary_data.items():
                if isinstance(value, dict):
                    sub_elem = ET.SubElement(summary_elem, key)
                    for sub_key, sub_value in value.items():
                        ET.SubElement(sub_elem, sub_key).text = str(sub_value)
                else:
                    ET.SubElement(summary_elem, key).text = str(value)
        
        # Format XML with indentation
        self._indent_xml(root)
        return ET.tostring(root, encoding='unicode')
    
    def _add_section_to_xml(self, parent: ET.Element, section: ReportSection):
        """Add a section to XML element."""
        section_elem = ET.SubElement(parent, "section")
        ET.SubElement(section_elem, "title").text = section.title
        ET.SubElement(section_elem, "content").text = section.content
        
        if section.data:
            data_elem = ET.SubElement(section_elem, "data")
            for key, value in section.data.items():
                ET.SubElement(data_elem, key).text = str(value)
        
        if section.subsections:
            subsections_elem = ET.SubElement(section_elem, "subsections")
            for subsection in section.subsections:
                self._add_section_to_xml(subsections_elem, subsection)
    
    def _add_result_to_xml(self, parent: ET.Element, result: ToolResult):
        """Add a tool result to XML element."""
        result_elem = ET.SubElement(parent, "tool_result")
        ET.SubElement(result_elem, "tool_name").text = result.tool_name
        ET.SubElement(result_elem, "command").text = result.command
        ET.SubElement(result_elem, "status").text = result.status.value
        ET.SubElement(result_elem, "exit_code").text = str(result.exit_code) if result.exit_code is not None else ""
        ET.SubElement(result_elem, "execution_time").text = str(result.execution_time)
        ET.SubElement(result_elem, "timestamp").text = str(result.timestamp)
        
        if result.error_message:
            ET.SubElement(result_elem, "error_message").text = result.error_message
        
        # Add findings
        if result.findings:
            findings_elem = ET.SubElement(result_elem, "findings")
            for i, finding in enumerate(result.findings):
                finding_elem = ET.SubElement(findings_elem, "finding", id=str(i))
                for key, value in finding.items():
                    ET.SubElement(finding_elem, key).text = str(value)
    
    def _indent_xml(self, elem: ET.Element, level: int = 0):
        """Add indentation to XML for pretty printing."""
        indent = "\n" + level * "  "
        if len(elem):
            if not elem.text or not elem.text.strip():
                elem.text = indent + "  "
            if not elem.tail or not elem.tail.strip():
                elem.tail = indent
            for child in elem:
                self._indent_xml(child, level + 1)
            if not child.tail or not child.tail.strip():
                child.tail = indent
        else:
            if level and (not elem.tail or not elem.tail.strip()):
                elem.tail = indent
    
    def get_file_extension(self) -> str:
        """Get file extension."""
        return ".xml"


class CSVFormatter(ReportFormatter):
    """CSV report formatter."""
    
    def format_report(self, metadata: ReportMetadata, sections: List[ReportSection], 
                     results: List[ToolResult]) -> str:
        """Format report as CSV."""
        import io
        
        output = io.StringIO()
        
        # Write metadata as comments
        output.write(f"# Report: {metadata.title}\n")
        output.write(f"# Description: {metadata.description}\n")
        output.write(f"# Author: {metadata.author}\n")
        output.write(f"# Created: {metadata.created_at.isoformat()}\n")
        output.write(f"# Version: {metadata.version}\n")
        output.write("\n")
        
        # Write tool results
        if results:
            writer = csv.writer(output)
            
            # Write header
            writer.writerow([
                'Tool Name', 'Command', 'Status', 'Exit Code', 'Execution Time',
                'Timestamp', 'Findings Count', 'Error Message'
            ])
            
            # Write data rows
            for result in results:
                writer.writerow([
                    result.tool_name,
                    result.command,
                    result.status.value,
                    result.exit_code if result.exit_code is not None else '',
                    result.execution_time,
                    result.timestamp,
                    len(result.findings),
                    result.error_message or ''
                ])
            
            output.write("\n")
        
        # Write findings as separate CSV section
        if any(result.findings for result in results):
            output.write("# Findings\n")
            writer = csv.writer(output)
            
            # Collect all unique finding keys
            all_keys = set()
            for result in results:
                for finding in result.findings:
                    all_keys.update(finding.keys())
            
            # Write findings header
            header = ['Tool Name', 'Target'] + sorted(all_keys)
            writer.writerow(header)
            
            # Write findings data
            for result in results:
                for finding in result.findings:
                    row = [result.tool_name, finding.get('target', '')]
                    for key in sorted(all_keys):
                        row.append(finding.get(key, ''))
                    writer.writerow(row)
        
        return output.getvalue()
    
    def get_file_extension(self) -> str:
        """Get file extension."""
        return ".csv"


class HTMLFormatter(ReportFormatter):
    """HTML report formatter with basic styling."""
    
    def format_report(self, metadata: ReportMetadata, sections: List[ReportSection], 
                     results: List[ToolResult]) -> str:
        """Format report as HTML."""
        html_parts = []
        
        # HTML header
        html_parts.append("""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }}
        .header {{ background: #f4f4f4; padding: 20px; border-radius: 5px; margin-bottom: 30px; }}
        .section {{ margin-bottom: 30px; }}
        .section h2 {{ color: #333; border-bottom: 2px solid #ddd; padding-bottom: 10px; }}
        .section h3 {{ color: #666; }}
        table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
        th {{ background-color: #f2f2f2; font-weight: bold; }}
        .status-completed {{ color: green; font-weight: bold; }}
        .status-failed {{ color: red; font-weight: bold; }}
        .severity-critical {{ color: #d32f2f; font-weight: bold; }}
        .severity-high {{ color: #f57c00; font-weight: bold; }}
        .severity-medium {{ color: #fbc02d; font-weight: bold; }}
        .severity-low {{ color: #388e3c; }}
        .severity-info {{ color: #1976d2; }}
        .summary-stats {{ display: flex; gap: 20px; margin: 20px 0; }}
        .stat-box {{ background: #f9f9f9; padding: 15px; border-radius: 5px; text-align: center; }}
        .stat-number {{ font-size: 24px; font-weight: bold; color: #333; }}
        .stat-label {{ font-size: 14px; color: #666; }}
    </style>
</head>
<body>""".format(title=metadata.title))
        
        # Report header
        html_parts.append(f"""
    <div class="header">
        <h1>{metadata.title}</h1>
        <p><strong>Description:</strong> {metadata.description}</p>
        <p><strong>Author:</strong> {metadata.author}</p>
        <p><strong>Created:</strong> {metadata.created_at.strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p><strong>Version:</strong> {metadata.version}</p>
        {f'<p><strong>Tags:</strong> {", ".join(metadata.tags)}</p>' if metadata.tags else ''}
    </div>
""")
        
        # Summary section
        if results:
            summary = JSONFormatter()._generate_summary(results)
            html_parts.append("""
    <div class="section">
        <h2>Executive Summary</h2>
        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-number">{total_tools}</div>
                <div class="stat-label">Tools Executed</div>
            </div>
            <div class="stat-box">
                <div class="stat-number">{total_findings}</div>
                <div class="stat-label">Total Findings</div>
            </div>
            <div class="stat-box">
                <div class="stat-number">{avg_time:.2f}s</div>
                <div class="stat-label">Avg Execution Time</div>
            </div>
        </div>
    </div>
""".format(
                total_tools=summary['total_tools_executed'],
                total_findings=summary['total_findings'],
                avg_time=summary['execution_time_average']
            ))
        
        # Custom sections
        for section in sections:
            html_parts.append(self._format_section_html(section))
        
        # Tool results section
        if results:
            html_parts.append("""
    <div class="section">
        <h2>Tool Execution Results</h2>
        <table>
            <thead>
                <tr>
                    <th>Tool</th>
                    <th>Status</th>
                    <th>Execution Time</th>
                    <th>Findings</th>
                    <th>Command</th>
                </tr>
            </thead>
            <tbody>
""")
            
            for result in results:
                status_class = f"status-{result.status.value.lower()}"
                html_parts.append(f"""
                <tr>
                    <td>{result.tool_name}</td>
                    <td class="{status_class}">{result.status.value}</td>
                    <td>{result.execution_time:.2f}s</td>
                    <td>{len(result.findings)}</td>
                    <td><code>{result.command}</code></td>
                </tr>
""")
            
            html_parts.append("""
            </tbody>
        </table>
    </div>
""")
        
        # Findings section
        all_findings = []
        for result in results:
            for finding in result.findings:
                finding_with_tool = finding.copy()
                finding_with_tool['tool_name'] = result.tool_name
                all_findings.append(finding_with_tool)
        
        if all_findings:
            html_parts.append("""
    <div class="section">
        <h2>Detailed Findings</h2>
        <table>
            <thead>
                <tr>
                    <th>Tool</th>
                    <th>Type</th>
                    <th>Severity</th>
                    <th>Description</th>
                    <th>Target</th>
                </tr>
            </thead>
            <tbody>
""")
            
            for finding in all_findings:
                severity = finding.get('severity', 'info')
                severity_class = f"severity-{severity.lower()}"
                html_parts.append(f"""
                <tr>
                    <td>{finding.get('tool_name', '')}</td>
                    <td>{finding.get('type', '')}</td>
                    <td class="{severity_class}">{severity.upper()}</td>
                    <td>{finding.get('description', '')}</td>
                    <td>{finding.get('target', '')}</td>
                </tr>
""")
            
            html_parts.append("""
            </tbody>
        </table>
    </div>
""")
        
        # HTML footer
        html_parts.append("""
</body>
</html>
""")
        
        return ''.join(html_parts)
    
    def _format_section_html(self, section: ReportSection, level: int = 2) -> str:
        """Format a section as HTML."""
        html = f"""
    <div class="section">
        <h{level}>{section.title}</h{level}>
        <p>{section.content}</p>
"""
        
        if section.data:
            html += "        <table>\n"
            for key, value in section.data.items():
                html += f"            <tr><td><strong>{key}</strong></td><td>{value}</td></tr>\n"
            html += "        </table>\n"
        
        for subsection in section.subsections:
            html += self._format_section_html(subsection, level + 1)
        
        html += "    </div>\n"
        return html
    
    def get_file_extension(self) -> str:
        """Get file extension."""
        return ".html"


class ReportTemplate:
    """Template for generating reports."""

    def __init__(self, name: str, description: str = ""):
        """
        Initialize report template.

        Args:
            name: Template name
            description: Template description
        """
        self.name = name
        self.description = description
        self.sections: List[ReportSection] = []
        self.metadata_template: Dict[str, Any] = {}

    def add_section(self, section: ReportSection) -> 'ReportTemplate':
        """Add a section to the template."""
        self.sections.append(section)
        return self

    def set_metadata_template(self, **kwargs) -> 'ReportTemplate':
        """Set metadata template values."""
        self.metadata_template.update(kwargs)
        return self

    def generate_metadata(self, **overrides) -> ReportMetadata:
        """Generate metadata from template with overrides."""
        metadata_data = self.metadata_template.copy()
        metadata_data.update(overrides)

        return ReportMetadata(
            title=metadata_data.get('title', f'Report - {self.name}'),
            description=metadata_data.get('description', self.description),
            author=metadata_data.get('author', 'Tool Connectors Framework'),
            version=metadata_data.get('version', '1.0'),
            tags=metadata_data.get('tags', []),
            custom_fields=metadata_data.get('custom_fields', {})
        )


class ReportGenerator:
    """
    Main report generator with multiple format support and templates.
    """

    def __init__(self):
        """Initialize report generator."""
        self.logger = logging.getLogger(self.__class__.__name__)
        self.formatters: Dict[str, ReportFormatter] = {
            'json': JSONFormatter(),
            'xml': XMLFormatter(),
            'csv': CSVFormatter(),
            'html': HTMLFormatter()
        }
        self.templates: Dict[str, ReportTemplate] = {}

        # Add default templates
        self._add_default_templates()

    def _add_default_templates(self):
        """Add default report templates."""
        # Security Assessment Template
        security_template = ReportTemplate(
            "security_assessment",
            "Comprehensive security assessment report"
        )
        security_template.set_metadata_template(
            title="Security Assessment Report",
            tags=["security", "assessment", "vulnerability"]
        )
        security_template.add_section(ReportSection(
            title="Executive Summary",
            content="This report presents the findings of a comprehensive security assessment."
        ))
        security_template.add_section(ReportSection(
            title="Methodology",
            content="The assessment was conducted using automated security tools including network scanners, web application scanners, and vulnerability assessment tools."
        ))
        security_template.add_section(ReportSection(
            title="Risk Assessment",
            content="Findings have been categorized by severity level to help prioritize remediation efforts."
        ))

        self.templates["security_assessment"] = security_template

        # Penetration Test Template
        pentest_template = ReportTemplate(
            "penetration_test",
            "Penetration testing report"
        )
        pentest_template.set_metadata_template(
            title="Penetration Test Report",
            tags=["pentest", "security", "testing"]
        )
        pentest_template.add_section(ReportSection(
            title="Scope and Objectives",
            content="This penetration test was conducted to identify security vulnerabilities and assess the overall security posture."
        ))
        pentest_template.add_section(ReportSection(
            title="Testing Methodology",
            content="The testing followed industry-standard methodologies including OWASP guidelines and NIST frameworks."
        ))

        self.templates["penetration_test"] = pentest_template

        # Quick Scan Template
        quick_template = ReportTemplate(
            "quick_scan",
            "Quick security scan report"
        )
        quick_template.set_metadata_template(
            title="Quick Security Scan Report",
            tags=["scan", "quick", "security"]
        )
        quick_template.add_section(ReportSection(
            title="Scan Overview",
            content="This report contains the results of a quick security scan."
        ))

        self.templates["quick_scan"] = quick_template

    def add_formatter(self, name: str, formatter: ReportFormatter):
        """Add a custom formatter."""
        self.formatters[name] = formatter
        self.logger.info(f"Added custom formatter: {name}")

    def add_template(self, template: ReportTemplate):
        """Add a custom template."""
        self.templates[template.name] = template
        self.logger.info(f"Added custom template: {template.name}")

    def get_available_formats(self) -> List[str]:
        """Get list of available output formats."""
        return list(self.formatters.keys())

    def get_available_templates(self) -> List[str]:
        """Get list of available templates."""
        return list(self.templates.keys())

    def generate_report(self, results: List[ToolResult],
                       output_format: str = 'html',
                       template_name: Optional[str] = None,
                       metadata_overrides: Optional[Dict[str, Any]] = None,
                       custom_sections: Optional[List[ReportSection]] = None) -> str:
        """
        Generate a report from tool results.

        Args:
            results: List of tool results
            output_format: Output format (html, json, xml, csv)
            template_name: Template to use (optional)
            metadata_overrides: Metadata overrides (optional)
            custom_sections: Custom sections to add (optional)

        Returns:
            Formatted report as string
        """
        if output_format not in self.formatters:
            raise ValueError(f"Unsupported format: {output_format}. Available: {list(self.formatters.keys())}")

        formatter = self.formatters[output_format]

        # Generate metadata
        if template_name and template_name in self.templates:
            template = self.templates[template_name]
            metadata = template.generate_metadata(**(metadata_overrides or {}))
            sections = template.sections.copy()
        else:
            # Create metadata with defaults and overrides
            metadata_data = {
                'title': 'Security Scan Report',
                'description': 'Automated security scan results',
                'author': 'Tool Connectors Framework',
                'version': '1.0',
                'tags': [],
                'custom_fields': {}
            }

            if metadata_overrides:
                metadata_data.update(metadata_overrides)

            metadata = ReportMetadata(**metadata_data)
            sections = []

        # Add custom sections
        if custom_sections:
            sections.extend(custom_sections)

        # Add analysis sections
        sections.extend(self._generate_analysis_sections(results))

        return formatter.format_report(metadata, sections, results)

    def _generate_analysis_sections(self, results: List[ToolResult]) -> List[ReportSection]:
        """Generate analysis sections from results."""
        sections = []

        if not results:
            return sections

        # Tool execution summary
        tool_summary = self._analyze_tool_execution(results)
        sections.append(ReportSection(
            title="Tool Execution Analysis",
            content=f"Executed {len(results)} tools with an average execution time of {tool_summary['avg_time']:.2f} seconds.",
            data=tool_summary
        ))

        # Findings analysis
        findings_analysis = self._analyze_findings(results)
        if findings_analysis['total_findings'] > 0:
            sections.append(ReportSection(
                title="Findings Analysis",
                content=f"Discovered {findings_analysis['total_findings']} findings across {findings_analysis['affected_tools']} tools.",
                data=findings_analysis
            ))

        # Risk assessment
        risk_assessment = self._assess_risk(results)
        sections.append(ReportSection(
            title="Risk Assessment",
            content=f"Overall risk level: {risk_assessment['overall_risk']}",
            data=risk_assessment
        ))

        return sections

    def _analyze_tool_execution(self, results: List[ToolResult]) -> Dict[str, Any]:
        """Analyze tool execution statistics."""
        if not results:
            return {}

        total_time = sum(r.execution_time for r in results)
        successful_tools = [r for r in results if r.status == ToolStatus.COMPLETED]
        failed_tools = [r for r in results if r.status == ToolStatus.FAILED]

        return {
            'total_tools': len(results),
            'successful_tools': len(successful_tools),
            'failed_tools': len(failed_tools),
            'success_rate': len(successful_tools) / len(results) * 100,
            'total_time': total_time,
            'avg_time': total_time / len(results),
            'fastest_tool': min(results, key=lambda r: r.execution_time).tool_name,
            'slowest_tool': max(results, key=lambda r: r.execution_time).tool_name
        }

    def _analyze_findings(self, results: List[ToolResult]) -> Dict[str, Any]:
        """Analyze findings across all results."""
        all_findings = []
        tools_with_findings = set()
        severity_counts = defaultdict(int)
        finding_types = defaultdict(int)

        for result in results:
            if result.findings:
                tools_with_findings.add(result.tool_name)
                all_findings.extend(result.findings)

                for finding in result.findings:
                    severity = finding.get('severity', 'info')
                    severity_counts[severity] += 1

                    finding_type = finding.get('type', 'unknown')
                    finding_types[finding_type] += 1

        return {
            'total_findings': len(all_findings),
            'affected_tools': len(tools_with_findings),
            'severity_distribution': dict(severity_counts),
            'finding_types': dict(finding_types),
            'tools_with_findings': list(tools_with_findings)
        }

    def _assess_risk(self, results: List[ToolResult]) -> Dict[str, Any]:
        """Assess overall risk level based on findings."""
        all_findings = []
        for result in results:
            all_findings.extend(result.findings)

        if not all_findings:
            return {
                'overall_risk': 'LOW',
                'risk_score': 0,
                'critical_findings': 0,
                'high_findings': 0,
                'medium_findings': 0,
                'low_findings': 0
            }

        # Count findings by severity
        severity_counts = defaultdict(int)
        for finding in all_findings:
            severity = finding.get('severity', 'info')
            severity_counts[severity] += 1

        # Calculate risk score
        risk_score = (
            severity_counts.get('critical', 0) * 10 +
            severity_counts.get('high', 0) * 7 +
            severity_counts.get('medium', 0) * 4 +
            severity_counts.get('low', 0) * 1
        )

        # Determine overall risk level
        if severity_counts.get('critical', 0) > 0:
            overall_risk = 'CRITICAL'
        elif severity_counts.get('high', 0) > 0:
            overall_risk = 'HIGH'
        elif severity_counts.get('medium', 0) > 2:
            overall_risk = 'MEDIUM'
        elif severity_counts.get('medium', 0) > 0 or severity_counts.get('low', 0) > 5:
            overall_risk = 'LOW'
        else:
            overall_risk = 'MINIMAL'

        return {
            'overall_risk': overall_risk,
            'risk_score': risk_score,
            'critical_findings': severity_counts.get('critical', 0),
            'high_findings': severity_counts.get('high', 0),
            'medium_findings': severity_counts.get('medium', 0),
            'low_findings': severity_counts.get('low', 0),
            'info_findings': severity_counts.get('info', 0)
        }

    def save_report(self, report_content: str, file_path: str, output_format: str):
        """Save report to file."""
        formatter = self.formatters.get(output_format)
        if not formatter:
            raise ValueError(f"Unknown format: {output_format}")

        # Ensure file has correct extension
        path = Path(file_path)
        expected_ext = formatter.get_file_extension()
        if not path.suffix:
            path = path.with_suffix(expected_ext)
        elif path.suffix != expected_ext:
            self.logger.warning(f"File extension {path.suffix} doesn't match format {output_format} (expected {expected_ext})")

        # Create directory if it doesn't exist
        path.parent.mkdir(parents=True, exist_ok=True)

        # Write file
        with open(path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        self.logger.info(f"Report saved to: {path}")
        return str(path)


# Global report generator instance
_report_generator: Optional[ReportGenerator] = None


def get_report_generator() -> ReportGenerator:
    """
    Get the global report generator instance.

    Returns:
        ReportGenerator instance
    """
    global _report_generator

    if _report_generator is None:
        _report_generator = ReportGenerator()

    return _report_generator
