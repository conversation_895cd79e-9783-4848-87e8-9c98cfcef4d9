"""
Unit tests for plugin system.
"""

import os
import tempfile
import unittest
from pathlib import Path
from typing import Any, Dict, List
from unittest.mock import patch

from tool_connectors.plugin_system import <PERSON>lugin<PERSON>ana<PERSON>, PluginInfo, get_plugin_manager
from tool_connectors.base import BaseToolConnector, ToolResult, ToolStatus
from tool_connectors import ToolManager


class MockPluginConnector(BaseToolConnector):
    """Mock connector for testing plugins."""
    
    __plugin_metadata__ = {
        "version": "1.0.0",
        "author": "Test Author"
    }
    
    def __init__(self, tool_path: str = None, timeout: int = 300, 
                 mock_mode: bool = False, mock_data: Dict[str, Any] = None):
        super().__init__(tool_path, timeout, mock_mode, mock_data)
    
    @property
    def tool_name(self) -> str:
        return "mock_plugin_tool"
    
    @property
    def default_command(self) -> str:
        return "mock-plugin-tool"
    
    def build_command(self, target: str, **kwargs) -> List[str]:
        return [self.default_command, target]
    
    def parse_output(self, stdout: str, stderr: str) -> Dict[str, Any]:
        return {'target': 'test', 'findings': []}
    
    def validate_target(self, target: str) -> bool:
        return bool(target and target.strip())
    
    def _extract_findings(self, parsed_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        return []


class TestPluginManager(unittest.TestCase):
    """Test cases for PluginManager"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.plugin_manager = PluginManager()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_plugin_info_creation(self):
        """Test PluginInfo creation"""
        plugin_info = PluginInfo(
            "test_plugin", 
            MockPluginConnector, 
            "/path/to/plugin.py",
            {"version": "1.0.0"}
        )
        
        self.assertEqual(plugin_info.name, "test_plugin")
        self.assertEqual(plugin_info.connector_class, MockPluginConnector)
        self.assertEqual(plugin_info.module_path, "/path/to/plugin.py")
        self.assertIn("version", plugin_info.metadata)
        self.assertIn("author", plugin_info.metadata)  # From class metadata
    
    def test_register_plugin_class(self):
        """Test registering a plugin class directly"""
        self.plugin_manager.register_plugin_class(
            "test_plugin", 
            MockPluginConnector,
            {"description": "Test plugin"}
        )
        
        plugin_info = self.plugin_manager.get_plugin("test_plugin")
        self.assertIsNotNone(plugin_info)
        self.assertEqual(plugin_info.name, "test_plugin")
        self.assertEqual(plugin_info.connector_class, MockPluginConnector)
        self.assertIn("description", plugin_info.metadata)
    
    def test_create_connector(self):
        """Test creating connector from plugin"""
        self.plugin_manager.register_plugin_class("test_plugin", MockPluginConnector)
        
        connector = self.plugin_manager.create_connector("test_plugin", mock_mode=True)
        
        self.assertIsNotNone(connector)
        self.assertIsInstance(connector, MockPluginConnector)
        self.assertEqual(connector.tool_name, "mock_plugin_tool")
    
    def test_unregister_plugin(self):
        """Test unregistering a plugin"""
        self.plugin_manager.register_plugin_class("test_plugin", MockPluginConnector)
        
        # Verify plugin is registered
        self.assertIsNotNone(self.plugin_manager.get_plugin("test_plugin"))
        
        # Unregister plugin
        result = self.plugin_manager.unregister_plugin("test_plugin")
        self.assertTrue(result)
        
        # Verify plugin is no longer registered
        self.assertIsNone(self.plugin_manager.get_plugin("test_plugin"))
        
        # Try to unregister non-existent plugin
        result = self.plugin_manager.unregister_plugin("nonexistent")
        self.assertFalse(result)
    
    def test_list_plugins(self):
        """Test listing plugins"""
        self.plugin_manager.register_plugin_class("plugin1", MockPluginConnector)
        self.plugin_manager.register_plugin_class("plugin2", MockPluginConnector)
        
        plugins = self.plugin_manager.list_plugins()
        
        self.assertIn("plugin1", plugins)
        self.assertIn("plugin2", plugins)
        self.assertEqual(len(plugins), 2)
    
    def test_get_all_plugins(self):
        """Test getting all plugins"""
        self.plugin_manager.register_plugin_class("plugin1", MockPluginConnector)
        self.plugin_manager.register_plugin_class("plugin2", MockPluginConnector)
        
        all_plugins = self.plugin_manager.get_all_plugins()
        
        self.assertEqual(len(all_plugins), 2)
        self.assertIn("plugin1", all_plugins)
        self.assertIn("plugin2", all_plugins)
        self.assertIsInstance(all_plugins["plugin1"], PluginInfo)
    
    def test_get_plugin_metadata(self):
        """Test getting plugin metadata"""
        metadata = {"version": "2.0.0", "description": "Test plugin"}
        self.plugin_manager.register_plugin_class("test_plugin", MockPluginConnector, metadata)
        
        retrieved_metadata = self.plugin_manager.get_plugin_metadata("test_plugin")
        
        self.assertIsNotNone(retrieved_metadata)
        self.assertEqual(retrieved_metadata["version"], "2.0.0")
        self.assertEqual(retrieved_metadata["description"], "Test plugin")
        self.assertEqual(retrieved_metadata["author"], "Test Author")  # From class
    
    def test_plugin_directory_management(self):
        """Test plugin directory management"""
        test_dir = "/test/plugin/dir"
        
        self.plugin_manager.add_plugin_directory(test_dir)
        
        self.assertIn(test_dir, self.plugin_manager._plugin_directories)
        
        # Adding same directory again should not duplicate
        self.plugin_manager.add_plugin_directory(test_dir)
        count = self.plugin_manager._plugin_directories.count(test_dir)
        self.assertEqual(count, 1)
    
    def test_discover_plugins_from_file(self):
        """Test discovering plugins from Python files"""
        # Create a test plugin file
        plugin_file = Path(self.temp_dir) / "test_plugin.py"
        plugin_content = '''
from tool_connectors.base import BaseToolConnector
from typing import Any, Dict, List

__plugin_name__ = "file_plugin"
__plugin_metadata__ = {"version": "1.0.0"}

class FilePluginConnector(BaseToolConnector):
    @property
    def tool_name(self) -> str:
        return "file_plugin_tool"
    
    @property
    def default_command(self) -> str:
        return "file-plugin-tool"
    
    def build_command(self, target: str, **kwargs) -> List[str]:
        return [self.default_command, target]
    
    def parse_output(self, stdout: str, stderr: str) -> Dict[str, Any]:
        return {}
    
    def validate_target(self, target: str) -> bool:
        return True
    
    def _extract_findings(self, parsed_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        return []
'''
        
        with open(plugin_file, 'w') as f:
            f.write(plugin_content)
        
        # Add directory and discover plugins
        self.plugin_manager.add_plugin_directory(self.temp_dir)
        plugins = self.plugin_manager.discover_plugins(auto_load=True)
        
        # Check that plugin was discovered
        plugin_names = [p.name for p in plugins]
        self.assertIn("file_plugin", plugin_names)
        
        # Check that plugin was registered
        plugin_info = self.plugin_manager.get_plugin("file_plugin")
        self.assertIsNotNone(plugin_info)
        self.assertEqual(plugin_info.name, "file_plugin")
    
    def test_create_connector_error_handling(self):
        """Test error handling when creating connectors"""
        # Register a plugin that will fail to create
        class FailingConnector(BaseToolConnector):
            def __init__(self, *args, **kwargs):
                raise ValueError("Intentional failure")
            
            @property
            def tool_name(self) -> str:
                return "failing_tool"
            
            @property
            def default_command(self) -> str:
                return "failing-tool"
            
            def build_command(self, target: str, **kwargs) -> List[str]:
                return []
            
            def parse_output(self, stdout: str, stderr: str) -> Dict[str, Any]:
                return {}
            
            def validate_target(self, target: str) -> bool:
                return True
            
            def _extract_findings(self, parsed_data: Dict[str, Any]) -> List[Dict[str, Any]]:
                return []
        
        self.plugin_manager.register_plugin_class("failing_plugin", FailingConnector)
        
        # Try to create connector - should return None
        connector = self.plugin_manager.create_connector("failing_plugin")
        self.assertIsNone(connector)


class TestToolManagerPluginIntegration(unittest.TestCase):
    """Test ToolManager integration with plugins"""
    
    def test_plugin_registration_in_tool_manager(self):
        """Test registering plugins in ToolManager"""
        manager = ToolManager(mock_mode=True)
        
        # Register a plugin
        manager.register_plugin_connector("test_plugin", MockPluginConnector)
        
        # Check that plugin is available
        available_tools = manager.get_available_tools()
        self.assertIn("test_plugin", available_tools)
        
        # Check that we can get the connector
        connector = manager.get_connector("test_plugin")
        self.assertIsNotNone(connector)
        self.assertIsInstance(connector, MockPluginConnector)
    
    def test_plugin_execution_in_tool_manager(self):
        """Test executing plugins through ToolManager"""
        manager = ToolManager(mock_mode=True)
        manager.register_plugin_connector("test_plugin", MockPluginConnector)
        
        # Execute the plugin
        result = manager.execute_tool("test_plugin", "example.com")
        
        self.assertEqual(result.tool_name, "mock_plugin_tool")
        self.assertEqual(result.status, ToolStatus.COMPLETED)
    
    def test_plugin_info_retrieval(self):
        """Test retrieving plugin information"""
        manager = ToolManager(mock_mode=True)
        metadata = {"version": "1.0.0", "description": "Test plugin"}
        manager.register_plugin_connector("test_plugin", MockPluginConnector, metadata)
        
        # Get plugin info
        plugin_info = manager.get_plugin_info("test_plugin")
        
        self.assertIsNotNone(plugin_info)
        self.assertEqual(plugin_info["name"], "test_plugin")
        self.assertEqual(plugin_info["class"], "MockPluginConnector")
        self.assertIn("version", plugin_info["metadata"])
    
    def test_plugin_unregistration(self):
        """Test unregistering plugins from ToolManager"""
        manager = ToolManager(mock_mode=True)
        manager.register_plugin_connector("test_plugin", MockPluginConnector)
        
        # Verify plugin is registered
        self.assertIn("test_plugin", manager.get_available_tools())
        
        # Unregister plugin
        result = manager.unregister_connector("test_plugin")
        self.assertTrue(result)
        
        # Verify plugin is no longer available
        self.assertNotIn("test_plugin", manager.get_available_tools())
    
    def test_list_all_plugin_info(self):
        """Test listing all plugin information"""
        manager = ToolManager(mock_mode=True)
        manager.register_plugin_connector("plugin1", MockPluginConnector)
        manager.register_plugin_connector("plugin2", MockPluginConnector)
        
        all_plugin_info = manager.get_all_plugin_info()
        
        self.assertIn("plugin1", all_plugin_info)
        self.assertIn("plugin2", all_plugin_info)
        self.assertEqual(len(all_plugin_info), 2)


class TestGlobalPluginFunctions(unittest.TestCase):
    """Test global plugin functions"""
    
    def test_get_plugin_manager(self):
        """Test global plugin manager function"""
        manager1 = get_plugin_manager()
        manager2 = get_plugin_manager()
        
        # Should return same instance
        self.assertIs(manager1, manager2)


if __name__ == '__main__':
    unittest.main()
