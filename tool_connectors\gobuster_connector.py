"""
Gobuster Tool Connector

This module provides integration with Gobuster for directory and file brute forcing.
"""

import json
import re
from typing import Any, Dict, List
from urllib.parse import urlparse, urljoin

from .base import BaseToolConnector, Severity


class GobusterConnector(BaseToolConnector):
    """Connector for Gobuster directory/file brute forcing tool"""
    
    @property
    def tool_name(self) -> str:
        return "gobuster"
    
    @property
    def default_command(self) -> str:
        return "gobuster"
    
    def build_command(self, target: str, **kwargs) -> List[str]:
        """
        Build Gobuster command with specified options.
        
        Args:
            target: Target URL to scan
            **kwargs: Gobuster options
                - mode: Scan mode (dir, dns, vhost)
                - wordlist: Path to wordlist file
                - extensions: File extensions to search for
                - threads: Number of threads
                - timeout: Request timeout
                - user_agent: User agent string
                - cookies: <PERSON>ie string
                - username: Username for basic auth
                - password: Password for basic auth
                - status_codes: Status codes to include
                - exclude_status: Status codes to exclude
        """
        cmd = [self.tool_path or self.default_command]
        
        # Mode
        mode = kwargs.get('mode', 'dir')
        cmd.append(mode)
        
        # Target URL
        cmd.extend(['-u', target])
        
        # Wordlist
        wordlist = kwargs.get('wordlist', '/usr/share/wordlists/dirb/common.txt')
        cmd.extend(['-w', wordlist])
        
        # Extensions (for dir mode)
        if mode == 'dir':
            extensions = kwargs.get('extensions', ['php', 'html', 'txt'])
            if extensions:
                if isinstance(extensions, list):
                    extensions = ','.join(extensions)
                cmd.extend(['-x', extensions])
        
        # Threads
        threads = kwargs.get('threads', 10)
        cmd.extend(['-t', str(threads)])
        
        # Timeout
        timeout = kwargs.get('timeout', 10)
        cmd.extend(['--timeout', f"{timeout}s"])
        
        # User agent
        user_agent = kwargs.get('user_agent')
        if user_agent:
            cmd.extend(['-a', user_agent])
        
        # Cookies
        cookies = kwargs.get('cookies')
        if cookies:
            cmd.extend(['-c', cookies])
        
        # Basic authentication
        username = kwargs.get('username')
        password = kwargs.get('password')
        if username and password:
            cmd.extend(['-U', username, '-P', password])
        
        # Status codes
        status_codes = kwargs.get('status_codes')
        if status_codes:
            if isinstance(status_codes, list):
                status_codes = ','.join(map(str, status_codes))
            cmd.extend(['-s', status_codes])
        
        # Exclude status codes
        exclude_status = kwargs.get('exclude_status')
        if exclude_status:
            if isinstance(exclude_status, list):
                exclude_status = ','.join(map(str, exclude_status))
            cmd.extend(['-b', exclude_status])
        
        # Follow redirects
        if kwargs.get('follow_redirects', False):
            cmd.append('-r')
        
        # Quiet mode
        if kwargs.get('quiet', True):
            cmd.append('-q')
        
        return cmd
    
    def parse_output(self, stdout: str, stderr: str) -> Dict[str, Any]:
        """
        Parse Gobuster output into standardized format.
        
        Args:
            stdout: Gobuster stdout output
            stderr: Gobuster stderr output
            
        Returns:
            Parsed data dictionary
        """
        parsed_data = {
            'target': '',
            'mode': '',
            'found_paths': [],
            'statistics': {},
            'warnings': [],
            'errors': []
        }
        
        lines = stdout.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # Extract target and mode from initial output
            if 'Target:' in line:
                target_match = re.search(r'Target:\s+(.+)', line)
                if target_match:
                    parsed_data['target'] = target_match.group(1)
            
            if 'Mode:' in line:
                mode_match = re.search(r'Mode:\s+(.+)', line)
                if mode_match:
                    parsed_data['mode'] = mode_match.group(1)
            
            # Parse found paths (format: /path (Status: 200) [Size: 1234])
            if line.startswith('/') or line.startswith('http'):
                path_info = self._parse_found_path(line)
                if path_info:
                    parsed_data['found_paths'].append(path_info)
            
            # Parse statistics
            if 'Finished' in line:
                parsed_data['statistics']['completed'] = True
            
            # Extract warnings from stderr
            if 'WARNING' in line.upper():
                parsed_data['warnings'].append(line)
            
            # Extract errors
            if 'ERROR' in line.upper() or 'FATAL' in line.upper():
                parsed_data['errors'].append(line)
        
        return parsed_data
    
    def _parse_found_path(self, line: str) -> Dict[str, Any]:
        """Parse a found path line from Gobuster output"""
        # Pattern: /path (Status: 200) [Size: 1234]
        path_pattern = r'^([^\s]+)\s+\(Status:\s+(\d+)\)\s+\[Size:\s+(\d+)\]'
        match = re.match(path_pattern, line)
        
        if match:
            return {
                'path': match.group(1),
                'status_code': int(match.group(2)),
                'size': int(match.group(3)),
                'raw_line': line
            }
        
        # Alternative pattern for different output formats
        alt_pattern = r'^([^\s]+)\s+(\d+)'
        alt_match = re.match(alt_pattern, line)
        
        if alt_match:
            return {
                'path': alt_match.group(1),
                'status_code': int(alt_match.group(2)),
                'size': None,
                'raw_line': line
            }
        
        return None
    
    def _extract_findings(self, parsed_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract security findings from Gobuster results"""
        findings = []
        
        target = parsed_data.get('target', 'unknown')
        
        for path_info in parsed_data.get('found_paths', []):
            path = path_info.get('path')
            status_code = path_info.get('status_code')
            size = path_info.get('size')
            
            # Assess severity based on path and status code
            severity = self._assess_path_severity(path, status_code)
            
            # Build full URL
            full_url = urljoin(target, path) if not path.startswith('http') else path
            
            finding = {
                'type': 'discovered_path',
                'severity': severity,
                'target': target,
                'path': path,
                'full_url': full_url,
                'status_code': status_code,
                'size': size,
                'description': f"Discovered path: {path} (Status: {status_code})",
                'recommendation': self._get_path_recommendation(path, status_code)
            }
            findings.append(finding)
        
        return findings
    
    def _assess_path_severity(self, path: str, status_code: int) -> str:
        """Assess the severity of a discovered path"""
        path_lower = path.lower()
        
        # High severity paths
        if any(keyword in path_lower for keyword in [
            'admin', 'administrator', 'config', 'backup', 'database', 'db',
            'phpmyadmin', 'wp-admin', 'cpanel', 'webmail'
        ]):
            return Severity.HIGH.value
        
        # Medium severity paths
        if any(keyword in path_lower for keyword in [
            'login', 'auth', 'user', 'account', 'private', 'internal',
            'test', 'dev', 'staging', 'debug'
        ]):
            return Severity.MEDIUM.value
        
        # Low severity for common files/directories
        if any(keyword in path_lower for keyword in [
            'images', 'css', 'js', 'fonts', 'assets', 'static',
            'uploads', 'downloads', 'docs', 'help'
        ]):
            return Severity.LOW.value
        
        # Info for everything else
        return Severity.INFO.value
    
    def _get_path_recommendation(self, path: str, status_code: int) -> str:
        """Get security recommendation for a discovered path"""
        path_lower = path.lower()
        
        if status_code == 403:
            return 'Directory is forbidden but exists - ensure proper access controls'
        
        if any(keyword in path_lower for keyword in ['admin', 'config', 'backup']):
            return 'Sensitive directory discovered - restrict access and consider moving to non-public location'
        
        if any(keyword in path_lower for keyword in ['login', 'auth']):
            return 'Authentication endpoint discovered - ensure proper security measures are in place'
        
        if any(keyword in path_lower for keyword in ['test', 'dev', 'debug']):
            return 'Development/testing directory found - remove from production environment'
        
        return 'Review if this path should be publicly accessible'
    
    def validate_target(self, target: str) -> bool:
        """Validate Gobuster target URL format"""
        if not target or not target.strip():
            return False
        
        try:
            parsed = urlparse(target)
            return bool(parsed.scheme and parsed.netloc)
        except Exception:
            return False
