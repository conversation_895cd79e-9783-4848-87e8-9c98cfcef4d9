"""
Advanced workflow system for tool connectors.

This module provides an enhanced workflow system with conditional execution,
dependency management, parallel/sequential execution control, and custom workflow definitions.
"""

import asyncio
import logging
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Callable, Union
import time

from .base import ToolR<PERSON>ult, ToolStatus


class ExecutionMode(Enum):
    """Execution mode for workflow steps."""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"


class WorkflowStatus(Enum):
    """Status of workflow execution."""
    NOT_STARTED = "not_started"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PARTIAL = "partial"


@dataclass
class WorkflowStep:
    """
    Represents a single step in a workflow.
    """
    name: str
    tool_name: str
    config: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    conditions: List[Callable[[Dict[str, ToolResult]], bool]] = field(default_factory=list)
    execution_mode: ExecutionMode = ExecutionMode.SEQUENTIAL
    timeout: Optional[int] = None
    retry_count: int = 0
    retry_delay: float = 1.0
    continue_on_failure: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Post-initialization validation."""
        if not self.name:
            raise ValueError("Step name cannot be empty")
        if not self.tool_name:
            raise ValueError("Tool name cannot be empty")


@dataclass
class WorkflowResult:
    """
    Result of workflow execution.
    """
    workflow_name: str
    status: WorkflowStatus
    start_time: float
    end_time: Optional[float] = None
    step_results: Dict[str, ToolResult] = field(default_factory=dict)
    failed_steps: List[str] = field(default_factory=list)
    skipped_steps: List[str] = field(default_factory=list)
    execution_order: List[str] = field(default_factory=list)
    total_findings: int = 0
    error_message: Optional[str] = None
    
    @property
    def execution_time(self) -> float:
        """Get total execution time."""
        if self.end_time is None:
            return time.time() - self.start_time
        return self.end_time - self.start_time
    
    @property
    def success_rate(self) -> float:
        """Get success rate of executed steps."""
        total_steps = len(self.step_results)
        if total_steps == 0:
            return 0.0
        failed_count = len(self.failed_steps)
        return (total_steps - failed_count) / total_steps


class WorkflowDefinition:
    """
    Defines a workflow with steps and execution logic.
    """
    
    def __init__(self, name: str, description: str = ""):
        """
        Initialize workflow definition.
        
        Args:
            name: Workflow name
            description: Workflow description
        """
        self.name = name
        self.description = description
        self.steps: Dict[str, WorkflowStep] = {}
        self.metadata: Dict[str, Any] = {}
        self.logger = logging.getLogger(f"{self.__class__.__name__}.{name}")
    
    def add_step(self, step: WorkflowStep) -> 'WorkflowDefinition':
        """
        Add a step to the workflow.
        
        Args:
            step: Workflow step to add
            
        Returns:
            Self for method chaining
        """
        if step.name in self.steps:
            raise ValueError(f"Step '{step.name}' already exists in workflow")
        
        # Validate dependencies
        for dep in step.dependencies:
            if dep not in self.steps:
                self.logger.warning(f"Dependency '{dep}' for step '{step.name}' not found (yet)")
        
        self.steps[step.name] = step
        return self
    
    def remove_step(self, step_name: str) -> 'WorkflowDefinition':
        """
        Remove a step from the workflow.
        
        Args:
            step_name: Name of step to remove
            
        Returns:
            Self for method chaining
        """
        if step_name not in self.steps:
            raise ValueError(f"Step '{step_name}' not found in workflow")
        
        # Check if other steps depend on this one
        dependents = [name for name, step in self.steps.items() 
                     if step_name in step.dependencies]
        if dependents:
            raise ValueError(f"Cannot remove step '{step_name}': "
                           f"steps {dependents} depend on it")
        
        del self.steps[step_name]
        return self
    
    def get_execution_order(self) -> List[str]:
        """
        Get the execution order of steps based on dependencies.
        
        Returns:
            List of step names in execution order
        """
        # Topological sort to handle dependencies
        visited = set()
        temp_visited = set()
        order = []
        
        def visit(step_name: str):
            if step_name in temp_visited:
                raise ValueError(f"Circular dependency detected involving '{step_name}'")
            if step_name in visited:
                return
            
            temp_visited.add(step_name)
            
            # Visit dependencies first
            step = self.steps[step_name]
            for dep in step.dependencies:
                if dep in self.steps:
                    visit(dep)
            
            temp_visited.remove(step_name)
            visited.add(step_name)
            order.append(step_name)
        
        # Visit all steps
        for step_name in self.steps:
            if step_name not in visited:
                visit(step_name)
        
        return order
    
    def validate(self) -> List[str]:
        """
        Validate the workflow definition.
        
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        if not self.steps:
            errors.append("Workflow has no steps")
            return errors
        
        # Check for circular dependencies
        try:
            self.get_execution_order()
        except ValueError as e:
            errors.append(str(e))
        
        # Validate each step
        for step_name, step in self.steps.items():
            # Check dependencies exist
            for dep in step.dependencies:
                if dep not in self.steps:
                    errors.append(f"Step '{step_name}' depends on non-existent step '{dep}'")
        
        return errors


class WorkflowEngine:
    """
    Engine for executing workflows with advanced features.
    """
    
    def __init__(self, tool_manager):
        """
        Initialize workflow engine.
        
        Args:
            tool_manager: ToolManager instance for executing tools
        """
        self.tool_manager = tool_manager
        self.logger = logging.getLogger(self.__class__.__name__)
        self._running_workflows: Dict[str, WorkflowResult] = {}
    
    async def execute_workflow(self, workflow: WorkflowDefinition, target: str,
                             global_config: Optional[Dict[str, Any]] = None) -> WorkflowResult:
        """
        Execute a workflow.
        
        Args:
            workflow: Workflow definition to execute
            target: Target for the workflow
            global_config: Global configuration for all steps
            
        Returns:
            Workflow execution result
        """
        # Validate workflow
        errors = workflow.validate()
        if errors:
            raise ValueError(f"Workflow validation failed: {'; '.join(errors)}")
        
        # Initialize result
        result = WorkflowResult(
            workflow_name=workflow.name,
            status=WorkflowStatus.RUNNING,
            start_time=time.time()
        )
        
        self._running_workflows[workflow.name] = result
        
        try:
            # Get execution order
            execution_order = workflow.get_execution_order()
            result.execution_order = execution_order
            
            self.logger.info(f"Starting workflow '{workflow.name}' with {len(execution_order)} steps")
            
            # Execute steps
            for step_name in execution_order:
                step = workflow.steps[step_name]
                
                # Check if step should be executed
                if not self._should_execute_step(step, result.step_results):
                    result.skipped_steps.append(step_name)
                    self.logger.info(f"Skipping step '{step_name}' due to conditions")
                    continue
                
                # Execute step
                step_result = await self._execute_step(step, target, global_config, result.step_results)
                result.step_results[step_name] = step_result
                
                # Handle step failure
                if step_result.status == ToolStatus.FAILED:
                    result.failed_steps.append(step_name)
                    
                    if not step.continue_on_failure:
                        self.logger.error(f"Step '{step_name}' failed, stopping workflow")
                        result.status = WorkflowStatus.FAILED
                        result.error_message = f"Step '{step_name}' failed: {step_result.error_message}"
                        break
                    else:
                        self.logger.warning(f"Step '{step_name}' failed but continuing due to continue_on_failure=True")
            
            # Determine final status
            if result.status == WorkflowStatus.RUNNING:
                if result.failed_steps:
                    result.status = WorkflowStatus.PARTIAL
                else:
                    result.status = WorkflowStatus.COMPLETED
            
            # Calculate total findings
            result.total_findings = sum(len(r.findings) for r in result.step_results.values())
            
            self.logger.info(f"Workflow '{workflow.name}' completed with status {result.status.value}")
            
        except Exception as e:
            result.status = WorkflowStatus.FAILED
            result.error_message = str(e)
            self.logger.error(f"Workflow '{workflow.name}' failed: {e}")
        
        finally:
            result.end_time = time.time()
            if workflow.name in self._running_workflows:
                del self._running_workflows[workflow.name]
        
        return result
    
    def _should_execute_step(self, step: WorkflowStep, 
                           previous_results: Dict[str, ToolResult]) -> bool:
        """
        Check if a step should be executed based on conditions.
        
        Args:
            step: Step to check
            previous_results: Results from previous steps
            
        Returns:
            True if step should be executed
        """
        # Check dependencies are satisfied
        for dep in step.dependencies:
            if dep not in previous_results:
                return False
            if previous_results[dep].status == ToolStatus.FAILED:
                return False
        
        # Check custom conditions
        for condition in step.conditions:
            try:
                if not condition(previous_results):
                    return False
            except Exception as e:
                self.logger.warning(f"Condition check failed for step '{step.name}': {e}")
                return False
        
        return True
    
    async def _execute_step(self, step: WorkflowStep, target: str,
                          global_config: Optional[Dict[str, Any]],
                          previous_results: Dict[str, ToolResult]) -> ToolResult:
        """
        Execute a single workflow step.
        
        Args:
            step: Step to execute
            target: Target for execution
            global_config: Global configuration
            previous_results: Results from previous steps
            
        Returns:
            Tool execution result
        """
        # Merge configurations
        config = {}
        if global_config:
            config.update(global_config)
        config.update(step.config)
        
        # Add timeout if specified
        if step.timeout:
            config['timeout'] = step.timeout
        
        self.logger.info(f"Executing step '{step.name}' with tool '{step.tool_name}'")
        
        # Execute with retry logic
        last_exception = None
        for attempt in range(step.retry_count + 1):
            try:
                if attempt > 0:
                    self.logger.info(f"Retrying step '{step.name}' (attempt {attempt + 1})")
                    await asyncio.sleep(step.retry_delay)
                
                result = await self.tool_manager.execute_tool_async(
                    step.tool_name, target, **config
                )
                
                if result.status != ToolStatus.FAILED:
                    return result
                
                last_exception = Exception(result.error_message or "Tool execution failed")
                
            except Exception as e:
                last_exception = e
                self.logger.warning(f"Step '{step.name}' attempt {attempt + 1} failed: {e}")
        
        # All attempts failed
        self.logger.error(f"Step '{step.name}' failed after {step.retry_count + 1} attempts")
        
        # Create failed result
        failed_result = ToolResult(
            tool_name=step.tool_name,
            command=f"Failed step: {step.name}",
            status=ToolStatus.FAILED,
            error_message=str(last_exception) if last_exception else "Unknown error"
        )
        
        return failed_result
    
    def get_running_workflows(self) -> Dict[str, WorkflowResult]:
        """Get currently running workflows."""
        return self._running_workflows.copy()
    
    def cancel_workflow(self, workflow_name: str) -> bool:
        """
        Cancel a running workflow.
        
        Args:
            workflow_name: Name of workflow to cancel
            
        Returns:
            True if workflow was cancelled, False if not found
        """
        if workflow_name in self._running_workflows:
            result = self._running_workflows[workflow_name]
            result.status = WorkflowStatus.CANCELLED
            result.end_time = time.time()
            del self._running_workflows[workflow_name]
            self.logger.info(f"Cancelled workflow '{workflow_name}'")
            return True
        return False


# Predefined workflow builders
class WorkflowBuilder:
    """Builder for creating common workflow patterns."""

    @staticmethod
    def create_reconnaissance_workflow(name: str = "reconnaissance") -> WorkflowDefinition:
        """Create a reconnaissance workflow."""
        workflow = WorkflowDefinition(name, "Comprehensive reconnaissance workflow")

        # Network discovery
        workflow.add_step(WorkflowStep(
            name="network_scan",
            tool_name="nmap",
            config={
                "scan_type": "tcp_syn",
                "service_detection": True,
                "timing": 4,
                "ports": "1-1000"
            }
        ))

        # Web application discovery (conditional on HTTP/HTTPS ports)
        def has_web_ports(results):
            if "network_scan" not in results:
                return False
            nmap_result = results["network_scan"]
            # Check if findings contain web ports
            for finding in nmap_result.findings:
                if finding.get('port') in ['80', '443', '8080', '8443']:
                    return True
            return False

        workflow.add_step(WorkflowStep(
            name="web_scan",
            tool_name="nikto",
            dependencies=["network_scan"],
            conditions=[has_web_ports],
            config={"timeout": 30}
        ))

        workflow.add_step(WorkflowStep(
            name="directory_scan",
            tool_name="gobuster",
            dependencies=["web_scan"],
            config={
                "mode": "dir",
                "threads": 20,
                "extensions": ["php", "html", "txt"]
            },
            continue_on_failure=True
        ))

        return workflow

    @staticmethod
    def create_vulnerability_assessment_workflow(name: str = "vuln_assessment") -> WorkflowDefinition:
        """Create a vulnerability assessment workflow."""
        workflow = WorkflowDefinition(name, "Vulnerability assessment workflow")

        # Initial port scan
        workflow.add_step(WorkflowStep(
            name="port_scan",
            tool_name="nmap",
            config={
                "scan_type": "tcp_syn",
                "service_detection": True,
                "scripts": ["vuln", "safe"],
                "timing": 3
            }
        ))

        # Web vulnerability scan
        def has_web_services(results):
            if "port_scan" not in results:
                return False
            # Check for web services in findings
            for finding in results["port_scan"].findings:
                service = finding.get('service', '').lower()
                if any(web_service in service for web_service in ['http', 'https', 'web']):
                    return True
            return False

        workflow.add_step(WorkflowStep(
            name="web_vuln_scan",
            tool_name="nikto",
            dependencies=["port_scan"],
            conditions=[has_web_services],
            config={
                "plugins": "@@ALL",
                "timeout": 60
            },
            retry_count=1,
            retry_delay=5.0
        ))

        # SQL injection testing
        workflow.add_step(WorkflowStep(
            name="sql_injection_test",
            tool_name="sqlmap",
            dependencies=["web_vuln_scan"],
            config={
                "level": 2,
                "risk": 2,
                "batch": True
            },
            continue_on_failure=True,
            timeout=1800
        ))

        return workflow

    @staticmethod
    def create_conditional_workflow(name: str = "conditional_scan") -> WorkflowDefinition:
        """Create a workflow with complex conditional logic."""
        workflow = WorkflowDefinition(name, "Conditional scanning workflow")

        # Initial discovery
        workflow.add_step(WorkflowStep(
            name="discovery",
            tool_name="nmap",
            config={"scan_type": "tcp_syn", "timing": 5, "ports": "80,443,22,21,25"}
        ))

        # SSH-specific tests
        def has_ssh(results):
            if "discovery" not in results:
                return False
            for finding in results["discovery"].findings:
                if finding.get('port') == '22' or 'ssh' in finding.get('service', '').lower():
                    return True
            return False

        workflow.add_step(WorkflowStep(
            name="ssh_scan",
            tool_name="nmap",
            dependencies=["discovery"],
            conditions=[has_ssh],
            config={
                "scripts": ["ssh-auth-methods", "ssh-hostkey"],
                "ports": "22"
            }
        ))

        # Web-specific tests
        def has_web(results):
            if "discovery" not in results:
                return False
            for finding in results["discovery"].findings:
                port = finding.get('port')
                service = finding.get('service', '').lower()
                if port in ['80', '443'] or 'http' in service:
                    return True
            return False

        workflow.add_step(WorkflowStep(
            name="web_scan",
            tool_name="nikto",
            dependencies=["discovery"],
            conditions=[has_web],
            config={"ssl": True, "timeout": 45}
        ))

        return workflow
