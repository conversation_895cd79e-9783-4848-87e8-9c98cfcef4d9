#!/usr/bin/env python3
"""
Caching and Persistence Demo

This script demonstrates the caching and persistence capabilities of the tool connector framework.
It shows how to:
1. Use different cache backends (memory, file, SQLite)
2. Configure cache policies and TTL
3. Query cached results
4. Manage cache lifecycle
5. Integrate caching with tool execution
"""

import sys
import time
from pathlib import Path

# Add the parent directory to the path so we can import tool_connectors
sys.path.insert(0, str(Path(__file__).parent.parent))

from tool_connectors import ToolManager
from tool_connectors.cache import MemoryCache, FileCache, SQLiteCache, CacheManager


def demo_memory_cache():
    """Demonstrate memory cache backend"""
    print("=== Memory Cache Demo ===")
    
    cache = MemoryCache()
    
    # Store some data
    data1 = {'tool': 'nmap', 'target': 'example.com', 'ports': [80, 443]}
    data2 = {'tool': 'nikto', 'target': 'example.com', 'findings': 5}
    
    cache.set('nmap:example.com:1', data1)
    cache.set('nikto:example.com:1', data2)
    
    print(f"Stored {len(cache.keys())} entries in memory cache")
    
    # Retrieve data
    retrieved = cache.get('nmap:example.com:1')
    print(f"Retrieved: {retrieved}")
    
    # Test TTL
    cache.set('ttl_test', {'data': 'expires soon'}, ttl=2)
    print(f"TTL test data exists: {cache.exists('ttl_test')}")
    
    time.sleep(2.1)
    print(f"TTL test data exists after expiry: {cache.exists('ttl_test')}")
    
    # Pattern matching
    nmap_keys = cache.keys('nmap:*')
    print(f"Keys matching 'nmap:*': {nmap_keys}")
    
    print()


def demo_file_cache():
    """Demonstrate file cache backend"""
    print("=== File Cache Demo ===")
    
    cache_dir = "./demo_cache"
    cache = FileCache(cache_dir)
    
    # Store data
    scan_result = {
        'tool_name': 'nmap',
        'target': 'demo.example.com',
        'status': 'completed',
        'findings': [
            {'type': 'open_port', 'port': 80, 'service': 'http'},
            {'type': 'open_port', 'port': 443, 'service': 'https'}
        ],
        'execution_time': 15.3
    }
    
    cache.set('file_demo_key', scan_result)
    print(f"Stored scan result in file cache at: {cache_dir}")
    
    # Retrieve data
    retrieved = cache.get('file_demo_key')
    print(f"Retrieved scan result: {retrieved['tool_name']} -> {retrieved['target']}")
    print(f"Found {len(retrieved['findings'])} findings")
    
    # Show persistence
    cache2 = FileCache(cache_dir)
    persistent_data = cache2.get('file_demo_key')
    print(f"Data persists across cache instances: {persistent_data is not None}")
    
    # Cleanup
    cache.clear()
    print("Cleaned up file cache")
    
    print()


def demo_sqlite_cache():
    """Demonstrate SQLite cache backend with querying"""
    print("=== SQLite Cache Demo ===")
    
    cache = SQLiteCache("./demo_cache.db")
    
    # Store multiple scan results
    results = [
        {
            'tool_name': 'nmap',
            'target': 'server1.example.com',
            'status': 'completed',
            'findings': [{'port': 22}, {'port': 80}]
        },
        {
            'tool_name': 'nikto',
            'target': 'server1.example.com',
            'status': 'completed',
            'findings': [{'vuln': 'outdated_server'}]
        },
        {
            'tool_name': 'nmap',
            'target': 'server2.example.com',
            'status': 'failed',
            'findings': []
        },
        {
            'tool_name': 'sqlmap',
            'target': 'app.example.com',
            'status': 'completed',
            'findings': [{'injection': 'sql_injection_found'}]
        }
    ]
    
    # Store results
    for i, result in enumerate(results):
        cache.set(f'demo_key_{i}', result)
    
    print(f"Stored {len(results)} results in SQLite cache")
    
    # Query by tool name
    nmap_results = cache.query(tool_name='nmap')
    print(f"Found {len(nmap_results)} nmap results")
    
    # Query by target
    server1_results = cache.query(target='server1')
    print(f"Found {len(server1_results)} results for server1")
    
    # Query by status
    completed_results = cache.query(status='completed')
    print(f"Found {len(completed_results)} completed scans")
    
    # Query with limit
    recent_results = cache.query(limit=2)
    print(f"Most recent 2 results:")
    for result in recent_results:
        print(f"  - {result['tool_name']} on {result['target']}: {result['status']}")
    
    # Cleanup
    cache.clear()
    print("Cleaned up SQLite cache")
    
    print()


def demo_cache_manager():
    """Demonstrate cache manager with policies"""
    print("=== Cache Manager Demo ===")
    
    # Create cache manager with custom policies
    cache_manager = CacheManager(
        backend=MemoryCache(),
        default_ttl=1800,  # 30 minutes
        enabled=True
    )
    
    # Show cache policies
    print("Cache policies:")
    for tool, policy in cache_manager.tool_policies.items():
        print(f"  {tool}: TTL={policy['ttl']}s, cache_on_error={policy['cache_on_error']}")
    
    # Generate cache keys
    key1 = cache_manager.generate_cache_key('nmap', 'example.com', timing=4, ports='1-1000')
    key2 = cache_manager.generate_cache_key('nmap', 'example.com', timing=4, ports='1-1000')
    key3 = cache_manager.generate_cache_key('nmap', 'example.com', timing=5, ports='1-1000')
    
    print(f"\nCache key consistency:")
    print(f"Same parameters generate same key: {key1 == key2}")
    print(f"Different parameters generate different keys: {key1 != key3}")
    
    # Test caching policies
    from tool_connectors.base import ToolResult, ToolStatus
    
    successful_result = ToolResult(
        tool_name='nmap',
        command='nmap -sS example.com',
        status=ToolStatus.COMPLETED,
        findings=[{'port': 80}]
    )
    
    failed_result = ToolResult(
        tool_name='nmap',
        command='nmap -sS unreachable.com',
        status=ToolStatus.FAILED,
        error_message='Host unreachable'
    )
    
    print(f"\nCaching policies:")
    print(f"Should cache successful nmap result: {cache_manager.should_cache_result('nmap', successful_result)}")
    print(f"Should cache failed nmap result: {cache_manager.should_cache_result('nmap', failed_result)}")
    print(f"Should cache failed sqlmap result: {cache_manager.should_cache_result('sqlmap', failed_result)}")
    
    # Cache and retrieve results
    cache_manager.cache_result('nmap', 'example.com', successful_result, timing=4)
    cached = cache_manager.get_cached_result('nmap', 'example.com', timing=4)
    
    print(f"\nCached result retrieved: {cached is not None}")
    if cached:
        print(f"Cached result status: {cached.status.value}")
    
    # Cache statistics
    stats = cache_manager.get_cache_stats()
    print(f"\nCache statistics:")
    print(f"  Total entries: {stats['total_entries']}")
    print(f"  Backend type: {stats['backend_type']}")
    print(f"  Tool breakdown: {stats['tool_breakdown']}")
    
    print()


def demo_tool_manager_caching():
    """Demonstrate caching integration with ToolManager"""
    print("=== ToolManager Caching Integration Demo ===")
    
    # Create ToolManager with different cache backends
    print("Testing different cache backends:")
    
    # Memory cache
    manager_memory = ToolManager(mock_mode=True, cache_enabled=True, cache_backend='memory')
    print(f"Memory cache backend: {manager_memory.cache_manager.backend.__class__.__name__}")
    
    # File cache
    manager_file = ToolManager(mock_mode=True, cache_enabled=True, cache_backend='file')
    print(f"File cache backend: {manager_file.cache_manager.backend.__class__.__name__}")
    
    # SQLite cache
    manager_sqlite = ToolManager(mock_mode=True, cache_enabled=True, cache_backend='sqlite')
    print(f"SQLite cache backend: {manager_sqlite.cache_manager.backend.__class__.__name__}")
    
    # Demonstrate caching behavior
    print(f"\nDemonstrating cache behavior:")
    
    manager = manager_memory  # Use memory cache for demo
    
    # First execution (not cached)
    print("First execution (should execute tool):")
    start_time = time.time()
    result1 = manager.execute_tool('nmap', 'cache-demo.com', timing=4, ports='80,443')
    first_time = time.time() - start_time
    print(f"  Status: {result1.status.value}")
    print(f"  Execution time: {first_time:.3f}s")
    print(f"  Findings: {len(result1.findings)}")
    
    # Second execution (should use cache)
    print("Second execution (should use cache):")
    start_time = time.time()
    result2 = manager.execute_tool('nmap', 'cache-demo.com', timing=4, ports='80,443')
    second_time = time.time() - start_time
    print(f"  Status: {result2.status.value}")
    print(f"  Execution time: {second_time:.3f}s")
    print(f"  Findings: {len(result2.findings)}")
    print(f"  Cache hit (faster): {second_time < first_time}")
    
    # Different parameters (should execute tool again)
    print("Different parameters (should execute tool):")
    start_time = time.time()
    result3 = manager.execute_tool('nmap', 'cache-demo.com', timing=5, ports='80,443')
    third_time = time.time() - start_time
    print(f"  Status: {result3.status.value}")
    print(f"  Execution time: {third_time:.3f}s")
    print(f"  Cache miss (slower): {third_time > second_time}")
    
    # Cache management
    print(f"\nCache management:")
    stats = manager.get_cache_stats()
    print(f"  Total cached entries: {stats['total_entries']}")
    
    # Invalidate specific tool cache
    invalidated = manager.invalidate_cache(tool_name='nmap')
    print(f"  Invalidated {invalidated} nmap entries")
    
    # Verify cache is cleared
    stats = manager.get_cache_stats()
    print(f"  Remaining entries: {stats['total_entries']}")
    
    print()


def demo_cache_querying():
    """Demonstrate cache querying capabilities"""
    print("=== Cache Querying Demo ===")
    
    # Use SQLite backend for querying
    manager = ToolManager(mock_mode=True, cache_enabled=True, cache_backend='sqlite')
    
    # Execute various tools to populate cache
    targets = ['web1.example.com', 'web2.example.com', 'db.example.com']
    tools = ['nmap', 'nikto', 'sqlmap']
    
    print("Populating cache with scan results...")
    for target in targets:
        for tool in tools:
            if tool == 'sqlmap' and 'db' not in target:
                continue  # Only run sqlmap on database servers
            manager.execute_tool(tool, target)
    
    # Query cached results
    print(f"\nQuerying cached results:")
    
    # Query by tool
    nmap_results = manager.query_cached_results(tool_name='nmap')
    print(f"  Nmap results: {len(nmap_results)}")
    
    # Query by target pattern
    web_results = manager.query_cached_results(target='web')
    print(f"  Web server results: {len(web_results)}")
    
    # Query with limit
    recent_results = manager.query_cached_results(limit=3)
    print(f"  Most recent 3 results:")
    for result in recent_results:
        data = result['data']
        print(f"    - {data.get('tool_name', 'unknown')} on {result['target']}")
    
    # Cleanup
    manager.clear_cache()
    print("Cache cleared")
    
    print()


def main():
    """Main function to run all cache demos"""
    print("Tool Connectors Framework - Caching and Persistence Demo")
    print("=" * 70)
    print("This demo shows caching and persistence capabilities including:")
    print("- Different cache backends (memory, file, SQLite)")
    print("- Cache policies and TTL management")
    print("- Result querying and filtering")
    print("- Integration with ToolManager")
    print("- Cache lifecycle management")
    print()
    
    try:
        demo_memory_cache()
        demo_file_cache()
        demo_sqlite_cache()
        demo_cache_manager()
        demo_tool_manager_caching()
        demo_cache_querying()
        
        print("=== Cache Demo Summary ===")
        print("All caching demos completed successfully!")
        print("The caching system supports:")
        print("- Multiple backend types with different persistence levels")
        print("- Configurable TTL and cache policies per tool")
        print("- Automatic cache key generation based on parameters")
        print("- Advanced querying capabilities with SQLite backend")
        print("- Seamless integration with tool execution")
        print("- Cache invalidation and lifecycle management")
        
    except Exception as e:
        print(f"Error during demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
