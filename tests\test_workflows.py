"""
Unit tests for advanced workflow system.
"""

import asyncio
import unittest
from unittest.mock import Mock, AsyncMock

from tool_connectors.workflows import (
    WorkflowDefinition, WorkflowStep, WorkflowEngine, WorkflowBuilder,
    ExecutionMode, WorkflowStatus
)
from tool_connectors.base import <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolStatus
from tool_connectors import ToolManager


class TestWorkflowStep(unittest.TestCase):
    """Test cases for WorkflowStep"""
    
    def test_step_creation(self):
        """Test creating a workflow step"""
        step = WorkflowStep(
            name="test_step",
            tool_name="nmap",
            config={"ports": "80,443"},
            dependencies=["previous_step"],
            timeout=300
        )
        
        self.assertEqual(step.name, "test_step")
        self.assertEqual(step.tool_name, "nmap")
        self.assertEqual(step.config["ports"], "80,443")
        self.assertEqual(step.dependencies, ["previous_step"])
        self.assertEqual(step.timeout, 300)
        self.assertEqual(step.execution_mode, ExecutionMode.SEQUENTIAL)
    
    def test_step_validation(self):
        """Test step validation"""
        # Valid step
        step = WorkflowStep(name="valid", tool_name="nmap")
        self.assertEqual(step.name, "valid")
        
        # Invalid steps
        with self.assertRaises(ValueError):
            WorkflowStep(name="", tool_name="nmap")
        
        with self.assertRaises(ValueError):
            WorkflowStep(name="test", tool_name="")


class TestWorkflowDefinition(unittest.TestCase):
    """Test cases for WorkflowDefinition"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.workflow = WorkflowDefinition("test_workflow", "Test workflow")
    
    def test_workflow_creation(self):
        """Test creating a workflow"""
        self.assertEqual(self.workflow.name, "test_workflow")
        self.assertEqual(self.workflow.description, "Test workflow")
        self.assertEqual(len(self.workflow.steps), 0)
    
    def test_add_step(self):
        """Test adding steps to workflow"""
        step1 = WorkflowStep("step1", "nmap")
        step2 = WorkflowStep("step2", "nikto", dependencies=["step1"])
        
        self.workflow.add_step(step1)
        self.workflow.add_step(step2)
        
        self.assertEqual(len(self.workflow.steps), 2)
        self.assertIn("step1", self.workflow.steps)
        self.assertIn("step2", self.workflow.steps)
    
    def test_duplicate_step_name(self):
        """Test adding step with duplicate name"""
        step1 = WorkflowStep("duplicate", "nmap")
        step2 = WorkflowStep("duplicate", "nikto")
        
        self.workflow.add_step(step1)
        
        with self.assertRaises(ValueError):
            self.workflow.add_step(step2)
    
    def test_remove_step(self):
        """Test removing steps from workflow"""
        step1 = WorkflowStep("step1", "nmap")
        step2 = WorkflowStep("step2", "nikto", dependencies=["step1"])
        
        self.workflow.add_step(step1)
        self.workflow.add_step(step2)
        
        # Cannot remove step with dependents
        with self.assertRaises(ValueError):
            self.workflow.remove_step("step1")
        
        # Can remove step without dependents
        self.workflow.remove_step("step2")
        self.assertNotIn("step2", self.workflow.steps)
        
        # Now can remove step1
        self.workflow.remove_step("step1")
        self.assertNotIn("step1", self.workflow.steps)
    
    def test_execution_order(self):
        """Test getting execution order"""
        step1 = WorkflowStep("step1", "nmap")
        step2 = WorkflowStep("step2", "nikto", dependencies=["step1"])
        step3 = WorkflowStep("step3", "gobuster", dependencies=["step2"])
        step4 = WorkflowStep("step4", "sqlmap", dependencies=["step1"])
        
        self.workflow.add_step(step1)
        self.workflow.add_step(step2)
        self.workflow.add_step(step3)
        self.workflow.add_step(step4)
        
        order = self.workflow.get_execution_order()
        
        # step1 should be first
        self.assertEqual(order[0], "step1")
        
        # step2 and step4 should come after step1
        step1_index = order.index("step1")
        step2_index = order.index("step2")
        step4_index = order.index("step4")
        
        self.assertGreater(step2_index, step1_index)
        self.assertGreater(step4_index, step1_index)
        
        # step3 should come after step2
        step3_index = order.index("step3")
        self.assertGreater(step3_index, step2_index)
    
    def test_circular_dependency_detection(self):
        """Test detection of circular dependencies"""
        step1 = WorkflowStep("step1", "nmap", dependencies=["step2"])
        step2 = WorkflowStep("step2", "nikto", dependencies=["step1"])
        
        self.workflow.add_step(step1)
        self.workflow.add_step(step2)
        
        with self.assertRaises(ValueError):
            self.workflow.get_execution_order()
    
    def test_workflow_validation(self):
        """Test workflow validation"""
        # Empty workflow
        errors = self.workflow.validate()
        self.assertIn("no steps", errors[0])
        
        # Valid workflow
        step1 = WorkflowStep("step1", "nmap")
        step2 = WorkflowStep("step2", "nikto", dependencies=["step1"])
        
        self.workflow.add_step(step1)
        self.workflow.add_step(step2)
        
        errors = self.workflow.validate()
        self.assertEqual(len(errors), 0)
        
        # Invalid dependency
        step3 = WorkflowStep("step3", "gobuster", dependencies=["nonexistent"])
        self.workflow.add_step(step3)
        
        errors = self.workflow.validate()
        self.assertGreater(len(errors), 0)
        self.assertIn("non-existent", errors[0])


class TestWorkflowEngine(unittest.TestCase):
    """Test cases for WorkflowEngine"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_tool_manager = Mock()
        self.engine = WorkflowEngine(self.mock_tool_manager)
    
    def test_engine_creation(self):
        """Test creating workflow engine"""
        self.assertEqual(self.engine.tool_manager, self.mock_tool_manager)
        self.assertEqual(len(self.engine._running_workflows), 0)
    
    def test_should_execute_step(self):
        """Test step execution conditions"""
        # Step with no dependencies or conditions
        step = WorkflowStep("test", "nmap")
        self.assertTrue(self.engine._should_execute_step(step, {}))
        
        # Step with satisfied dependency
        step_with_dep = WorkflowStep("test", "nmap", dependencies=["dep1"])
        mock_result = Mock()
        mock_result.status = ToolStatus.COMPLETED
        results = {"dep1": mock_result}
        
        self.assertTrue(self.engine._should_execute_step(step_with_dep, results))
        
        # Step with failed dependency
        mock_result.status = ToolStatus.FAILED
        self.assertFalse(self.engine._should_execute_step(step_with_dep, results))
        
        # Step with custom condition
        def always_true(results):
            return True
        
        def always_false(results):
            return False
        
        step_with_condition = WorkflowStep("test", "nmap", conditions=[always_true])
        self.assertTrue(self.engine._should_execute_step(step_with_condition, {}))
        
        step_with_condition = WorkflowStep("test", "nmap", conditions=[always_false])
        self.assertFalse(self.engine._should_execute_step(step_with_condition, {}))
    
    def test_execute_workflow_simple(self):
        """Test executing a simple workflow"""
        async def run_test():
            # Create simple workflow
            workflow = WorkflowDefinition("test", "Test workflow")
            workflow.add_step(WorkflowStep("step1", "nmap"))
            
            # Mock tool execution
            mock_result = Mock()
            mock_result.status = ToolStatus.COMPLETED
            mock_result.findings = []
            
            self.mock_tool_manager.execute_tool_async = AsyncMock(return_value=mock_result)
            
            # Execute workflow
            result = await self.engine.execute_workflow(workflow, "example.com")
            
            # Check result
            self.assertEqual(result.workflow_name, "test")
            self.assertEqual(result.status, WorkflowStatus.COMPLETED)
            self.assertIn("step1", result.step_results)
            self.assertEqual(len(result.failed_steps), 0)
            self.assertEqual(len(result.skipped_steps), 0)
        
        asyncio.run(run_test())
    
    def test_execute_workflow_with_failure(self):
        """Test executing workflow with step failure"""
        async def run_test():
            # Create workflow with continue_on_failure=False
            workflow = WorkflowDefinition("test", "Test workflow")
            workflow.add_step(WorkflowStep("step1", "nmap"))
            workflow.add_step(WorkflowStep("step2", "nikto", dependencies=["step1"]))
            
            # Mock first step failure
            mock_result1 = Mock()
            mock_result1.status = ToolStatus.FAILED
            mock_result1.error_message = "Tool failed"
            mock_result1.findings = []
            
            self.mock_tool_manager.execute_tool_async = AsyncMock(return_value=mock_result1)
            
            # Execute workflow
            result = await self.engine.execute_workflow(workflow, "example.com")
            
            # Check result
            self.assertEqual(result.status, WorkflowStatus.FAILED)
            self.assertIn("step1", result.failed_steps)
            self.assertEqual(len(result.step_results), 1)  # Only first step executed
        
        asyncio.run(run_test())
    
    def test_execute_workflow_with_conditions(self):
        """Test executing workflow with conditional steps"""
        async def run_test():
            # Create workflow with conditional step
            workflow = WorkflowDefinition("test", "Test workflow")
            workflow.add_step(WorkflowStep("step1", "nmap"))
            
            # Condition that always returns False
            def never_execute(results):
                return False
            
            workflow.add_step(WorkflowStep(
                "step2", "nikto", 
                dependencies=["step1"],
                conditions=[never_execute]
            ))
            
            # Mock tool execution
            mock_result = Mock()
            mock_result.status = ToolStatus.COMPLETED
            mock_result.findings = []
            
            self.mock_tool_manager.execute_tool_async = AsyncMock(return_value=mock_result)
            
            # Execute workflow
            result = await self.engine.execute_workflow(workflow, "example.com")
            
            # Check result
            self.assertEqual(result.status, WorkflowStatus.COMPLETED)
            self.assertIn("step2", result.skipped_steps)
            self.assertEqual(len(result.step_results), 1)  # Only first step executed
        
        asyncio.run(run_test())


class TestWorkflowBuilder(unittest.TestCase):
    """Test cases for WorkflowBuilder"""
    
    def test_reconnaissance_workflow(self):
        """Test creating reconnaissance workflow"""
        workflow = WorkflowBuilder.create_reconnaissance_workflow()
        
        self.assertEqual(workflow.name, "reconnaissance")
        self.assertIn("network_scan", workflow.steps)
        self.assertIn("web_scan", workflow.steps)
        self.assertIn("directory_scan", workflow.steps)
        
        # Check dependencies
        web_scan = workflow.steps["web_scan"]
        self.assertIn("network_scan", web_scan.dependencies)
        
        directory_scan = workflow.steps["directory_scan"]
        self.assertIn("web_scan", directory_scan.dependencies)
        
        # Validate workflow
        errors = workflow.validate()
        self.assertEqual(len(errors), 0)
    
    def test_vulnerability_assessment_workflow(self):
        """Test creating vulnerability assessment workflow"""
        workflow = WorkflowBuilder.create_vulnerability_assessment_workflow()
        
        self.assertEqual(workflow.name, "vuln_assessment")
        self.assertIn("port_scan", workflow.steps)
        self.assertIn("web_vuln_scan", workflow.steps)
        self.assertIn("sql_injection_test", workflow.steps)
        
        # Check retry configuration
        web_vuln_scan = workflow.steps["web_vuln_scan"]
        self.assertEqual(web_vuln_scan.retry_count, 1)
        self.assertEqual(web_vuln_scan.retry_delay, 5.0)
        
        # Validate workflow
        errors = workflow.validate()
        self.assertEqual(len(errors), 0)
    
    def test_conditional_workflow(self):
        """Test creating conditional workflow"""
        workflow = WorkflowBuilder.create_conditional_workflow()
        
        self.assertEqual(workflow.name, "conditional_scan")
        self.assertIn("discovery", workflow.steps)
        self.assertIn("ssh_scan", workflow.steps)
        self.assertIn("web_scan", workflow.steps)
        
        # Check conditions exist
        ssh_scan = workflow.steps["ssh_scan"]
        self.assertGreater(len(ssh_scan.conditions), 0)
        
        web_scan = workflow.steps["web_scan"]
        self.assertGreater(len(web_scan.conditions), 0)
        
        # Validate workflow
        errors = workflow.validate()
        self.assertEqual(len(errors), 0)


class TestWorkflowIntegration(unittest.TestCase):
    """Test workflow integration with ToolManager"""
    
    def test_tool_manager_workflow_methods(self):
        """Test ToolManager workflow methods"""
        manager = ToolManager(mock_mode=True)
        
        # Test workflow creation
        workflow = manager.create_workflow("test", "Test workflow")
        self.assertIsInstance(workflow, WorkflowDefinition)
        self.assertEqual(workflow.name, "test")
        
        # Test step creation
        step = manager.create_workflow_step("test_step", "nmap", ports="80,443")
        self.assertIsInstance(step, WorkflowStep)
        self.assertEqual(step.name, "test_step")
        self.assertEqual(step.tool_name, "nmap")
        self.assertEqual(step.config["ports"], "80,443")
    
    def test_workflow_execution_integration(self):
        """Test workflow execution through ToolManager"""
        async def run_test():
            manager = ToolManager(mock_mode=True)
            
            # Create simple workflow
            workflow = manager.create_workflow("integration_test")
            step = manager.create_workflow_step("nmap_scan", "nmap", timing=4)
            workflow.add_step(step)
            
            # Execute workflow
            result = await manager.execute_advanced_workflow(workflow, "example.com")
            
            # Check result
            self.assertEqual(result.workflow_name, "integration_test")
            self.assertEqual(result.status, WorkflowStatus.COMPLETED)
            self.assertIn("nmap_scan", result.step_results)
        
        asyncio.run(run_test())


if __name__ == '__main__':
    unittest.main()
