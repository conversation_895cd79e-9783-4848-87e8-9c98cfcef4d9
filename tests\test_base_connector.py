"""
Unit tests for the base tool connector functionality.
"""

import asyncio
import unittest
from unittest.mock import Mock, patch, AsyncMock
import time
import pytest

from tool_connectors.base import BaseToolConnector, ToolResult, ToolStatus, Severity


class MockToolConnector(BaseToolConnector):
    """Mock implementation of BaseToolConnector for testing"""
    
    @property
    def tool_name(self) -> str:
        return "mock_tool"
    
    @property
    def default_command(self) -> str:
        return "mock"
    
    def build_command(self, target: str, **kwargs):
        return ["mock", target]
    
    def parse_output(self, stdout: str, stderr: str):
        return {"output": stdout, "errors": stderr}


class TestBaseToolConnector(unittest.TestCase):
    """Test cases for BaseToolConnector"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.connector = MockToolConnector(mock_mode=True)
    
    def test_initialization(self):
        """Test connector initialization"""
        self.assertEqual(self.connector.tool_name, "mock_tool")
        self.assertEqual(self.connector.default_command, "mock")
        self.assertEqual(self.connector.timeout, 300)
    
    def test_custom_timeout(self):
        """Test connector with custom timeout"""
        connector = MockToolConnector(timeout=600)
        self.assertEqual(connector.timeout, 600)
    
    def test_build_command(self):
        """Test command building"""
        command = self.connector.build_command("example.com")
        self.assertEqual(command, ["mock", "example.com"])
    
    def test_parse_output(self):
        """Test output parsing"""
        stdout = "test output"
        stderr = "test error"
        result = self.connector.parse_output(stdout, stderr)
        self.assertEqual(result["output"], stdout)
        self.assertEqual(result["errors"], stderr)
    
    def test_validate_target_basic(self):
        """Test basic target validation"""
        self.assertTrue(self.connector.validate_target("example.com"))
        self.assertFalse(self.connector.validate_target(""))
        self.assertFalse(self.connector.validate_target(None))
    
    def test_execute_async_success(self):
        """Test successful asynchronous execution in mock mode"""
        async def run_test():
            result = await self.connector.execute_async("example.com")

            self.assertEqual(result.tool_name, "mock_tool")
            self.assertEqual(result.status, ToolStatus.COMPLETED)
            self.assertIsInstance(result.stdout, str)
            self.assertEqual(result.exit_code, 0)

        asyncio.run(run_test())
    
    def test_execute_async_failure(self):
        """Test failed asynchronous execution when tool not available"""
        async def run_test():
            # Create a connector not in mock mode to test real failure
            connector = MockToolConnector(mock_mode=False)

            result = await connector.execute_async("example.com")

            self.assertEqual(result.status, ToolStatus.FAILED)
            self.assertIn("not available", result.error_message)

        asyncio.run(run_test())
    
    def test_execute_async_timeout(self):
        """Test timeout behavior in mock mode"""
        async def run_test():
            # In mock mode, we can test with very short delay
            result = await self.connector.execute_async("example.com", mock_delay=0.1, timeout=0.05)

            # Mock mode should complete successfully even with short timeout
            # since it's just simulating execution
            self.assertEqual(result.status, ToolStatus.COMPLETED)

        asyncio.run(run_test())
    
    def test_execute_sync(self):
        """Test synchronous execution wrapper"""
        with patch.object(self.connector, 'execute_async') as mock_async:
            mock_result = ToolResult(
                tool_name="mock_tool",
                command="mock example.com",
                status=ToolStatus.COMPLETED
            )
            mock_async.return_value = mock_result
            
            # Mock asyncio.run
            with patch('asyncio.run', return_value=mock_result):
                result = self.connector.execute("example.com")
                self.assertEqual(result.status, ToolStatus.COMPLETED)


class TestToolResult(unittest.TestCase):
    """Test cases for ToolResult data class"""
    
    def test_tool_result_creation(self):
        """Test ToolResult creation with required fields"""
        result = ToolResult(
            tool_name="test_tool",
            command="test command",
            status=ToolStatus.COMPLETED
        )
        
        self.assertEqual(result.tool_name, "test_tool")
        self.assertEqual(result.command, "test command")
        self.assertEqual(result.status, ToolStatus.COMPLETED)
        self.assertEqual(result.exit_code, None)
        self.assertEqual(result.stdout, "")
        self.assertEqual(result.stderr, "")
        self.assertEqual(result.execution_time, 0.0)
        self.assertIsInstance(result.timestamp, float)
        self.assertEqual(result.findings, [])
        self.assertEqual(result.raw_output, "")
        self.assertEqual(result.parsed_data, {})
        self.assertEqual(result.error_message, None)
    
    def test_tool_result_with_all_fields(self):
        """Test ToolResult creation with all fields"""
        findings = [{"type": "test", "severity": "high"}]
        parsed_data = {"key": "value"}
        
        result = ToolResult(
            tool_name="test_tool",
            command="test command",
            status=ToolStatus.COMPLETED,
            exit_code=0,
            stdout="output",
            stderr="error",
            execution_time=1.5,
            findings=findings,
            raw_output="raw",
            parsed_data=parsed_data,
            error_message="error"
        )
        
        self.assertEqual(result.exit_code, 0)
        self.assertEqual(result.stdout, "output")
        self.assertEqual(result.stderr, "error")
        self.assertEqual(result.execution_time, 1.5)
        self.assertEqual(result.findings, findings)
        self.assertEqual(result.raw_output, "raw")
        self.assertEqual(result.parsed_data, parsed_data)
        self.assertEqual(result.error_message, "error")


class TestEnums(unittest.TestCase):
    """Test cases for enum classes"""
    
    def test_tool_status_enum(self):
        """Test ToolStatus enum values"""
        self.assertEqual(ToolStatus.PENDING.value, "pending")
        self.assertEqual(ToolStatus.RUNNING.value, "running")
        self.assertEqual(ToolStatus.COMPLETED.value, "completed")
        self.assertEqual(ToolStatus.FAILED.value, "failed")
        self.assertEqual(ToolStatus.TIMEOUT.value, "timeout")
        self.assertEqual(ToolStatus.CANCELLED.value, "cancelled")
    
    def test_severity_enum(self):
        """Test Severity enum values"""
        self.assertEqual(Severity.CRITICAL.value, "critical")
        self.assertEqual(Severity.HIGH.value, "high")
        self.assertEqual(Severity.MEDIUM.value, "medium")
        self.assertEqual(Severity.LOW.value, "low")
        self.assertEqual(Severity.INFO.value, "info")


if __name__ == '__main__':
    unittest.main()
