["tests/test_base_connector.py::TestBaseToolConnector::test_build_command", "tests/test_base_connector.py::TestBaseToolConnector::test_custom_timeout", "tests/test_base_connector.py::TestBaseToolConnector::test_execute_async_failure", "tests/test_base_connector.py::TestBaseToolConnector::test_execute_async_success", "tests/test_base_connector.py::TestBaseToolConnector::test_execute_async_timeout", "tests/test_base_connector.py::TestBaseToolConnector::test_execute_sync", "tests/test_base_connector.py::TestBaseToolConnector::test_initialization", "tests/test_base_connector.py::TestBaseToolConnector::test_parse_output", "tests/test_base_connector.py::TestBaseToolConnector::test_validate_target_basic", "tests/test_base_connector.py::TestEnums::test_severity_enum", "tests/test_base_connector.py::TestEnums::test_tool_status_enum", "tests/test_base_connector.py::TestToolResult::test_tool_result_creation", "tests/test_base_connector.py::TestToolResult::test_tool_result_with_all_fields", "tests/test_cache.py::TestCacheManager::test_cache_invalidation", "tests/test_cache.py::TestCacheManager::test_cache_key_generation", "tests/test_cache.py::TestCacheManager::test_cache_policies", "tests/test_cache.py::TestCacheManager::test_cache_stats", "tests/test_cache.py::TestCacheManager::test_disabled_cache", "tests/test_cache.py::TestCacheManager::test_result_caching", "tests/test_cache.py::TestFileCache::test_basic_operations", "tests/test_cache.py::TestFileCache::test_persistence", "tests/test_cache.py::TestGlobalCacheManager::test_get_cache_manager", "tests/test_cache.py::TestMemoryCache::test_basic_operations", "tests/test_cache.py::TestMemoryCache::test_clear", "tests/test_cache.py::TestMemoryCache::test_keys_listing", "tests/test_cache.py::TestMemoryCache::test_ttl_expiration", "tests/test_cache.py::TestSQLiteCache::test_basic_operations", "tests/test_cache.py::TestSQLiteCache::test_cleanup_expired", "tests/test_cache.py::TestSQLiteCache::test_query_functionality", "tests/test_cache.py::TestToolManagerCacheIntegration::test_cache_integration", "tests/test_cache.py::TestToolManagerCacheIntegration::test_cache_management_methods", "tests/test_cache.py::TestToolManagerCacheIntegration::test_different_cache_backends", "tests/test_config.py::TestConfigManager::test_config_merging", "tests/test_config.py::TestConfigManager::test_config_sources", "tests/test_config.py::TestConfigManager::test_default_configuration", "tests/test_config.py::TestConfigManager::test_environment_variable_override", "tests/test_config.py::TestConfigManager::test_get_set_methods", "tests/test_config.py::TestConfigManager::test_invalid_config_file", "tests/test_config.py::TestConfigManager::test_json_config_loading", "tests/test_config.py::TestConfigManager::test_save_config", "tests/test_config.py::TestConfigManager::test_tool_specific_methods", "tests/test_config.py::TestConfigManager::test_utility_methods", "tests/test_config.py::TestConfigManager::test_workflow_methods", "tests/test_config.py::TestConfigManager::test_yaml_config_loading", "tests/test_config.py::TestGlobalConfigFunctions::test_get_config", "tests/test_config.py::TestGlobalConfigFunctions::test_get_config_manager", "tests/test_config.py::TestGlobalConfigFunctions::test_get_tool_config", "tests/test_gobuster_connector.py::TestGobusterConnector::test_assess_path_severity", "tests/test_gobuster_connector.py::TestGobusterConnector::test_build_command_basic", "tests/test_gobuster_connector.py::TestGobusterConnector::test_build_command_with_auth", "tests/test_gobuster_connector.py::TestGobusterConnector::test_build_command_with_cookies", "tests/test_gobuster_connector.py::TestGobusterConnector::test_build_command_with_exclude_status", "tests/test_gobuster_connector.py::TestGobusterConnector::test_build_command_with_extensions", "tests/test_gobuster_connector.py::TestGobusterConnector::test_build_command_with_mode", "tests/test_gobuster_connector.py::TestGobusterConnector::test_build_command_with_status_codes", "tests/test_gobuster_connector.py::TestGobusterConnector::test_build_command_with_threads", "tests/test_gobuster_connector.py::TestGobusterConnector::test_build_command_with_timeout", "tests/test_gobuster_connector.py::TestGobusterConnector::test_build_command_with_user_agent", "tests/test_gobuster_connector.py::TestGobusterConnector::test_build_command_with_wordlist", "tests/test_gobuster_connector.py::TestGobusterConnector::test_extract_findings", "tests/test_gobuster_connector.py::TestGobusterConnector::test_get_path_recommendation", "tests/test_gobuster_connector.py::TestGobusterConnector::test_initialization", "tests/test_gobuster_connector.py::TestGobusterConnector::test_mock_execution", "tests/test_gobuster_connector.py::TestGobusterConnector::test_parse_output_basic", "tests/test_gobuster_connector.py::TestGobusterConnector::test_validate_target", "tests/test_mock_functionality.py::TestMockDataGeneration::test_mock_service_mapping", "tests/test_mock_functionality.py::TestMockDataGeneration::test_nmap_mock_data_structure", "tests/test_mock_functionality.py::TestMockFunctionality::test_async_mock_execution", "tests/test_mock_functionality.py::TestMockFunctionality::test_enable_disable_mock_mode", "tests/test_mock_functionality.py::TestMockFunctionality::test_mixed_mode_operation", "tests/test_mock_functionality.py::TestMockFunctionality::test_mock_delay_parameter", "tests/test_mock_functionality.py::TestMockFunctionality::test_mock_findings_aggregation", "tests/test_mock_functionality.py::TestMockFunctionality::test_mock_workflow_execution", "tests/test_mock_functionality.py::TestMockFunctionality::test_multiple_tools_mock_execution", "tests/test_mock_functionality.py::TestMockFunctionality::test_nmap_mock_execution", "tests/test_mock_functionality.py::TestMockFunctionality::test_nmap_mock_mode_initialization", "tests/test_mock_functionality.py::TestMockFunctionality::test_nmap_mock_output_parsing", "tests/test_mock_functionality.py::TestMockFunctionality::test_tool_info_in_mock_mode", "tests/test_mock_functionality.py::TestMockFunctionality::test_tool_manager_mock_execution", "tests/test_mock_functionality.py::TestMockFunctionality::test_tool_manager_mock_mode", "tests/test_monitoring.py::TestGlobalMonitoringManager::test_get_monitoring_manager", "tests/test_monitoring.py::TestHealthMonitor::test_add_remove_checks", "tests/test_monitoring.py::TestHealthMonitor::test_check_with_exception", "tests/test_monitoring.py::TestHealthMonitor::test_failing_check", "tests/test_monitoring.py::TestHealthMonitor::test_nonexistent_check", "tests/test_monitoring.py::TestHealthMonitor::test_run_all_checks", "tests/test_monitoring.py::TestHealthMonitor::test_run_single_check", "tests/test_monitoring.py::TestMetricsCollector::test_counter_operations", "tests/test_monitoring.py::TestMetricsCollector::test_gauge_operations", "tests/test_monitoring.py::TestMetricsCollector::test_histogram_operations", "tests/test_monitoring.py::TestMetricsCollector::test_metrics_filtering", "tests/test_monitoring.py::TestMetricsCollector::test_metrics_summary", "tests/test_monitoring.py::TestMetricsCollector::test_record_metric", "tests/test_monitoring.py::TestMonitoringManager::test_manager_initialization", "tests/test_monitoring.py::TestMonitoringManager::test_monitoring_summary", "tests/test_monitoring.py::TestMonitoringManager::test_tool_execution_monitoring", "tests/test_monitoring.py::TestPerformanceMonitor::test_execution_monitoring", "tests/test_monitoring.py::TestPerformanceMonitor::test_performance_summary", "tests/test_monitoring.py::TestStructuredLogger::test_logger_creation", "tests/test_monitoring.py::TestStructuredLogger::test_logging_methods", "tests/test_monitoring.py::TestToolManagerMonitoringIntegration::test_custom_health_checks", "tests/test_monitoring.py::TestToolManagerMonitoringIntegration::test_monitoring_disabled", "tests/test_monitoring.py::TestToolManagerMonitoringIntegration::test_monitoring_integration", "tests/test_monitoring.py::TestToolManagerMonitoringIntegration::test_monitoring_methods", "tests/test_nikto_connector.py::TestNiktoConnector::test_assess_vulnerability_severity", "tests/test_nikto_connector.py::TestNiktoConnector::test_build_command_basic", "tests/test_nikto_connector.py::TestNiktoConnector::test_build_command_output_formats", "tests/test_nikto_connector.py::TestNiktoConnector::test_build_command_with_plugins", "tests/test_nikto_connector.py::TestNiktoConnector::test_build_command_with_port", "tests/test_nikto_connector.py::TestNiktoConnector::test_build_command_with_ssl", "tests/test_nikto_connector.py::TestNiktoConnector::test_build_command_with_timeout", "tests/test_nikto_connector.py::TestNiktoConnector::test_build_command_with_tuning", "tests/test_nikto_connector.py::TestNiktoConnector::test_build_command_with_user_agent", "tests/test_nikto_connector.py::TestNiktoConnector::test_extract_findings", "tests/test_nikto_connector.py::TestNiktoConnector::test_extract_scan_info", "tests/test_nikto_connector.py::TestNiktoConnector::test_extract_target_info", "tests/test_nikto_connector.py::TestNiktoConnector::test_extract_vulnerabilities", "tests/test_nikto_connector.py::TestNiktoConnector::test_get_vulnerability_recommendation", "tests/test_nikto_connector.py::TestNiktoConnector::test_initialization", "tests/test_nikto_connector.py::TestNiktoConnector::test_mock_execution", "tests/test_nikto_connector.py::TestNiktoConnector::test_parse_output_basic", "tests/test_nikto_connector.py::TestNiktoConnector::test_parse_text_output", "tests/test_nikto_connector.py::TestNiktoConnector::test_validate_target", "tests/test_nmap_connector.py::TestNmapConnector::test_assess_port_severity", "tests/test_nmap_connector.py::TestNmapConnector::test_build_command_advanced_options", "tests/test_nmap_connector.py::TestNmapConnector::test_build_command_basic", "tests/test_nmap_connector.py::TestNmapConnector::test_build_command_scan_types", "tests/test_nmap_connector.py::TestNmapConnector::test_build_command_scripts", "tests/test_nmap_connector.py::TestNmapConnector::test_build_command_timing", "tests/test_nmap_connector.py::TestNmapConnector::test_build_command_with_ports", "tests/test_nmap_connector.py::TestNmapConnector::test_extract_findings", "tests/test_nmap_connector.py::TestNmapConnector::test_get_mock_service_name", "tests/test_nmap_connector.py::TestNmapConnector::test_get_port_recommendation", "tests/test_nmap_connector.py::TestNmapConnector::test_initialization", "tests/test_nmap_connector.py::TestNmapConnector::test_is_vulnerability_script", "tests/test_nmap_connector.py::TestNmapConnector::test_mock_output_generation", "tests/test_nmap_connector.py::TestNmapConnector::test_parse_text_output", "tests/test_nmap_connector.py::TestNmapConnector::test_parse_xml_output", "tests/test_nmap_connector.py::TestNmapConnector::test_validate_target", "tests/test_performance.py::TestPerformance::test_augmentation_performance", "tests/test_performance.py::TestPerformance::test_batch_generation_performance", "tests/test_performance.py::TestPerformance::test_concurrent_generation_performance", "tests/test_performance.py::TestPerformance::test_large_dataset_generation", "tests/test_performance.py::TestPerformance::test_memory_usage", "tests/test_performance.py::TestPerformance::test_quality_validation_performance", "tests/test_performance.py::TestPerformance::test_single_generation_performance", "tests/test_performance.py::TestPerformance::test_template_generation_performance", "tests/test_performance.py::TestScalability::test_scaling_generation_count", "tests/test_performance.py::TestScalability::test_scaling_target_count", "tests/test_performance.py::TestScalability::test_workflow_scalability", "tests/test_plugins.py::TestGlobalPluginFunctions::test_get_plugin_manager", "tests/test_plugins.py::TestPluginManager::test_create_connector", "tests/test_plugins.py::TestPluginManager::test_create_connector_error_handling", "tests/test_plugins.py::TestPluginManager::test_discover_plugins_from_file", "tests/test_plugins.py::TestPluginManager::test_get_all_plugins", "tests/test_plugins.py::TestPluginManager::test_get_plugin_metadata", "tests/test_plugins.py::TestPluginManager::test_list_plugins", "tests/test_plugins.py::TestPluginManager::test_plugin_directory_management", "tests/test_plugins.py::TestPluginManager::test_plugin_info_creation", "tests/test_plugins.py::TestPluginManager::test_register_plugin_class", "tests/test_plugins.py::TestPluginManager::test_unregister_plugin", "tests/test_plugins.py::TestToolManagerPluginIntegration::test_list_all_plugin_info", "tests/test_plugins.py::TestToolManagerPluginIntegration::test_plugin_execution_in_tool_manager", "tests/test_plugins.py::TestToolManagerPluginIntegration::test_plugin_info_retrieval", "tests/test_plugins.py::TestToolManagerPluginIntegration::test_plugin_registration_in_tool_manager", "tests/test_plugins.py::TestToolManagerPluginIntegration::test_plugin_unregistration", "tests/test_reporting.py::TestGlobalReportGenerator::test_get_report_generator", "tests/test_reporting.py::TestReportFormatters::test_csv_formatter", "tests/test_reporting.py::TestReportFormatters::test_file_extensions", "tests/test_reporting.py::TestReportFormatters::test_html_formatter", "tests/test_reporting.py::TestReportFormatters::test_json_formatter", "tests/test_reporting.py::TestReportFormatters::test_xml_formatter", "tests/test_reporting.py::TestReportGenerator::test_analysis_sections", "tests/test_reporting.py::TestReportGenerator::test_available_formats", "tests/test_reporting.py::TestReportGenerator::test_available_templates", "tests/test_reporting.py::TestReportGenerator::test_generate_report_basic", "tests/test_reporting.py::TestReportGenerator::test_generate_report_with_custom_sections", "tests/test_reporting.py::TestReportGenerator::test_generate_report_with_template", "tests/test_reporting.py::TestReportGenerator::test_generator_initialization", "tests/test_reporting.py::TestReportGenerator::test_save_report", "tests/test_reporting.py::TestReportGenerator::test_unsupported_format", "tests/test_reporting.py::TestReportMetadata::test_metadata_creation", "tests/test_reporting.py::TestReportSection::test_nested_sections", "tests/test_reporting.py::TestReportSection::test_section_creation", "tests/test_reporting.py::TestReportTemplate::test_metadata_template", "tests/test_reporting.py::TestReportTemplate::test_template_creation", "tests/test_reporting.py::TestReportTemplate::test_template_sections", "tests/test_reporting.py::TestToolManagerReportingIntegration::test_custom_report_sections", "tests/test_reporting.py::TestToolManagerReportingIntegration::test_report_formats_and_templates", "tests/test_reporting.py::TestToolManagerReportingIntegration::test_reporting_integration", "tests/test_reporting.py::TestToolManagerReportingIntegration::test_save_report", "tests/test_security.py::TestGlobalSecurityManager::test_custom_policy", "tests/test_security.py::TestGlobalSecurityManager::test_get_security_manager", "tests/test_security.py::TestInputValidator::test_blocked_patterns", "tests/test_security.py::TestInputValidator::test_command_syntax_validation", "tests/test_security.py::TestInputValidator::test_command_validation", "tests/test_security.py::TestInputValidator::test_input_length_validation", "tests/test_security.py::TestInputValidator::test_valid_input", "tests/test_security.py::TestResourceMonitor::test_monitor_initialization", "tests/test_security.py::TestResourceMonitor::test_monitoring_start_stop", "tests/test_security.py::TestResourceMonitor::test_process_registration", "tests/test_security.py::TestResourceMonitor::test_process_unregistration", "tests/test_security.py::TestSandboxEnvironment::test_sandbox_cleanup", "tests/test_security.py::TestSandboxEnvironment::test_sandbox_initialization", "tests/test_security.py::TestSandboxEnvironment::test_sandbox_path_generation", "tests/test_security.py::TestSandboxEnvironment::test_secure_environment_creation", "tests/test_security.py::TestSecureExecutor::test_executor_initialization", "tests/test_security.py::TestSecureExecutor::test_secure_command_execution", "tests/test_security.py::TestSecureExecutor::test_security_violation_handling", "tests/test_security.py::TestSecurityPolicy::test_custom_policy", "tests/test_security.py::TestSecurityPolicy::test_default_policy", "tests/test_security.py::TestSecurityViolation::test_violation_creation", "tests/test_security.py::TestToolManagerSecurityIntegration::test_command_authorization", "tests/test_security.py::TestToolManagerSecurityIntegration::test_input_validation", "tests/test_security.py::TestToolManagerSecurityIntegration::test_resource_usage_monitoring", "tests/test_security.py::TestToolManagerSecurityIntegration::test_security_cleanup", "tests/test_security.py::TestToolManagerSecurityIntegration::test_security_disabled_manager", "tests/test_security.py::TestToolManagerSecurityIntegration::test_security_enabled_manager", "tests/test_security.py::TestToolManagerSecurityIntegration::test_security_policy_updates", "tests/test_security.py::TestToolManagerSecurityIntegration::test_security_status", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_assess_injection_severity", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_build_command_basic", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_build_command_with_cookie", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_build_command_with_crawl", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_build_command_with_data", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_build_command_with_dbms", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_build_command_with_forms", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_build_command_with_level_and_risk", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_build_command_with_proxy", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_build_command_with_tamper", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_build_command_with_technique", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_build_command_with_threads", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_build_command_with_user_agent", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_extract_databases", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_extract_dbms", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_extract_findings", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_extract_injections", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_get_injection_recommendation", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_initialization", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_mock_execution", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_parse_output_basic", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_parse_text_output", "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_validate_target", "tests/test_synthetic_data_generator.py::TestBaseGenerator::test_add_quality_validator", "tests/test_synthetic_data_generator.py::TestBaseGenerator::test_generate_batch", "tests/test_synthetic_data_generator.py::TestBaseGenerator::test_generate_batch_with_validation", "tests/test_synthetic_data_generator.py::TestBaseGenerator::test_generate_single", "tests/test_synthetic_data_generator.py::TestBaseGenerator::test_initialization", "tests/test_synthetic_data_generator.py::TestBaseGenerator::test_initialization_with_config", "tests/test_synthetic_data_generator.py::TestConfigManager::test_get_generator_config", "tests/test_synthetic_data_generator.py::TestConfigManager::test_initialization", "tests/test_synthetic_data_generator.py::TestConfigManager::test_save_and_load_config", "tests/test_synthetic_data_generator.py::TestConfigManager::test_update_generator_config", "tests/test_synthetic_data_generator.py::TestConfigManager::test_update_system_config", "tests/test_synthetic_data_generator.py::TestConfigManager::test_update_system_config_invalid_param", "tests/test_synthetic_data_generator.py::TestGenerationConfig::test_default_creation", "tests/test_synthetic_data_generator.py::TestGenerationConfig::test_full_creation", "tests/test_synthetic_data_generator.py::TestGenerationResult::test_add_data", "tests/test_synthetic_data_generator.py::TestGenerationResult::test_add_error", "tests/test_synthetic_data_generator.py::TestGenerationResult::test_add_warning", "tests/test_synthetic_data_generator.py::TestGenerationResult::test_creation", "tests/test_synthetic_data_generator.py::TestGeneratorConfig::test_creation", "tests/test_synthetic_data_generator.py::TestGeneratorConfig::test_from_dict", "tests/test_synthetic_data_generator.py::TestGeneratorConfig::test_to_dict", "tests/test_synthetic_data_generator.py::TestIntegrationLayer::test_input_module_integration", "tests/test_synthetic_data_generator.py::TestIntegrationLayer::test_tool_connector_integration", "tests/test_synthetic_data_generator.py::TestQualityValidation::test_network_topology_validator", "tests/test_synthetic_data_generator.py::TestQualityValidation::test_vulnerability_validator", "tests/test_synthetic_data_generator.py::TestSyntheticData::test_creation", "tests/test_synthetic_data_generator.py::TestSyntheticData::test_to_json", "tests/test_synthetic_data_generator.py::TestSyntheticData::test_to_normalized_format", "tests/test_synthetic_data_generator.py::TestSyntheticData::test_validate_format", "tests/test_synthetic_data_generator.py::TestSyntheticDataManager::test_batch_generation", "tests/test_synthetic_data_generator.py::TestSyntheticDataManager::test_data_augmentation", "tests/test_synthetic_data_generator.py::TestSyntheticDataManager::test_generate_network_topology", "tests/test_synthetic_data_generator.py::TestSyntheticDataManager::test_generate_vulnerability_scenario", "tests/test_synthetic_data_generator.py::TestSyntheticDataManager::test_initialization", "tests/test_synthetic_data_generator.py::TestSyntheticDataManager::test_template_generation", "tests/test_synthetic_data_generator.py::TestSyntheticDataType::test_enum_values", "tests/test_synthetic_data_generator.py::TestSystemConfig::test_default_creation", "tests/test_synthetic_data_generator.py::TestSystemConfig::test_from_dict", "tests/test_synthetic_data_generator.py::TestSystemConfig::test_to_dict", "tests/test_tool_manager.py::TestToolManager::test_aggregate_findings", "tests/test_tool_manager.py::TestToolManager::test_available_tools_detailed", "tests/test_tool_manager.py::TestToolManager::test_enable_disable_mock_mode", "tests/test_tool_manager.py::TestToolManager::test_execute_multiple_tools", "tests/test_tool_manager.py::TestToolManager::test_execute_multiple_tools_async", "tests/test_tool_manager.py::TestToolManager::test_execute_scan_workflow", "tests/test_tool_manager.py::TestToolManager::test_execute_scan_workflow_invalid", "tests/test_tool_manager.py::TestToolManager::test_execute_tool", "tests/test_tool_manager.py::TestToolManager::test_execute_tool_async", "tests/test_tool_manager.py::TestToolManager::test_execute_tool_invalid", "tests/test_tool_manager.py::TestToolManager::test_generate_report", "tests/test_tool_manager.py::TestToolManager::test_generate_report_invalid_format", "tests/test_tool_manager.py::TestToolManager::test_get_available_tools", "tests/test_tool_manager.py::TestToolManager::test_get_connector", "tests/test_tool_manager.py::TestToolManager::test_initialization", "tests/test_tool_manager.py::TestToolManager::test_register_connector", "tests/test_tool_manager.py::TestToolManager::test_tool_status", "tests/test_tool_manager.py::TestToolManager::test_workflow_configs", "tests/test_utils.py::TestFormattingFunctions::test_format_bytes", "tests/test_utils.py::TestFormattingFunctions::test_format_duration", "tests/test_utils.py::TestFormattingFunctions::test_merge_findings", "tests/test_utils.py::TestFormattingFunctions::test_sanitize_filename", "tests/test_utils.py::TestParsingFunctions::test_extract_ips_from_text", "tests/test_utils.py::TestParsingFunctions::test_extract_urls_from_text", "tests/test_utils.py::TestParsingFunctions::test_normalize_target", "tests/test_utils.py::TestParsingFunctions::test_parse_port_range", "tests/test_utils.py::TestValidationFunctions::test_validate_hostname", "tests/test_utils.py::TestValidationFunctions::test_validate_ip_address", "tests/test_utils.py::TestValidationFunctions::test_validate_ip_network", "tests/test_utils.py::TestValidationFunctions::test_validate_port", "tests/test_utils.py::TestValidationFunctions::test_validate_url", "tests/test_workflows.py::TestWorkflowBuilder::test_conditional_workflow", "tests/test_workflows.py::TestWorkflowBuilder::test_reconnaissance_workflow", "tests/test_workflows.py::TestWorkflowBuilder::test_vulnerability_assessment_workflow", "tests/test_workflows.py::TestWorkflowDefinition::test_add_step", "tests/test_workflows.py::TestWorkflowDefinition::test_circular_dependency_detection", "tests/test_workflows.py::TestWorkflowDefinition::test_duplicate_step_name", "tests/test_workflows.py::TestWorkflowDefinition::test_execution_order", "tests/test_workflows.py::TestWorkflowDefinition::test_remove_step", "tests/test_workflows.py::TestWorkflowDefinition::test_workflow_creation", "tests/test_workflows.py::TestWorkflowDefinition::test_workflow_validation", "tests/test_workflows.py::TestWorkflowEngine::test_engine_creation", "tests/test_workflows.py::TestWorkflowEngine::test_execute_workflow_simple", "tests/test_workflows.py::TestWorkflowEngine::test_execute_workflow_with_conditions", "tests/test_workflows.py::TestWorkflowEngine::test_execute_workflow_with_failure", "tests/test_workflows.py::TestWorkflowEngine::test_should_execute_step", "tests/test_workflows.py::TestWorkflowIntegration::test_tool_manager_workflow_methods", "tests/test_workflows.py::TestWorkflowIntegration::test_workflow_execution_integration", "tests/test_workflows.py::TestWorkflowStep::test_step_creation", "tests/test_workflows.py::TestWorkflowStep::test_step_validation"]