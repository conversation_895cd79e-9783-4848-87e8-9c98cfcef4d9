#!/usr/bin/env python3
"""
Advanced Workflow System Demo

This script demonstrates the advanced workflow capabilities of the tool connector framework.
It shows how to:
1. Create custom workflows with dependencies
2. Use conditional execution
3. Handle parallel execution
4. Implement retry logic and error handling
5. Use predefined workflow builders
"""

import asyncio
import sys
from pathlib import Path

# Add the parent directory to the path so we can import tool_connectors
sys.path.insert(0, str(Path(__file__).parent.parent))

from tool_connectors import ToolManager
from tool_connectors.workflows import (
    WorkflowDefinition, WorkflowStep, WorkflowBuilder, 
    ExecutionMode, WorkflowStatus
)


async def demo_basic_workflow():
    """Demonstrate basic workflow creation and execution"""
    print("=== Basic Workflow Demo ===")
    
    manager = ToolManager(mock_mode=True)
    
    # Create a simple workflow
    workflow = manager.create_workflow("basic_scan", "Basic scanning workflow")
    
    # Add steps
    step1 = manager.create_workflow_step(
        "port_scan", "nmap",
        scan_type="tcp_syn",
        timing=4,
        ports="1-1000"
    )
    
    step2 = manager.create_workflow_step(
        "web_scan", "nikto",
        dependencies=["port_scan"],
        timeout=30
    )
    
    workflow.add_step(step1)
    workflow.add_step(step2)
    
    print(f"Created workflow '{workflow.name}' with {len(workflow.steps)} steps")
    print(f"Execution order: {workflow.get_execution_order()}")
    
    # Execute workflow
    print("Executing workflow...")
    result = await manager.execute_advanced_workflow(workflow, "example.com")
    
    print(f"Workflow completed with status: {result.status.value}")
    print(f"Execution time: {result.execution_time:.2f}s")
    print(f"Steps executed: {len(result.step_results)}")
    print(f"Total findings: {result.total_findings}")
    print()


async def demo_conditional_workflow():
    """Demonstrate conditional workflow execution"""
    print("=== Conditional Workflow Demo ===")
    
    manager = ToolManager(mock_mode=True)
    
    # Create workflow with conditional steps
    workflow = manager.create_workflow("conditional_scan", "Conditional scanning workflow")
    
    # Initial discovery step
    discovery_step = manager.create_workflow_step(
        "discovery", "nmap",
        scan_type="tcp_syn",
        timing=5,
        ports="22,80,443,21,25"
    )
    workflow.add_step(discovery_step)
    
    # Conditional web scan (only if HTTP/HTTPS ports are found)
    def has_web_ports(results):
        if "discovery" not in results:
            return False
        
        # Check if any web ports were found
        discovery_result = results["discovery"]
        for finding in discovery_result.findings:
            port = finding.get('port', '')
            if port in ['80', '443', '8080', '8443']:
                return True
        return False
    
    web_step = manager.create_workflow_step(
        "web_scan", "nikto",
        dependencies=["discovery"],
        conditions=[has_web_ports],
        timeout=45
    )
    workflow.add_step(web_step)
    
    # Conditional SSH scan (only if SSH port is found)
    def has_ssh_port(results):
        if "discovery" not in results:
            return False
        
        discovery_result = results["discovery"]
        for finding in discovery_result.findings:
            port = finding.get('port', '')
            service = finding.get('service', '').lower()
            if port == '22' or 'ssh' in service:
                return True
        return False
    
    ssh_step = manager.create_workflow_step(
        "ssh_scan", "nmap",
        dependencies=["discovery"],
        conditions=[has_ssh_port],
        scripts=["ssh-auth-methods", "ssh-hostkey"],
        ports="22"
    )
    workflow.add_step(ssh_step)
    
    print(f"Created conditional workflow with {len(workflow.steps)} steps")
    
    # Execute workflow
    print("Executing conditional workflow...")
    result = await manager.execute_advanced_workflow(workflow, "example.com")
    
    print(f"Workflow completed with status: {result.status.value}")
    print(f"Steps executed: {len(result.step_results)}")
    print(f"Steps skipped: {len(result.skipped_steps)}")
    if result.skipped_steps:
        print(f"Skipped steps: {result.skipped_steps}")
    print()


async def demo_retry_and_error_handling():
    """Demonstrate retry logic and error handling"""
    print("=== Retry and Error Handling Demo ===")
    
    manager = ToolManager(mock_mode=True)
    
    # Create workflow with retry configuration
    workflow = manager.create_workflow("retry_demo", "Workflow with retry logic")
    
    # Step with retry configuration
    retry_step = manager.create_workflow_step(
        "flaky_scan", "nmap",
        timing=4,
        ports="80,443",
        retry_count=2,
        retry_delay=1.0
    )
    workflow.add_step(retry_step)
    
    # Step that continues on failure
    continue_step = manager.create_workflow_step(
        "optional_scan", "nikto",
        dependencies=["flaky_scan"],
        continue_on_failure=True,
        timeout=30
    )
    workflow.add_step(continue_step)
    
    # Final step that depends on the optional one
    final_step = manager.create_workflow_step(
        "final_scan", "gobuster",
        dependencies=["optional_scan"],
        mode="dir",
        threads=10
    )
    workflow.add_step(final_step)
    
    print(f"Created workflow with retry and error handling")
    print(f"Retry configuration: {retry_step.retry_count} retries with {retry_step.retry_delay}s delay")
    
    # Execute workflow
    print("Executing workflow with retry logic...")
    result = await manager.execute_advanced_workflow(workflow, "example.com")
    
    print(f"Workflow completed with status: {result.status.value}")
    print(f"Failed steps: {result.failed_steps}")
    print(f"Success rate: {result.success_rate:.1%}")
    print()


async def demo_predefined_workflows():
    """Demonstrate predefined workflow builders"""
    print("=== Predefined Workflows Demo ===")
    
    manager = ToolManager(mock_mode=True)
    
    # Test reconnaissance workflow
    print("Testing reconnaissance workflow...")
    recon_workflow = WorkflowBuilder.create_reconnaissance_workflow()
    
    print(f"Reconnaissance workflow has {len(recon_workflow.steps)} steps:")
    for step_name in recon_workflow.get_execution_order():
        step = recon_workflow.steps[step_name]
        deps = f" (depends on: {step.dependencies})" if step.dependencies else ""
        conditions = f" (conditional)" if step.conditions else ""
        print(f"  - {step_name}: {step.tool_name}{deps}{conditions}")
    
    result = await manager.execute_advanced_workflow(recon_workflow, "example.com")
    print(f"Reconnaissance completed: {result.status.value} ({result.execution_time:.2f}s)")
    print()
    
    # Test vulnerability assessment workflow
    print("Testing vulnerability assessment workflow...")
    vuln_workflow = WorkflowBuilder.create_vulnerability_assessment_workflow()
    
    print(f"Vulnerability assessment workflow has {len(vuln_workflow.steps)} steps:")
    for step_name in vuln_workflow.get_execution_order():
        step = vuln_workflow.steps[step_name]
        retry_info = f" (retry: {step.retry_count})" if step.retry_count > 0 else ""
        timeout_info = f" (timeout: {step.timeout}s)" if step.timeout else ""
        print(f"  - {step_name}: {step.tool_name}{retry_info}{timeout_info}")
    
    result = await manager.execute_advanced_workflow(vuln_workflow, "example.com")
    print(f"Vulnerability assessment completed: {result.status.value} ({result.execution_time:.2f}s)")
    print()
    
    # Test conditional workflow
    print("Testing conditional workflow...")
    conditional_workflow = WorkflowBuilder.create_conditional_workflow()
    
    print(f"Conditional workflow has {len(conditional_workflow.steps)} steps:")
    for step_name in conditional_workflow.get_execution_order():
        step = conditional_workflow.steps[step_name]
        conditions = f" (conditional)" if step.conditions else ""
        print(f"  - {step_name}: {step.tool_name}{conditions}")
    
    result = await manager.execute_advanced_workflow(conditional_workflow, "example.com")
    print(f"Conditional workflow completed: {result.status.value}")
    print(f"Steps executed: {len(result.step_results)}, skipped: {len(result.skipped_steps)}")
    print()


async def demo_workflow_validation():
    """Demonstrate workflow validation"""
    print("=== Workflow Validation Demo ===")
    
    manager = ToolManager(mock_mode=True)
    
    # Create workflow with validation issues
    workflow = manager.create_workflow("validation_test", "Workflow validation test")
    
    # Add steps with circular dependency
    step1 = manager.create_workflow_step("step1", "nmap", dependencies=["step2"])
    step2 = manager.create_workflow_step("step2", "nikto", dependencies=["step1"])
    
    workflow.add_step(step1)
    workflow.add_step(step2)
    
    print("Created workflow with circular dependency")
    
    # Validate workflow
    errors = workflow.validate()
    if errors:
        print("Validation errors found:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("Workflow is valid")
    
    # Fix the workflow by creating a new one
    print("\nFixing circular dependency...")
    print("Creating new workflow without circular dependencies...")

    # Create a new workflow with proper dependencies
    fixed_workflow = manager.create_workflow("validation_test_fixed", "Fixed workflow")
    fixed_workflow.add_step(manager.create_workflow_step("step1", "nmap"))
    fixed_workflow.add_step(manager.create_workflow_step("step2", "nikto", dependencies=["step1"]))
    
    errors = fixed_workflow.validate()
    if errors:
        print("Still has validation errors:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("Workflow is now valid!")
    
    print()


async def demo_workflow_monitoring():
    """Demonstrate workflow monitoring and cancellation"""
    print("=== Workflow Monitoring Demo ===")
    
    manager = ToolManager(mock_mode=True)
    
    # Create a long-running workflow
    workflow = manager.create_workflow("monitoring_test", "Long-running workflow")
    
    for i in range(3):
        step = manager.create_workflow_step(
            f"step_{i+1}", "nmap",
            timing=1,  # Slow timing
            ports=f"{1000+i*100}-{1099+i*100}"
        )
        if i > 0:
            step.dependencies = [f"step_{i}"]
        workflow.add_step(step)
    
    print(f"Created workflow with {len(workflow.steps)} steps")
    
    # Start workflow execution (don't await immediately)
    print("Starting workflow execution...")
    workflow_task = asyncio.create_task(
        manager.execute_advanced_workflow(workflow, "example.com")
    )
    
    # Monitor running workflows
    await asyncio.sleep(0.1)  # Let it start
    running = manager.get_running_workflows()
    
    if running:
        print(f"Running workflows: {list(running.keys())}")
        for name, result in running.items():
            print(f"  {name}: {result.status.value} (running for {result.execution_time:.1f}s)")
    
    # Let it complete
    result = await workflow_task
    
    print(f"Workflow completed: {result.status.value}")
    print(f"Final execution time: {result.execution_time:.2f}s")
    print()


async def main():
    """Main function to run all workflow demos"""
    print("Tool Connectors Framework - Advanced Workflow System Demo")
    print("=" * 70)
    print("This demo shows advanced workflow capabilities including:")
    print("- Dependency management")
    print("- Conditional execution")
    print("- Retry logic and error handling")
    print("- Predefined workflow builders")
    print("- Workflow validation")
    print("- Workflow monitoring")
    print()
    
    try:
        await demo_basic_workflow()
        await demo_conditional_workflow()
        await demo_retry_and_error_handling()
        await demo_predefined_workflows()
        await demo_workflow_validation()
        await demo_workflow_monitoring()
        
        print("=== Workflow Demo Summary ===")
        print("All workflow demos completed successfully!")
        print("The advanced workflow system supports:")
        print("- Complex dependency management with topological sorting")
        print("- Conditional step execution based on previous results")
        print("- Retry logic with configurable delays")
        print("- Error handling with continue-on-failure options")
        print("- Predefined workflow builders for common patterns")
        print("- Comprehensive workflow validation")
        print("- Real-time workflow monitoring and cancellation")
        
    except Exception as e:
        print(f"Error during demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
