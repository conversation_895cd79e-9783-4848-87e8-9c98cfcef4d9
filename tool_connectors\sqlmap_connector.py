"""
SQLmap Tool Connector

This module provides integration with SQLmap for automated SQL injection
testing and database takeover.
"""

import json
import re
from typing import Any, Dict, List, Optional
from urllib.parse import urlparse

from .base import BaseToolConnector, Severity


class SQLmapConnector(BaseToolConnector):
    """Connector for SQLmap SQL injection testing tool"""
    
    @property
    def tool_name(self) -> str:
        return "sqlmap"
    
    @property
    def default_command(self) -> str:
        return "sqlmap"
    
    def build_command(self, target: str, **kwargs) -> List[str]:
        """
        Build SQLmap command with specified options.
        
        Args:
            target: Target URL to test
            **kwargs: SQLmap options
                - data: POST data
                - cookie: Cookie string
                - user_agent: User agent string
                - proxy: Proxy URL
                - threads: Number of threads
                - level: Test level (1-5)
                - risk: Risk level (1-3)
                - technique: SQL injection techniques to use
                - dbms: Force DBMS type
                - batch: Non-interactive mode
                - forms: Parse and test forms
        """
        cmd = [self.tool_path or self.default_command]
        
        # Target URL
        cmd.extend(['-u', target])
        
        # POST data
        data = kwargs.get('data')
        if data:
            cmd.extend(['--data', data])
        
        # Cookie
        cookie = kwargs.get('cookie')
        if cookie:
            cmd.extend(['--cookie', cookie])
        
        # User agent
        user_agent = kwargs.get('user_agent')
        if user_agent:
            cmd.extend(['--user-agent', user_agent])
        
        # Proxy
        proxy = kwargs.get('proxy')
        if proxy:
            cmd.extend(['--proxy', proxy])
        
        # Threads
        threads = kwargs.get('threads', 1)
        cmd.extend(['--threads', str(threads)])
        
        # Test level
        level = kwargs.get('level', 1)
        cmd.extend(['--level', str(level)])
        
        # Risk level
        risk = kwargs.get('risk', 1)
        cmd.extend(['--risk', str(risk)])
        
        # Techniques
        technique = kwargs.get('technique')
        if technique:
            cmd.extend(['--technique', technique])
        
        # DBMS
        dbms = kwargs.get('dbms')
        if dbms:
            cmd.extend(['--dbms', dbms])
        
        # Batch mode (non-interactive)
        if kwargs.get('batch', True):
            cmd.append('--batch')
        
        # Parse forms
        if kwargs.get('forms', False):
            cmd.append('--forms')
        
        # Output options
        cmd.extend(['--output-dir', '/tmp/sqlmap'])
        cmd.append('--flush-session')
        
        return cmd
    
    def parse_output(self, stdout: str, stderr: str) -> Dict[str, Any]:
        """
        Parse SQLmap output into standardized format.
        
        Args:
            stdout: SQLmap stdout output
            stderr: SQLmap stderr output
            
        Returns:
            Parsed data dictionary
        """
        parsed_data = {
            'target': '',
            'vulnerabilities': [],
            'databases': [],
            'tables': [],
            'columns': [],
            'data': [],
            'injection_points': [],
            'techniques_used': [],
            'warnings': [],
            'errors': []
        }
        
        lines = stdout.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # Extract target URL
            if 'testing URL' in line:
                url_match = re.search(r"testing URL '([^']+)'", line)
                if url_match:
                    parsed_data['target'] = url_match.group(1)
            
            # Detect vulnerabilities
            if 'is vulnerable' in line.lower():
                vuln_match = re.search(r'Parameter: ([^\s]+)', line)
                if vuln_match:
                    param = vuln_match.group(1)
                    parsed_data['vulnerabilities'].append({
                        'parameter': param,
                        'type': 'SQL Injection',
                        'description': line
                    })
            
            # Extract injection points
            if 'injection point' in line.lower():
                parsed_data['injection_points'].append(line)
            
            # Extract techniques
            if 'technique' in line.lower() and 'used' in line.lower():
                technique_match = re.search(r'([A-Z]+)', line)
                if technique_match:
                    parsed_data['techniques_used'].append(technique_match.group(1))
            
            # Extract database names
            if 'available databases' in line.lower():
                # Next lines will contain database names
                continue
            
            # Extract warnings
            if '[WARNING]' in line:
                parsed_data['warnings'].append(line.replace('[WARNING]', '').strip())
            
            # Extract errors
            if '[ERROR]' in line or '[CRITICAL]' in line:
                parsed_data['errors'].append(line)
        
        return parsed_data
    
    def _extract_findings(self, parsed_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract security findings from SQLmap results"""
        findings = []
        
        target = parsed_data.get('target', 'unknown')
        
        # SQL injection vulnerabilities
        for vuln in parsed_data.get('vulnerabilities', []):
            finding = {
                'type': 'sql_injection',
                'severity': Severity.HIGH.value,
                'target': target,
                'parameter': vuln.get('parameter'),
                'description': f"SQL injection vulnerability in parameter '{vuln.get('parameter')}'",
                'details': vuln.get('description', ''),
                'recommendation': 'Use parameterized queries and input validation to prevent SQL injection'
            }
            findings.append(finding)
        
        # If injection points found but no explicit vulnerabilities
        if parsed_data.get('injection_points') and not parsed_data.get('vulnerabilities'):
            finding = {
                'type': 'potential_sql_injection',
                'severity': Severity.MEDIUM.value,
                'target': target,
                'description': 'Potential SQL injection points detected',
                'details': '\n'.join(parsed_data.get('injection_points', [])),
                'recommendation': 'Further manual testing recommended to confirm vulnerabilities'
            }
            findings.append(finding)
        
        return findings
    
    def validate_target(self, target: str) -> bool:
        """Validate SQLmap target URL format"""
        if not target or not target.strip():
            return False
        
        try:
            parsed = urlparse(target)
            return bool(parsed.scheme and parsed.netloc)
        except Exception:
            return False
