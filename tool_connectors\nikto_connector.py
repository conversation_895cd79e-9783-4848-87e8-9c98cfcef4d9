"""
Nikto Tool Connector

This module provides integration with Nikto for web server vulnerability scanning.
"""

import json
import re
from typing import Any, Dict, List
from urllib.parse import urlparse

from .base import BaseToolConnector, Severity


class NiktoConnector(BaseToolConnector):
    """Connector for Nikto web vulnerability scanner"""
    
    @property
    def tool_name(self) -> str:
        return "nikto"
    
    @property
    def default_command(self) -> str:
        return "nikto"
    
    def build_command(self, target: str, **kwargs) -> List[str]:
        """
        Build Nikto command with specified options.
        
        Args:
            target: Target URL to scan
            **kwargs: Nikto options
                - port: Port number
                - ssl: Use SSL
                - plugins: Plugins to use
                - tuning: Tuning options
                - timeout: Request timeout
                - user_agent: User agent string
                - output_format: Output format (txt, xml, csv)
        """
        cmd = [self.tool_path or self.default_command]
        
        # Target host
        cmd.extend(['-h', target])
        
        # Port
        port = kwargs.get('port')
        if port:
            cmd.extend(['-p', str(port)])
        
        # SSL
        if kwargs.get('ssl', False):
            cmd.append('-ssl')
        
        # Plugins
        plugins = kwargs.get('plugins')
        if plugins:
            if isinstance(plugins, list):
                plugins = ','.join(plugins)
            cmd.extend(['-Plugins', plugins])
        
        # Tuning
        tuning = kwargs.get('tuning')
        if tuning:
            cmd.extend(['-Tuning', tuning])
        
        # Timeout
        timeout = kwargs.get('timeout', 10)
        cmd.extend(['-timeout', str(timeout)])
        
        # User agent
        user_agent = kwargs.get('user_agent')
        if user_agent:
            cmd.extend(['-useragent', user_agent])
        
        # Output format
        output_format = kwargs.get('output_format', 'txt')
        if output_format == 'xml':
            cmd.extend(['-Format', 'xml'])
        elif output_format == 'csv':
            cmd.extend(['-Format', 'csv'])
        
        # No interactive mode
        cmd.append('-nointeractive')
        
        return cmd
    
    def parse_output(self, stdout: str, stderr: str) -> Dict[str, Any]:
        """
        Parse Nikto output into standardized format.
        
        Args:
            stdout: Nikto stdout output
            stderr: Nikto stderr output
            
        Returns:
            Parsed data dictionary
        """
        parsed_data = {
            'target': '',
            'scan_info': {},
            'vulnerabilities': [],
            'server_info': {},
            'warnings': [],
            'errors': []
        }
        
        lines = stdout.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # Extract target information
            if 'Target IP:' in line:
                ip_match = re.search(r'Target IP:\s+([^\s]+)', line)
                if ip_match:
                    parsed_data['target'] = ip_match.group(1)
            
            if 'Target Hostname:' in line:
                hostname_match = re.search(r'Target Hostname:\s+([^\s]+)', line)
                if hostname_match:
                    parsed_data['scan_info']['hostname'] = hostname_match.group(1)
            
            if 'Target Port:' in line:
                port_match = re.search(r'Target Port:\s+([^\s]+)', line)
                if port_match:
                    parsed_data['scan_info']['port'] = port_match.group(1)
            
            # Extract server information
            if 'Server:' in line and 'banner' not in line.lower():
                server_match = re.search(r'Server:\s+(.+)', line)
                if server_match:
                    parsed_data['server_info']['server'] = server_match.group(1)
            
            # Extract vulnerabilities (lines starting with +)
            if line.startswith('+'):
                vuln_info = self._parse_vulnerability_line(line)
                if vuln_info:
                    parsed_data['vulnerabilities'].append(vuln_info)
            
            # Extract warnings
            if 'WARNING' in line.upper():
                parsed_data['warnings'].append(line)
            
            # Extract errors
            if 'ERROR' in line.upper():
                parsed_data['errors'].append(line)
        
        return parsed_data
    
    def _parse_vulnerability_line(self, line: str) -> Dict[str, Any]:
        """Parse a vulnerability line from Nikto output"""
        # Remove the leading +
        line = line[1:].strip()
        
        # Try to extract OSVDB ID
        osvdb_match = re.search(r'OSVDB-(\d+)', line)
        osvdb_id = osvdb_match.group(1) if osvdb_match else None
        
        # Try to extract URL path
        url_match = re.search(r'(/[^\s:]+)', line)
        url_path = url_match.group(1) if url_match else None
        
        # The rest is description
        description = line
        if osvdb_id:
            description = re.sub(r'OSVDB-\d+:\s*', '', description)
        
        return {
            'osvdb_id': osvdb_id,
            'url_path': url_path,
            'description': description,
            'raw_line': line
        }
    
    def _extract_findings(self, parsed_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract security findings from Nikto results"""
        findings = []
        
        target = parsed_data.get('target', 'unknown')
        
        for vuln in parsed_data.get('vulnerabilities', []):
            # Assess severity based on description keywords
            severity = self._assess_vulnerability_severity(vuln.get('description', ''))
            
            finding = {
                'type': 'web_vulnerability',
                'severity': severity,
                'target': target,
                'url_path': vuln.get('url_path'),
                'osvdb_id': vuln.get('osvdb_id'),
                'description': vuln.get('description'),
                'recommendation': self._get_vulnerability_recommendation(vuln.get('description', ''))
            }
            findings.append(finding)
        
        return findings
    
    def _assess_vulnerability_severity(self, description: str) -> str:
        """Assess vulnerability severity based on description"""
        description_lower = description.lower()
        
        # Critical vulnerabilities
        if any(keyword in description_lower for keyword in [
            'remote code execution', 'rce', 'shell', 'backdoor', 'arbitrary file'
        ]):
            return Severity.CRITICAL.value
        
        # High severity
        if any(keyword in description_lower for keyword in [
            'sql injection', 'xss', 'csrf', 'authentication bypass', 'privilege escalation'
        ]):
            return Severity.HIGH.value
        
        # Medium severity
        if any(keyword in description_lower for keyword in [
            'information disclosure', 'directory traversal', 'weak', 'misconfiguration'
        ]):
            return Severity.MEDIUM.value
        
        # Low severity
        if any(keyword in description_lower for keyword in [
            'banner', 'version', 'fingerprint', 'robots.txt'
        ]):
            return Severity.LOW.value
        
        return Severity.INFO.value
    
    def _get_vulnerability_recommendation(self, description: str) -> str:
        """Get recommendation based on vulnerability description"""
        description_lower = description.lower()
        
        if 'version' in description_lower or 'banner' in description_lower:
            return 'Update to the latest version and configure server to hide version information'
        
        if 'directory' in description_lower:
            return 'Restrict directory access and remove unnecessary files'
        
        if 'authentication' in description_lower:
            return 'Implement proper authentication mechanisms'
        
        if 'ssl' in description_lower or 'tls' in description_lower:
            return 'Update SSL/TLS configuration and use strong ciphers'
        
        return 'Review the vulnerability details and apply appropriate security measures'
    
    def validate_target(self, target: str) -> bool:
        """Validate Nikto target format"""
        if not target or not target.strip():
            return False
        
        # Can be URL or just hostname/IP
        if target.startswith('http://') or target.startswith('https://'):
            try:
                parsed = urlparse(target)
                return bool(parsed.netloc)
            except Exception:
                return False
        else:
            # Just hostname or IP
            return bool(re.match(r'^[a-zA-Z0-9.-]+$', target))
