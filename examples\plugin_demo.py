#!/usr/bin/env python3
"""
Plugin System Demo

This script demonstrates the plugin system capabilities of the tool connector framework.
It shows how to:
1. Create custom tool connector plugins
2. Register plugins dynamically
3. Discover plugins from directories
4. Use plugins in ToolManager
5. Manage plugin metadata
"""

import sys
import tempfile
from pathlib import Path
from typing import Any, Dict, List

# Add the parent directory to the path so we can import tool_connectors
sys.path.insert(0, str(Path(__file__).parent.parent))

from tool_connectors.plugin_system import get_plugin_manager, PluginManager
from tool_connectors import ToolManager
from tool_connectors.base import BaseToolConnector, ToolResult, ToolStatus, Severity


class CustomScannerConnector(BaseToolConnector):
    """
    Example custom scanner connector.
    
    This demonstrates how to create a custom tool connector that can be used as a plugin.
    """
    
    # Plugin metadata
    __plugin_metadata__ = {
        "version": "1.0.0",
        "author": "Demo Author",
        "description": "Custom scanner for demonstration",
        "url": "https://github.com/example/custom-scanner",
        "license": "MIT",
        "supported_platforms": ["linux", "windows", "macos"],
        "capabilities": ["port_scanning", "service_detection"]
    }
    
    def __init__(self, tool_path: str = None, timeout: int = 300, 
                 mock_mode: bool = False, mock_data: Dict[str, Any] = None):
        """Initialize the custom scanner connector."""
        super().__init__(tool_path, timeout, mock_mode, mock_data)
    
    @property
    def tool_name(self) -> str:
        """Get the tool name."""
        return "custom_scanner"
    
    @property
    def default_command(self) -> str:
        """Get the default command."""
        return "custom-scanner"
    
    def build_command(self, target: str, **kwargs) -> List[str]:
        """Build command for the custom scanner."""
        cmd = [self.tool_path or self.default_command]
        
        # Add target
        cmd.extend(["--target", target])
        
        # Add optional parameters
        if kwargs.get('fast_scan'):
            cmd.append("--fast")
        
        if kwargs.get('deep_scan'):
            cmd.append("--deep")
        
        if kwargs.get('output_format'):
            cmd.extend(["--format", kwargs['output_format']])
        
        if kwargs.get('ports'):
            cmd.extend(["--ports", str(kwargs['ports'])])
        
        return cmd
    
    def parse_output(self, stdout: str, stderr: str) -> Dict[str, Any]:
        """Parse custom scanner output."""
        parsed = {
            'target': '',
            'open_ports': [],
            'services': [],
            'vulnerabilities': []
        }
        
        lines = stdout.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse target
            if line.startswith('Scanning target:'):
                parsed['target'] = line.split(':', 1)[1].strip()
            
            # Parse open ports
            elif line.startswith('Open port:'):
                port_info = line.split(':', 1)[1].strip()
                parsed['open_ports'].append(port_info)
            
            # Parse services
            elif line.startswith('Service detected:'):
                service_info = line.split(':', 1)[1].strip()
                parsed['services'].append(service_info)
            
            # Parse vulnerabilities
            elif line.startswith('Vulnerability:'):
                vuln_info = line.split(':', 1)[1].strip()
                parsed['vulnerabilities'].append(vuln_info)
        
        return parsed
    
    def validate_target(self, target: str) -> bool:
        """Validate target format."""
        # Accept IP addresses and hostnames
        return bool(target and target.strip() and not target.startswith('-'))
    
    def _extract_findings(self, parsed_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract findings from parsed data."""
        findings = []
        target = parsed_data.get('target', 'unknown')
        
        # Add findings for open ports
        for port in parsed_data.get('open_ports', []):
            findings.append({
                'type': 'open_port',
                'severity': Severity.INFO.value,
                'target': target,
                'port': port,
                'description': f"Open port detected: {port}",
                'recommendation': "Review if this port should be open"
            })
        
        # Add findings for services
        for service in parsed_data.get('services', []):
            findings.append({
                'type': 'service_detection',
                'severity': Severity.LOW.value,
                'target': target,
                'service': service,
                'description': f"Service detected: {service}",
                'recommendation': "Ensure service is properly secured"
            })
        
        # Add findings for vulnerabilities
        for vuln in parsed_data.get('vulnerabilities', []):
            findings.append({
                'type': 'vulnerability',
                'severity': self._assess_vulnerability_severity(vuln),
                'target': target,
                'vulnerability': vuln,
                'description': f"Vulnerability found: {vuln}",
                'recommendation': "Address this vulnerability immediately"
            })
        
        return findings
    
    def _assess_vulnerability_severity(self, vulnerability: str) -> str:
        """Assess vulnerability severity."""
        vuln_lower = vulnerability.lower()
        
        if any(keyword in vuln_lower for keyword in ['critical', 'rce', 'remote code']):
            return Severity.CRITICAL.value
        elif any(keyword in vuln_lower for keyword in ['high', 'privilege escalation']):
            return Severity.HIGH.value
        elif any(keyword in vuln_lower for keyword in ['medium', 'injection', 'xss']):
            return Severity.MEDIUM.value
        else:
            return Severity.LOW.value
    
    def _generate_mock_output(self, target: str, **kwargs) -> Dict[str, Any]:
        """Generate mock output for testing/demo purposes."""
        mock_output = f"""Custom Scanner v1.0.0
Scanning target: {target}
Starting comprehensive scan...

Open port: 22/tcp (SSH)
Open port: 80/tcp (HTTP)
Open port: 443/tcp (HTTPS)

Service detected: OpenSSH 7.4 on port 22
Service detected: Apache 2.4.41 on port 80
Service detected: Apache 2.4.41 on port 443

Vulnerability: Outdated SSH version detected
Vulnerability: Missing security headers on HTTP service

Scan completed successfully
Found 3 open ports, 3 services, 2 vulnerabilities
"""
        
        findings = [
            {
                'type': 'open_port',
                'severity': 'info',
                'target': target,
                'port': '22/tcp',
                'description': 'Open port detected: 22/tcp (SSH)',
                'recommendation': 'Review if this port should be open'
            },
            {
                'type': 'service_detection',
                'severity': 'low',
                'target': target,
                'service': 'OpenSSH 7.4',
                'description': 'Service detected: OpenSSH 7.4 on port 22',
                'recommendation': 'Ensure service is properly secured'
            },
            {
                'type': 'vulnerability',
                'severity': 'medium',
                'target': target,
                'vulnerability': 'Outdated SSH version',
                'description': 'Vulnerability found: Outdated SSH version detected',
                'recommendation': 'Address this vulnerability immediately'
            }
        ]
        
        return {
            'stdout': mock_output,
            'stderr': '',
            'findings': findings
        }


def demo_plugin_registration():
    """Demonstrate plugin registration"""
    print("=== Plugin Registration Demo ===")
    
    plugin_manager = get_plugin_manager()
    
    # Register a custom plugin
    plugin_manager.register_plugin_class(
        "custom_scanner",
        CustomScannerConnector,
        {
            "category": "network_scanner",
            "tags": ["custom", "demo", "scanner"]
        }
    )
    
    print("Registered custom scanner plugin")
    
    # List all plugins
    plugins = plugin_manager.list_plugins()
    print(f"Available plugins: {plugins}")
    
    # Get plugin information
    plugin_info = plugin_manager.get_plugin("custom_scanner")
    if plugin_info:
        print(f"Plugin info: {plugin_info}")
        print(f"Plugin metadata: {plugin_info.metadata}")
    
    print()


def demo_plugin_creation():
    """Demonstrate creating connectors from plugins"""
    print("=== Plugin Creation Demo ===")
    
    plugin_manager = get_plugin_manager()
    
    # Create connector from plugin
    connector = plugin_manager.create_connector("custom_scanner", mock_mode=True)
    
    if connector:
        print(f"Created connector: {connector.tool_name}")
        print(f"Default command: {connector.default_command}")
        
        # Test the connector
        result = connector.execute("example.com", fast_scan=True)
        print(f"Execution result: {result.status.value}")
        print(f"Findings: {len(result.findings)}")
        
        if result.findings:
            print("Sample findings:")
            for finding in result.findings[:3]:  # Show first 3
                print(f"  - {finding.get('description', 'No description')}")
    else:
        print("Failed to create connector")
    
    print()


def demo_tool_manager_integration():
    """Demonstrate ToolManager integration with plugins"""
    print("=== ToolManager Plugin Integration Demo ===")
    
    # Create ToolManager
    manager = ToolManager(mock_mode=True)
    
    # Register plugin with ToolManager
    manager.register_plugin_connector(
        "custom_scanner",
        CustomScannerConnector,
        {"integration_demo": True}
    )
    
    print("Registered plugin with ToolManager")
    
    # Show available tools
    available_tools = manager.get_available_tools()
    print(f"Available tools: {available_tools}")
    
    # Execute plugin through ToolManager
    print("Executing custom scanner through ToolManager...")
    result = manager.execute_tool("custom_scanner", "demo.example.com", deep_scan=True)
    
    print(f"Execution status: {result.status.value}")
    print(f"Tool name: {result.tool_name}")
    print(f"Execution time: {result.execution_time:.2f}s")
    print(f"Number of findings: {len(result.findings)}")
    
    # Show plugin information
    plugin_info = manager.get_plugin_info("custom_scanner")
    if plugin_info:
        print(f"Plugin class: {plugin_info['class']}")
        print(f"Plugin metadata keys: {list(plugin_info['metadata'].keys())}")
    
    print()


def demo_plugin_discovery():
    """Demonstrate plugin discovery from files"""
    print("=== Plugin Discovery Demo ===")
    
    # Create a temporary plugin file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        plugin_content = '''
from tool_connectors.base import BaseToolConnector
from typing import Any, Dict, List

__plugin_name__ = "discovered_tool"
__plugin_metadata__ = {
    "version": "1.0.0",
    "author": "Discovery Demo",
    "description": "Tool discovered from file"
}

class DiscoveredToolConnector(BaseToolConnector):
    @property
    def tool_name(self) -> str:
        return "discovered_tool"
    
    @property
    def default_command(self) -> str:
        return "discovered-tool"
    
    def build_command(self, target: str, **kwargs) -> List[str]:
        return [self.default_command, target]
    
    def parse_output(self, stdout: str, stderr: str) -> Dict[str, Any]:
        return {"target": target, "findings": []}
    
    def validate_target(self, target: str) -> bool:
        return bool(target and target.strip())
    
    def _extract_findings(self, parsed_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        return []
'''
        f.write(plugin_content)
        plugin_file = f.name
    
    try:
        # Add the directory containing the plugin file
        plugin_dir = str(Path(plugin_file).parent)
        plugin_manager = PluginManager()
        plugin_manager.add_plugin_directory(plugin_dir)
        
        # Discover plugins
        print(f"Discovering plugins in: {plugin_dir}")
        discovered = plugin_manager.discover_plugins(auto_load=True)
        
        print(f"Discovered {len(discovered)} plugins:")
        for plugin in discovered:
            if plugin.name == "discovered_tool":
                print(f"  - {plugin.name}: {plugin.connector_class.__name__}")
                print(f"    Metadata: {plugin.metadata}")
                
                # Test the discovered plugin
                connector = plugin_manager.create_connector("discovered_tool", mock_mode=True)
                if connector:
                    print(f"    Successfully created connector: {connector.tool_name}")
                break
        else:
            print("  No plugins discovered (this is expected on some systems)")
    
    finally:
        # Clean up
        import os
        try:
            os.unlink(plugin_file)
        except:
            pass
    
    print()


def demo_plugin_metadata():
    """Demonstrate plugin metadata handling"""
    print("=== Plugin Metadata Demo ===")
    
    plugin_manager = get_plugin_manager()
    
    # Get metadata for existing plugin
    metadata = plugin_manager.get_plugin_metadata("custom_scanner")
    if metadata:
        print("Custom scanner metadata:")
        for key, value in metadata.items():
            print(f"  {key}: {value}")
    
    # Show all plugin information
    all_plugins = plugin_manager.get_all_plugins()
    print(f"\nAll registered plugins ({len(all_plugins)}):")
    for name, plugin_info in all_plugins.items():
        print(f"  {name}:")
        print(f"    Class: {plugin_info.connector_class.__name__}")
        print(f"    Module: {plugin_info.module_path}")
        print(f"    Metadata keys: {list(plugin_info.metadata.keys())}")
    
    print()


def demo_plugin_unregistration():
    """Demonstrate plugin unregistration"""
    print("=== Plugin Unregistration Demo ===")
    
    manager = ToolManager(mock_mode=True)
    
    # Register a plugin
    manager.register_plugin_connector("temp_plugin", CustomScannerConnector)
    print("Registered temporary plugin")
    
    # Show it's available
    print(f"Available tools: {manager.get_available_tools()}")
    
    # Unregister the plugin
    result = manager.unregister_connector("temp_plugin")
    print(f"Unregistration result: {result}")
    
    # Show it's no longer available
    print(f"Available tools after unregistration: {manager.get_available_tools()}")
    
    print()


def main():
    """Main function to run all plugin demos"""
    print("Tool Connectors Framework - Plugin System Demo")
    print("=" * 60)
    print("This demo shows how to create and use custom tool connector plugins.")
    print()
    
    try:
        demo_plugin_registration()
        demo_plugin_creation()
        demo_tool_manager_integration()
        demo_plugin_discovery()
        demo_plugin_metadata()
        demo_plugin_unregistration()
        
        print("=== Plugin Demo Summary ===")
        print("All plugin demos completed successfully!")
        print("The plugin system supports:")
        print("- Dynamic plugin registration")
        print("- Plugin discovery from files and directories")
        print("- Integration with ToolManager")
        print("- Plugin metadata management")
        print("- Runtime plugin management (register/unregister)")
        print("- Custom connector creation")
        
    except Exception as e:
        print(f"Error during demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
