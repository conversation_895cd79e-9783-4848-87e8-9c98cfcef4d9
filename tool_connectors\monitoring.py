"""
Comprehensive logging and monitoring system for tool connectors.

This module provides structured logging, performance metrics, health checks,
and monitoring capabilities for the tool connector framework.
"""

import json
import logging
import logging.handlers
import os
import psutil
import threading
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Callable
from enum import Enum

from .base import ToolResult, ToolStatus


class LogLevel(Enum):
    """Log levels for structured logging."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class HealthStatus(Enum):
    """Health check status."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"


@dataclass
class MetricPoint:
    """A single metric data point."""
    name: str
    value: float
    timestamp: float
    tags: Dict[str, str] = field(default_factory=dict)
    unit: str = ""


@dataclass
class HealthCheck:
    """Health check definition."""
    name: str
    check_function: Callable[[], bool]
    description: str
    timeout: float = 5.0
    critical: bool = True


@dataclass
class PerformanceMetrics:
    """Performance metrics for tool execution."""
    tool_name: str
    target: str
    execution_time: float
    memory_usage: float
    cpu_usage: float
    status: ToolStatus
    timestamp: float
    findings_count: int = 0
    cache_hit: bool = False


class StructuredLogger:
    """
    Structured logger with JSON output and multiple handlers.
    """
    
    def __init__(self, name: str, log_level: str = "INFO", 
                 log_file: Optional[str] = None, max_file_size: int = 10485760):
        """
        Initialize structured logger.
        
        Args:
            name: Logger name
            log_level: Logging level
            log_file: Optional log file path
            max_file_size: Maximum log file size in bytes (10MB default)
        """
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Console handler with structured format
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(self._create_formatter())
        self.logger.addHandler(console_handler)
        
        # File handler if specified
        if log_file:
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.handlers.RotatingFileHandler(
                log_file, maxBytes=max_file_size, backupCount=5
            )
            file_handler.setFormatter(self._create_json_formatter())
            self.logger.addHandler(file_handler)
    
    def _create_formatter(self) -> logging.Formatter:
        """Create console formatter."""
        return logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    def _create_json_formatter(self) -> logging.Formatter:
        """Create JSON formatter for file output."""
        class JsonFormatter(logging.Formatter):
            def format(self, record):
                log_entry = {
                    'timestamp': datetime.fromtimestamp(record.created).isoformat(),
                    'level': record.levelname,
                    'logger': record.name,
                    'message': record.getMessage(),
                    'module': record.module,
                    'function': record.funcName,
                    'line': record.lineno
                }
                
                # Add exception info if present
                if record.exc_info:
                    log_entry['exception'] = self.formatException(record.exc_info)
                
                # Add extra fields
                for key, value in record.__dict__.items():
                    if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 
                                  'pathname', 'filename', 'module', 'lineno', 
                                  'funcName', 'created', 'msecs', 'relativeCreated',
                                  'thread', 'threadName', 'processName', 'process',
                                  'getMessage', 'exc_info', 'exc_text', 'stack_info']:
                        log_entry[key] = value
                
                return json.dumps(log_entry)
        
        return JsonFormatter()
    
    def log(self, level: LogLevel, message: str, **kwargs):
        """Log a structured message."""
        extra = kwargs
        getattr(self.logger, level.value.lower())(message, extra=extra)
    
    def debug(self, message: str, **kwargs):
        """Log debug message."""
        self.log(LogLevel.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message."""
        self.log(LogLevel.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message."""
        self.log(LogLevel.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message."""
        self.log(LogLevel.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message."""
        self.log(LogLevel.CRITICAL, message, **kwargs)


class MetricsCollector:
    """
    Metrics collector for performance monitoring.
    """
    
    def __init__(self, max_metrics: int = 10000):
        """
        Initialize metrics collector.
        
        Args:
            max_metrics: Maximum number of metrics to keep in memory
        """
        self.max_metrics = max_metrics
        self.metrics: deque = deque(maxlen=max_metrics)
        self.counters: Dict[str, int] = defaultdict(int)
        self.gauges: Dict[str, float] = {}
        self.histograms: Dict[str, List[float]] = defaultdict(list)
        self._lock = threading.Lock()
    
    def record_metric(self, name: str, value: float, tags: Optional[Dict[str, str]] = None, unit: str = ""):
        """Record a metric point."""
        with self._lock:
            metric = MetricPoint(
                name=name,
                value=value,
                timestamp=time.time(),
                tags=tags or {},
                unit=unit
            )
            self.metrics.append(metric)
    
    def increment_counter(self, name: str, value: int = 1, tags: Optional[Dict[str, str]] = None):
        """Increment a counter metric."""
        key = f"{name}:{json.dumps(tags or {}, sort_keys=True)}"
        with self._lock:
            self.counters[key] += value
        
        self.record_metric(name, self.counters[key], tags, "count")
    
    def set_gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """Set a gauge metric."""
        key = f"{name}:{json.dumps(tags or {}, sort_keys=True)}"
        with self._lock:
            self.gauges[key] = value
        
        self.record_metric(name, value, tags, "gauge")
    
    def record_histogram(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """Record a histogram value."""
        key = f"{name}:{json.dumps(tags or {}, sort_keys=True)}"
        with self._lock:
            self.histograms[key].append(value)
            # Keep only recent values
            if len(self.histograms[key]) > 1000:
                self.histograms[key] = self.histograms[key][-1000:]
        
        self.record_metric(name, value, tags, "histogram")
    
    def get_metrics(self, name_filter: Optional[str] = None, 
                   since: Optional[float] = None) -> List[MetricPoint]:
        """Get metrics with optional filtering."""
        with self._lock:
            filtered_metrics = []
            
            for metric in self.metrics:
                # Filter by name
                if name_filter and name_filter not in metric.name:
                    continue
                
                # Filter by time
                if since and metric.timestamp < since:
                    continue
                
                filtered_metrics.append(metric)
            
            return filtered_metrics
    
    def get_summary(self) -> Dict[str, Any]:
        """Get metrics summary."""
        with self._lock:
            now = time.time()
            recent_metrics = [m for m in self.metrics if now - m.timestamp < 3600]  # Last hour
            
            summary = {
                'total_metrics': len(self.metrics),
                'recent_metrics': len(recent_metrics),
                'counters': dict(self.counters),
                'gauges': dict(self.gauges),
                'histogram_counts': {k: len(v) for k, v in self.histograms.items()}
            }
            
            return summary


class HealthMonitor:
    """
    Health monitoring system with configurable checks.
    """
    
    def __init__(self):
        """Initialize health monitor."""
        self.checks: Dict[str, HealthCheck] = {}
        self.last_results: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.Lock()
        
        # Add default system health checks
        self._add_default_checks()
    
    def _add_default_checks(self):
        """Add default system health checks."""
        self.add_check(HealthCheck(
            name="memory_usage",
            check_function=lambda: psutil.virtual_memory().percent < 90,
            description="System memory usage below 90%",
            critical=False
        ))
        
        self.add_check(HealthCheck(
            name="disk_usage",
            check_function=lambda: psutil.disk_usage('/').percent < 95,
            description="Disk usage below 95%",
            critical=True
        ))
        
        self.add_check(HealthCheck(
            name="cpu_usage",
            check_function=lambda: psutil.cpu_percent(interval=1) < 95,
            description="CPU usage below 95%",
            critical=False
        ))
    
    def add_check(self, check: HealthCheck):
        """Add a health check."""
        with self._lock:
            self.checks[check.name] = check
    
    def remove_check(self, name: str):
        """Remove a health check."""
        with self._lock:
            self.checks.pop(name, None)
            self.last_results.pop(name, None)
    
    def run_check(self, name: str) -> Dict[str, Any]:
        """Run a specific health check."""
        if name not in self.checks:
            return {
                'name': name,
                'status': HealthStatus.UNHEALTHY.value,
                'message': f"Check '{name}' not found",
                'timestamp': time.time()
            }
        
        check = self.checks[name]
        start_time = time.time()
        
        try:
            # Run check with timeout
            result = check.check_function()
            execution_time = time.time() - start_time
            
            if execution_time > check.timeout:
                status = HealthStatus.DEGRADED.value
                message = f"Check completed but took {execution_time:.2f}s (timeout: {check.timeout}s)"
            elif result:
                status = HealthStatus.HEALTHY.value
                message = "Check passed"
            else:
                status = HealthStatus.UNHEALTHY.value
                message = "Check failed"
            
        except Exception as e:
            execution_time = time.time() - start_time
            status = HealthStatus.UNHEALTHY.value
            message = f"Check error: {str(e)}"
        
        result_data = {
            'name': name,
            'status': status,
            'message': message,
            'execution_time': execution_time,
            'timestamp': time.time(),
            'critical': check.critical,
            'description': check.description
        }
        
        with self._lock:
            self.last_results[name] = result_data
        
        return result_data
    
    def run_all_checks(self) -> Dict[str, Any]:
        """Run all health checks."""
        results = {}
        overall_status = HealthStatus.HEALTHY
        
        for name in self.checks:
            result = self.run_check(name)
            results[name] = result
            
            # Determine overall status
            if result['status'] == HealthStatus.UNHEALTHY.value:
                if result['critical']:
                    overall_status = HealthStatus.UNHEALTHY
                elif overall_status == HealthStatus.HEALTHY:
                    overall_status = HealthStatus.DEGRADED
            elif result['status'] == HealthStatus.DEGRADED.value and overall_status == HealthStatus.HEALTHY:
                overall_status = HealthStatus.DEGRADED
        
        return {
            'overall_status': overall_status.value,
            'timestamp': time.time(),
            'checks': results
        }
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get health summary."""
        with self._lock:
            return {
                'total_checks': len(self.checks),
                'last_results': dict(self.last_results)
            }


class PerformanceMonitor:
    """
    Performance monitoring for tool execution.
    """

    def __init__(self, metrics_collector: MetricsCollector):
        """
        Initialize performance monitor.

        Args:
            metrics_collector: Metrics collector instance
        """
        self.metrics = metrics_collector
        self.execution_history: deque = deque(maxlen=1000)
        self._lock = threading.Lock()

    def start_execution(self, tool_name: str, target: str) -> Dict[str, Any]:
        """Start monitoring tool execution."""
        context = {
            'tool_name': tool_name,
            'target': target,
            'start_time': time.time(),
            'start_memory': psutil.Process().memory_info().rss,
            'start_cpu': psutil.Process().cpu_percent()
        }
        return context

    def end_execution(self, context: Dict[str, Any], result: ToolResult, cache_hit: bool = False):
        """End monitoring tool execution."""
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss
        end_cpu = psutil.Process().cpu_percent()

        execution_time = end_time - context['start_time']
        memory_usage = end_memory - context['start_memory']
        cpu_usage = end_cpu - context['start_cpu']

        # Create performance metrics
        perf_metrics = PerformanceMetrics(
            tool_name=context['tool_name'],
            target=context['target'],
            execution_time=execution_time,
            memory_usage=memory_usage,
            cpu_usage=cpu_usage,
            status=result.status,
            timestamp=end_time,
            findings_count=len(result.findings),
            cache_hit=cache_hit
        )

        # Record metrics
        tags = {
            'tool': context['tool_name'],
            'status': result.status.value,
            'cache_hit': str(cache_hit)
        }

        self.metrics.record_histogram('execution_time', execution_time, tags)
        self.metrics.record_histogram('memory_usage', memory_usage, tags)
        self.metrics.record_histogram('cpu_usage', cpu_usage, tags)
        self.metrics.increment_counter('tool_executions', 1, tags)
        self.metrics.set_gauge('findings_count', len(result.findings), tags)

        # Store in history
        with self._lock:
            self.execution_history.append(perf_metrics)

        return perf_metrics

    def get_performance_summary(self, tool_name: Optional[str] = None,
                              hours: int = 24) -> Dict[str, Any]:
        """Get performance summary."""
        cutoff_time = time.time() - (hours * 3600)

        with self._lock:
            # Filter metrics
            filtered_metrics = [
                m for m in self.execution_history
                if m.timestamp >= cutoff_time and (not tool_name or m.tool_name == tool_name)
            ]

        if not filtered_metrics:
            return {'message': 'No metrics found for the specified criteria'}

        # Calculate statistics
        execution_times = [m.execution_time for m in filtered_metrics]
        memory_usage = [m.memory_usage for m in filtered_metrics]
        findings_counts = [m.findings_count for m in filtered_metrics]

        # Status distribution
        status_counts = defaultdict(int)
        for m in filtered_metrics:
            status_counts[m.status.value] += 1

        # Cache hit rate
        cache_hits = sum(1 for m in filtered_metrics if m.cache_hit)
        cache_hit_rate = cache_hits / len(filtered_metrics) if filtered_metrics else 0

        return {
            'period_hours': hours,
            'total_executions': len(filtered_metrics),
            'execution_time': {
                'avg': sum(execution_times) / len(execution_times),
                'min': min(execution_times),
                'max': max(execution_times),
                'p95': sorted(execution_times)[int(len(execution_times) * 0.95)] if execution_times else 0
            },
            'memory_usage': {
                'avg': sum(memory_usage) / len(memory_usage),
                'min': min(memory_usage),
                'max': max(memory_usage)
            },
            'findings': {
                'avg': sum(findings_counts) / len(findings_counts),
                'total': sum(findings_counts)
            },
            'status_distribution': dict(status_counts),
            'cache_hit_rate': cache_hit_rate
        }


class MonitoringDashboard:
    """
    Simple text-based monitoring dashboard.
    """

    def __init__(self, metrics_collector: MetricsCollector,
                 health_monitor: HealthMonitor,
                 performance_monitor: PerformanceMonitor):
        """
        Initialize monitoring dashboard.

        Args:
            metrics_collector: Metrics collector instance
            health_monitor: Health monitor instance
            performance_monitor: Performance monitor instance
        """
        self.metrics = metrics_collector
        self.health = health_monitor
        self.performance = performance_monitor

    def generate_dashboard(self) -> str:
        """Generate text-based dashboard."""
        lines = []
        lines.append("=" * 80)
        lines.append("TOOL CONNECTORS MONITORING DASHBOARD")
        lines.append("=" * 80)
        lines.append(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("")

        # Health Status
        lines.append("HEALTH STATUS")
        lines.append("-" * 40)
        health_summary = self.health.run_all_checks()
        lines.append(f"Overall Status: {health_summary['overall_status'].upper()}")

        for name, check in health_summary['checks'].items():
            status_icon = "✓" if check['status'] == 'healthy' else "⚠" if check['status'] == 'degraded' else "✗"
            lines.append(f"  {status_icon} {name}: {check['status']} - {check['message']}")

        lines.append("")

        # Performance Metrics
        lines.append("PERFORMANCE METRICS (Last 24 Hours)")
        lines.append("-" * 40)
        perf_summary = self.performance.get_performance_summary()

        if 'message' in perf_summary:
            lines.append(f"  {perf_summary['message']}")
        else:
            lines.append(f"  Total Executions: {perf_summary['total_executions']}")
            lines.append(f"  Avg Execution Time: {perf_summary['execution_time']['avg']:.2f}s")
            lines.append(f"  Cache Hit Rate: {perf_summary['cache_hit_rate']:.1%}")
            lines.append(f"  Total Findings: {perf_summary['findings']['total']}")

            lines.append("  Status Distribution:")
            for status, count in perf_summary['status_distribution'].items():
                lines.append(f"    {status}: {count}")

        lines.append("")

        # System Metrics
        lines.append("SYSTEM METRICS")
        lines.append("-" * 40)
        lines.append(f"  CPU Usage: {psutil.cpu_percent(interval=1):.1f}%")
        lines.append(f"  Memory Usage: {psutil.virtual_memory().percent:.1f}%")
        lines.append(f"  Disk Usage: {psutil.disk_usage('/').percent:.1f}%")

        # Recent Metrics
        lines.append("")
        lines.append("RECENT METRICS (Last Hour)")
        lines.append("-" * 40)
        recent_metrics = self.metrics.get_metrics(since=time.time() - 3600)

        if recent_metrics:
            lines.append(f"  Total Metrics: {len(recent_metrics)}")

            # Group by metric name
            metric_groups = defaultdict(list)
            for metric in recent_metrics:
                metric_groups[metric.name].append(metric.value)

            for name, values in list(metric_groups.items())[:10]:  # Show top 10
                avg_value = sum(values) / len(values)
                lines.append(f"  {name}: {avg_value:.2f} (avg of {len(values)} points)")
        else:
            lines.append("  No recent metrics available")

        lines.append("")
        lines.append("=" * 80)

        return "\n".join(lines)

    def save_dashboard(self, file_path: str):
        """Save dashboard to file."""
        dashboard_content = self.generate_dashboard()
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(dashboard_content)

    def print_dashboard(self):
        """Print dashboard to console."""
        print(self.generate_dashboard())


class MonitoringManager:
    """
    Central monitoring manager that coordinates all monitoring components.
    """

    def __init__(self, log_level: str = "INFO", log_file: Optional[str] = None):
        """
        Initialize monitoring manager.

        Args:
            log_level: Logging level
            log_file: Optional log file path
        """
        self.logger = StructuredLogger("MonitoringManager", log_level, log_file)
        self.metrics = MetricsCollector()
        self.health = HealthMonitor()
        self.performance = PerformanceMonitor(self.metrics)
        self.dashboard = MonitoringDashboard(self.metrics, self.health, self.performance)

        self.logger.info("Monitoring manager initialized")

    def start_tool_execution(self, tool_name: str, target: str) -> Dict[str, Any]:
        """Start monitoring a tool execution."""
        context = self.performance.start_execution(tool_name, target)
        self.logger.info(f"Started monitoring execution",
                        tool=tool_name, target=target)
        return context

    def end_tool_execution(self, context: Dict[str, Any], result: ToolResult,
                          cache_hit: bool = False):
        """End monitoring a tool execution."""
        perf_metrics = self.performance.end_execution(context, result, cache_hit)

        self.logger.info(f"Completed tool execution",
                        tool=context['tool_name'],
                        target=context['target'],
                        status=result.status.value,
                        execution_time=perf_metrics.execution_time,
                        findings_count=perf_metrics.findings_count,
                        cache_hit=cache_hit)

        return perf_metrics

    def get_monitoring_summary(self) -> Dict[str, Any]:
        """Get comprehensive monitoring summary."""
        return {
            'health': self.health.get_health_summary(),
            'metrics': self.metrics.get_summary(),
            'performance': self.performance.get_performance_summary()
        }


# Global monitoring manager instance
_monitoring_manager: Optional[MonitoringManager] = None


def get_monitoring_manager(log_level: str = "INFO",
                          log_file: Optional[str] = None) -> MonitoringManager:
    """
    Get the global monitoring manager instance.

    Args:
        log_level: Logging level (only used on first call)
        log_file: Optional log file path (only used on first call)

    Returns:
        MonitoringManager instance
    """
    global _monitoring_manager

    if _monitoring_manager is None:
        _monitoring_manager = MonitoringManager(log_level, log_file)

    return _monitoring_manager
