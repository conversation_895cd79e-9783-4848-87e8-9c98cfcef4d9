{"tests/test_input_module.py": true, "tests/test_nikto_connector.py::TestNiktoConnector::test_extract_scan_info": true, "tests/test_nikto_connector.py::TestNiktoConnector::test_extract_target_info": true, "tests/test_nikto_connector.py::TestNiktoConnector::test_extract_vulnerabilities": true, "tests/test_nikto_connector.py::TestNiktoConnector::test_parse_text_output": true, "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_assess_injection_severity": true, "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_extract_databases": true, "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_extract_dbms": true, "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_extract_injections": true, "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_get_injection_recommendation": true, "tests/test_sqlmap_connector.py::TestSQLmapConnector::test_parse_text_output": true}