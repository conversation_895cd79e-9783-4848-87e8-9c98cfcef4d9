"""
Example plugin for tool connectors.

This is an example of how to create a custom tool connector plugin.
"""

from typing import Any, Dict, List
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tool_connectors.base import BaseToolConnector, ToolResult, ToolStatus, Severity

# Plugin metadata
__plugin_name__ = "example_tool"
__plugin_metadata__ = {
    "version": "1.0.0",
    "author": "Tool Connectors Framework",
    "description": "Example tool connector plugin",
    "url": "https://github.com/example/example-tool",
    "license": "MIT"
}


class ExampleToolConnector(BaseToolConnector):
    """
    Example tool connector implementation.
    
    This demonstrates how to create a custom tool connector plugin.
    """
    
    # Plugin metadata can also be defined as class attributes
    __plugin_metadata__ = {
        "supported_platforms": ["linux", "windows", "macos"],
        "tool_version": "1.0.0",
        "capabilities": ["scanning", "enumeration"]
    }
    
    def __init__(self, tool_path: str = None, timeout: int = 300, 
                 mock_mode: bool = False, mock_data: Dict[str, Any] = None):
        """Initialize the example tool connector."""
        super().__init__(tool_path, timeout, mock_mode, mock_data)
    
    @property
    def tool_name(self) -> str:
        """Get the tool name."""
        return "example_tool"
    
    @property
    def default_command(self) -> str:
        """Get the default command."""
        return "example-tool"
    
    def build_command(self, target: str, **kwargs) -> List[str]:
        """
        Build command for the example tool.
        
        Args:
            target: Target to scan
            **kwargs: Additional parameters
            
        Returns:
            Command as list of strings
        """
        cmd = [self.tool_path or self.default_command]
        
        # Add target
        cmd.extend(["-t", target])
        
        # Add optional parameters
        if kwargs.get('verbose'):
            cmd.append("-v")
        
        if kwargs.get('output_format'):
            cmd.extend(["-f", kwargs['output_format']])
        
        if kwargs.get('threads'):
            cmd.extend(["-j", str(kwargs['threads'])])
        
        if kwargs.get('timeout'):
            cmd.extend(["--timeout", str(kwargs['timeout'])])
        
        return cmd
    
    def parse_output(self, stdout: str, stderr: str) -> Dict[str, Any]:
        """
        Parse tool output.
        
        Args:
            stdout: Standard output
            stderr: Standard error
            
        Returns:
            Parsed output dictionary
        """
        parsed = {
            'target': '',
            'findings': [],
            'scan_info': {}
        }
        
        lines = stdout.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse target information
            if line.startswith('Target:'):
                parsed['target'] = line.split(':', 1)[1].strip()
            
            # Parse findings
            elif line.startswith('FINDING:'):
                finding_text = line.split(':', 1)[1].strip()
                parsed['findings'].append({
                    'description': finding_text,
                    'severity': 'info'
                })
            
            # Parse scan information
            elif line.startswith('Scan completed in'):
                parsed['scan_info']['duration'] = line
        
        return parsed
    
    def validate_target(self, target: str) -> bool:
        """
        Validate target format.
        
        Args:
            target: Target to validate
            
        Returns:
            True if valid, False otherwise
        """
        # Basic validation - accept any non-empty string
        return bool(target and target.strip())
    
    def _extract_findings(self, parsed_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract findings from parsed data.
        
        Args:
            parsed_data: Parsed tool output
            
        Returns:
            List of findings
        """
        findings = []
        
        for finding_data in parsed_data.get('findings', []):
            finding = {
                'type': 'example_finding',
                'severity': self._assess_finding_severity(finding_data),
                'target': parsed_data.get('target', 'unknown'),
                'description': finding_data.get('description', 'No description'),
                'recommendation': self._get_finding_recommendation(finding_data)
            }
            findings.append(finding)
        
        return findings
    
    def _assess_finding_severity(self, finding_data: Dict[str, Any]) -> str:
        """Assess the severity of a finding."""
        description = finding_data.get('description', '').lower()
        
        if any(keyword in description for keyword in ['critical', 'exploit', 'rce']):
            return Severity.CRITICAL.value
        elif any(keyword in description for keyword in ['high', 'dangerous', 'vulnerable']):
            return Severity.HIGH.value
        elif any(keyword in description for keyword in ['medium', 'warning', 'issue']):
            return Severity.MEDIUM.value
        elif any(keyword in description for keyword in ['low', 'minor', 'notice']):
            return Severity.LOW.value
        else:
            return Severity.INFO.value
    
    def _get_finding_recommendation(self, finding_data: Dict[str, Any]) -> str:
        """Get recommendation for a finding."""
        description = finding_data.get('description', '').lower()
        
        if 'outdated' in description:
            return "Update the software to the latest version"
        elif 'configuration' in description:
            return "Review and secure the configuration"
        elif 'permission' in description:
            return "Review and restrict permissions"
        else:
            return "Review this finding and take appropriate action"
    
    def _generate_mock_output(self, target: str, **kwargs) -> Dict[str, Any]:
        """Generate mock output for testing/demo purposes."""
        mock_output = f"""Example Tool v1.0.0
Target: {target}
Starting scan...

FINDING: Example service detected on port 8080
FINDING: Configuration file found with default settings
FINDING: Outdated software version detected

Scan completed in 5.2 seconds
Found 3 issues
"""
        
        findings = [
            {
                'type': 'example_finding',
                'severity': 'medium',
                'target': target,
                'description': 'Example service detected on port 8080',
                'recommendation': 'Review service configuration and security'
            },
            {
                'type': 'example_finding',
                'severity': 'low',
                'target': target,
                'description': 'Configuration file found with default settings',
                'recommendation': 'Review and secure the configuration'
            },
            {
                'type': 'example_finding',
                'severity': 'medium',
                'target': target,
                'description': 'Outdated software version detected',
                'recommendation': 'Update the software to the latest version'
            }
        ]
        
        return {
            'stdout': mock_output,
            'stderr': '',
            'findings': findings
        }
