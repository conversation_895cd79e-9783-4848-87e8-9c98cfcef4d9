"""
Unit tests for ToolManager.
"""

import asyncio
import unittest
from unittest.mock import patch, Mock

from tool_connectors import ToolManager
from tool_connectors.base import ToolStatus


class TestToolManager(unittest.TestCase):
    """Test cases for ToolManager"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.manager = ToolManager(mock_mode=True)
    
    def test_initialization(self):
        """Test manager initialization"""
        self.assertTrue(self.manager.mock_mode)
        self.assertIsInstance(self.manager._connectors, dict)
        self.assertGreater(len(self.manager._connectors), 0)
    
    def test_get_available_tools(self):
        """Test getting available tools list"""
        tools = self.manager.get_available_tools()
        
        self.assertIsInstance(tools, list)
        self.assertIn('nmap', tools)
        self.assertIn('nikto', tools)
        self.assertIn('sqlmap', tools)
        self.assertIn('gobuster', tools)
    
    def test_get_connector(self):
        """Test getting specific connectors"""
        nmap_connector = self.manager.get_connector('nmap')
        self.assertIsNotNone(nmap_connector)
        self.assertEqual(nmap_connector.tool_name, 'nmap')
        
        # Test non-existent connector
        fake_connector = self.manager.get_connector('nonexistent')
        self.assertIsNone(fake_connector)
    
    def test_register_connector(self):
        """Test registering custom connector"""
        from tool_connectors.nmap_connector import NmapConnector
        
        custom_connector = NmapConnector(mock_mode=True)
        self.manager.register_connector('custom_nmap', custom_connector)
        
        # Check it was registered
        tools = self.manager.get_available_tools()
        self.assertIn('custom_nmap', tools)
        
        # Check we can retrieve it
        retrieved = self.manager.get_connector('custom_nmap')
        self.assertEqual(retrieved, custom_connector)
    
    def test_execute_tool(self):
        """Test executing a single tool"""
        result = self.manager.execute_tool('nmap', 'example.com', ports='80,443')
        
        self.assertEqual(result.tool_name, 'nmap')
        self.assertEqual(result.status, ToolStatus.COMPLETED)
        self.assertIsInstance(result.findings, list)
    
    def test_execute_tool_invalid(self):
        """Test executing non-existent tool"""
        with self.assertRaises(ValueError):
            self.manager.execute_tool('nonexistent', 'example.com')
    
    def test_execute_tool_async(self):
        """Test asynchronous tool execution"""
        async def run_test():
            result = await self.manager.execute_tool_async('nmap', 'example.com', ports='22,80')
            
            self.assertEqual(result.tool_name, 'nmap')
            self.assertEqual(result.status, ToolStatus.COMPLETED)
            return result
        
        result = asyncio.run(run_test())
        self.assertIsNotNone(result)
    
    def test_execute_multiple_tools(self):
        """Test executing multiple tools synchronously"""
        tools = ['nmap', 'nikto']
        results = self.manager.execute_multiple_tools(tools, 'example.com')
        
        self.assertEqual(len(results), 2)
        self.assertIn('nmap', results)
        self.assertIn('nikto', results)
        
        for tool_name, result in results.items():
            self.assertEqual(result.tool_name, tool_name)
            self.assertEqual(result.status, ToolStatus.COMPLETED)
    
    def test_execute_multiple_tools_async(self):
        """Test executing multiple tools asynchronously"""
        async def run_test():
            tools = ['nmap', 'nikto']
            tool_configs = {
                'nmap': {'ports': '22,80,443', 'timing': 4},
                'nikto': {'timeout': 15}
            }
            
            results = await self.manager.execute_multiple_tools_async(tools, 'example.com', tool_configs)
            
            self.assertEqual(len(results), 2)
            self.assertIn('nmap', results)
            self.assertIn('nikto', results)
            
            for tool_name, result in results.items():
                self.assertEqual(result.tool_name, tool_name)
                self.assertEqual(result.status, ToolStatus.COMPLETED)
            
            return results
        
        results = asyncio.run(run_test())
        self.assertIsNotNone(results)
    
    def test_execute_scan_workflow(self):
        """Test executing predefined workflows"""
        # Test quick workflow
        results = self.manager.execute_scan_workflow('example.com', workflow_type='quick')
        
        self.assertIn('nmap', results)
        self.assertIn('nikto', results)
        
        for tool_name, result in results.items():
            self.assertEqual(result.status, ToolStatus.COMPLETED)
        
        # Test comprehensive workflow
        results = self.manager.execute_scan_workflow('example.com', workflow_type='comprehensive')
        
        self.assertIn('nmap', results)
        self.assertIn('nikto', results)
        self.assertIn('gobuster', results)
        self.assertIn('sqlmap', results)
    
    def test_execute_scan_workflow_invalid(self):
        """Test executing invalid workflow"""
        with self.assertRaises(ValueError):
            self.manager.execute_scan_workflow('example.com', workflow_type='nonexistent')
    
    def test_aggregate_findings(self):
        """Test findings aggregation"""
        # Execute some tools first
        results = self.manager.execute_scan_workflow('example.com', workflow_type='quick')
        
        # Aggregate findings
        aggregated = self.manager.aggregate_findings(results)
        
        self.assertIn('total_findings', aggregated)
        self.assertIn('findings_by_severity', aggregated)
        self.assertIn('findings_by_tool', aggregated)
        self.assertIn('execution_summary', aggregated)
        
        # Check structure
        self.assertIsInstance(aggregated['total_findings'], int)
        self.assertIsInstance(aggregated['findings_by_severity'], dict)
        self.assertIsInstance(aggregated['findings_by_tool'], dict)
        self.assertIsInstance(aggregated['execution_summary'], dict)
        
        # Check severity categories
        severity_keys = ['critical', 'high', 'medium', 'low', 'info']
        for key in severity_keys:
            self.assertIn(key, aggregated['findings_by_severity'])
    
    def test_generate_report(self):
        """Test report generation"""
        # Execute some tools first
        results = self.manager.execute_scan_workflow('example.com', workflow_type='quick')
        
        # Test JSON report
        json_report = self.manager.generate_report(results, format='json')
        self.assertIsInstance(json_report, str)
        self.assertIn('total_findings', json_report)
        
        # Test text report
        text_report = self.manager.generate_report(results, format='text')
        self.assertIsInstance(text_report, str)
        self.assertIn('SECURITY SCAN REPORT', text_report)
        
        # Test HTML report
        html_report = self.manager.generate_report(results, format='html')
        self.assertIsInstance(html_report, str)
        self.assertIn('<html>', html_report)
    
    def test_generate_report_invalid_format(self):
        """Test report generation with invalid format"""
        # Create a proper mock result
        mock_result = Mock()
        mock_result.status.value = 'completed'
        mock_result.execution_time = 1.0
        mock_result.findings = []

        results = {'nmap': mock_result}

        with self.assertRaises(ValueError):
            self.manager.generate_report(results, format='invalid')
    
    def test_tool_status(self):
        """Test getting tool status"""
        status = self.manager.get_tool_status()
        
        self.assertIsInstance(status, dict)
        self.assertIn('nmap', status)
        
        for tool_name, info in status.items():
            self.assertIn('name', info)
            self.assertIn('available', info)
            self.assertIn('mock_mode', info)
            if tool_name != 'openvas':  # OpenVAS might have issues
                self.assertTrue(info['available'])
                self.assertTrue(info['mock_mode'])
    
    def test_available_tools_detailed(self):
        """Test getting detailed available tools info"""
        available = self.manager.get_available_tools_detailed()
        
        self.assertIsInstance(available, dict)
        
        for tool_name, info in available.items():
            self.assertIn('name', info)
            self.assertIn('available', info)
            self.assertIn('mock_mode', info)
            self.assertTrue(info['available'])
    
    def test_enable_disable_mock_mode(self):
        """Test enabling and disabling mock mode"""
        # Create manager without mock mode
        manager = ToolManager(mock_mode=False)
        self.assertFalse(manager.mock_mode)
        
        # Enable mock mode
        manager.enable_mock_mode()
        self.assertTrue(manager.mock_mode)
        
        # Check that connectors are in mock mode
        nmap_connector = manager.get_connector('nmap')
        if nmap_connector:
            self.assertTrue(nmap_connector.mock_mode)
        
        # Disable mock mode
        manager.disable_mock_mode()
        self.assertFalse(manager.mock_mode)
        
        # Check that connectors are not in mock mode
        if nmap_connector:
            self.assertFalse(nmap_connector.mock_mode)
    
    def test_workflow_configs(self):
        """Test workflow configuration generation"""
        configs = self.manager._get_workflow_configs('comprehensive', 'http://example.com')
        
        self.assertIn('nmap', configs)
        self.assertIn('nikto', configs)
        self.assertIn('gobuster', configs)
        self.assertIn('sqlmap', configs)
        
        # Check nmap config
        nmap_config = configs['nmap']
        self.assertIn('scan_type', nmap_config)
        self.assertIn('service_detection', nmap_config)
        
        # Check nikto config for HTTPS
        https_configs = self.manager._get_workflow_configs('web', 'https://example.com')
        nikto_config = https_configs['nikto']
        self.assertTrue(nikto_config['ssl'])


if __name__ == '__main__':
    unittest.main()
