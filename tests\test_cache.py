"""
Unit tests for caching and persistence system.
"""

import os
import tempfile
import time
import unittest
from pathlib import Path
from unittest.mock import Mock

from tool_connectors.cache import (
    MemoryCache, FileCache, SQLiteCache, CacheManager, get_cache_manager
)
from tool_connectors.base import <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolStatus
from tool_connectors import ToolManager


class TestMemoryCache(unittest.TestCase):
    """Test cases for MemoryCache"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.cache = MemoryCache()
    
    def test_basic_operations(self):
        """Test basic cache operations"""
        # Test set and get
        data = {'test': 'value', 'number': 42}
        self.assertTrue(self.cache.set('test_key', data))
        
        retrieved = self.cache.get('test_key')
        self.assertEqual(retrieved, data)
        
        # Test exists
        self.assertTrue(self.cache.exists('test_key'))
        self.assertFalse(self.cache.exists('nonexistent'))
        
        # Test delete
        self.assertTrue(self.cache.delete('test_key'))
        self.assertIsNone(self.cache.get('test_key'))
        self.assertFalse(self.cache.delete('nonexistent'))
    
    def test_ttl_expiration(self):
        """Test TTL expiration"""
        data = {'test': 'value'}
        
        # Set with 1 second TTL
        self.cache.set('ttl_key', data, ttl=1)
        
        # Should be available immediately
        self.assertEqual(self.cache.get('ttl_key'), data)
        
        # Wait for expiration
        time.sleep(1.1)
        
        # Should be expired
        self.assertIsNone(self.cache.get('ttl_key'))
    
    def test_keys_listing(self):
        """Test keys listing and pattern matching"""
        self.cache.set('key1', {'data': 1})
        self.cache.set('key2', {'data': 2})
        self.cache.set('other', {'data': 3})
        
        # Get all keys
        keys = self.cache.keys()
        self.assertEqual(set(keys), {'key1', 'key2', 'other'})
        
        # Pattern matching
        pattern_keys = self.cache.keys('key*')
        self.assertEqual(set(pattern_keys), {'key1', 'key2'})
    
    def test_clear(self):
        """Test cache clearing"""
        self.cache.set('key1', {'data': 1})
        self.cache.set('key2', {'data': 2})
        
        self.assertTrue(self.cache.clear())
        self.assertEqual(len(self.cache.keys()), 0)


class TestFileCache(unittest.TestCase):
    """Test cases for FileCache"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.cache = FileCache(self.temp_dir)
    
    def tearDown(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_basic_operations(self):
        """Test basic file cache operations"""
        data = {'test': 'value', 'list': [1, 2, 3]}
        
        # Test set and get
        self.assertTrue(self.cache.set('file_key', data))
        retrieved = self.cache.get('file_key')
        self.assertEqual(retrieved, data)
        
        # Check file was created
        cache_files = list(Path(self.temp_dir).glob('*.json'))
        self.assertEqual(len(cache_files), 1)
        
        # Test delete
        self.assertTrue(self.cache.delete('file_key'))
        self.assertIsNone(self.cache.get('file_key'))
        
        # Check file was removed
        cache_files = list(Path(self.temp_dir).glob('*.json'))
        self.assertEqual(len(cache_files), 0)
    
    def test_persistence(self):
        """Test that cache persists across instances"""
        data = {'persistent': 'data'}
        
        # Set data in first instance
        cache1 = FileCache(self.temp_dir)
        cache1.set('persist_key', data)
        
        # Create new instance and retrieve data
        cache2 = FileCache(self.temp_dir)
        retrieved = cache2.get('persist_key')
        self.assertEqual(retrieved, data)


class TestSQLiteCache(unittest.TestCase):
    """Test cases for SQLiteCache"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_db = tempfile.mktemp(suffix='.db')
        self.cache = SQLiteCache(self.temp_db)
    
    def tearDown(self):
        """Clean up test fixtures"""
        # Close cache to release database connections
        if hasattr(self, 'cache'):
            self.cache.close()

        # Try to remove database file
        if os.path.exists(self.temp_db):
            try:
                os.unlink(self.temp_db)
            except PermissionError:
                # On Windows, sometimes the file is still locked
                import time
                time.sleep(0.1)
                try:
                    os.unlink(self.temp_db)
                except PermissionError:
                    pass  # Ignore if we can't delete it
    
    def test_basic_operations(self):
        """Test basic SQLite cache operations"""
        data = {
            'tool_name': 'nmap',
            'target': 'example.com',
            'status': 'completed',
            'findings': [{'type': 'port', 'value': '80'}]
        }
        
        # Test set and get
        self.assertTrue(self.cache.set('sqlite_key', data))
        retrieved = self.cache.get('sqlite_key')
        self.assertEqual(retrieved, data)
        
        # Test exists
        self.assertTrue(self.cache.exists('sqlite_key'))
    
    def test_query_functionality(self):
        """Test SQLite query functionality"""
        # Add test data
        data1 = {'tool_name': 'nmap', 'target': 'example.com', 'status': 'completed'}
        data2 = {'tool_name': 'nikto', 'target': 'example.com', 'status': 'completed'}
        data3 = {'tool_name': 'nmap', 'target': 'test.com', 'status': 'failed'}
        
        self.cache.set('key1', data1)
        self.cache.set('key2', data2)
        self.cache.set('key3', data3)
        
        # Query by tool name
        nmap_results = self.cache.query(tool_name='nmap')
        self.assertEqual(len(nmap_results), 2)
        
        # Query by target
        example_results = self.cache.query(target='example.com')
        self.assertEqual(len(example_results), 2)
        
        # Query by status
        completed_results = self.cache.query(status='completed')
        self.assertEqual(len(completed_results), 2)
        
        # Query with limit
        limited_results = self.cache.query(limit=1)
        self.assertEqual(len(limited_results), 1)
    
    def test_cleanup_expired(self):
        """Test cleanup of expired entries"""
        data = {'test': 'data'}
        
        # Set with short TTL
        self.cache.set('expire_key', data, ttl=1)
        
        # Wait for expiration
        time.sleep(1.1)
        
        # Cleanup expired entries
        cleaned = self.cache.cleanup_expired()
        self.assertEqual(cleaned, 1)
        
        # Verify entry is gone
        self.assertIsNone(self.cache.get('expire_key'))


class TestCacheManager(unittest.TestCase):
    """Test cases for CacheManager"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.cache_manager = CacheManager(MemoryCache(), default_ttl=3600, enabled=True)
    
    def test_cache_key_generation(self):
        """Test cache key generation"""
        key1 = self.cache_manager.generate_cache_key('nmap', 'example.com', ports='80,443')
        key2 = self.cache_manager.generate_cache_key('nmap', 'example.com', ports='80,443')
        key3 = self.cache_manager.generate_cache_key('nmap', 'example.com', ports='22,80')
        
        # Same parameters should generate same key
        self.assertEqual(key1, key2)
        
        # Different parameters should generate different keys
        self.assertNotEqual(key1, key3)
        
        # Key should contain tool name and target
        self.assertIn('nmap', key1)
        self.assertIn('example.com', key1)
    
    def test_result_caching(self):
        """Test caching of tool results"""
        # Create mock result with all required attributes
        import time
        start_time = time.time()
        result = ToolResult(
            tool_name='nmap',
            command='nmap -sS example.com',
            status=ToolStatus.COMPLETED,
            findings=[{'type': 'port', 'value': '80'}],
            execution_time=1.0,
            timestamp=start_time,
            raw_output='Mock output',
            parsed_data={'target': 'example.com'}
        )
        
        # Cache result
        success = self.cache_manager.cache_result('nmap', 'example.com', result, ports='80')
        self.assertTrue(success)
        
        # Retrieve cached result
        cached = self.cache_manager.get_cached_result('nmap', 'example.com', ports='80')
        self.assertIsNotNone(cached)
        self.assertEqual(cached.tool_name, 'nmap')
        self.assertEqual(cached.status, ToolStatus.COMPLETED)
        self.assertEqual(len(cached.findings), 1)
    
    def test_cache_policies(self):
        """Test cache policies for different tools"""
        # Failed result that shouldn't be cached by default
        failed_result = ToolResult(
            tool_name='nmap',
            command='nmap example.com',
            status=ToolStatus.FAILED,
            error_message='Connection failed'
        )
        
        # Should not cache failed nmap result
        should_cache = self.cache_manager.should_cache_result('nmap', failed_result)
        self.assertFalse(should_cache)
        
        # SQLMap allows caching failed results
        should_cache_sqlmap = self.cache_manager.should_cache_result('sqlmap', failed_result)
        self.assertTrue(should_cache_sqlmap)
    
    def test_cache_invalidation(self):
        """Test cache invalidation"""
        # Cache some results with all required attributes
        import time
        start_time = time.time()
        result1 = ToolResult(
            tool_name='nmap', command='test', status=ToolStatus.COMPLETED,
            execution_time=1.0, timestamp=start_time, findings=[]
        )
        result2 = ToolResult(
            tool_name='nikto', command='test', status=ToolStatus.COMPLETED,
            execution_time=1.0, timestamp=start_time, findings=[]
        )
        
        self.cache_manager.cache_result('nmap', 'example.com', result1)
        self.cache_manager.cache_result('nikto', 'example.com', result2)
        self.cache_manager.cache_result('nmap', 'test.com', result1)
        
        # Invalidate nmap results
        invalidated = self.cache_manager.invalidate_cache(tool_name='nmap')
        self.assertEqual(invalidated, 2)
        
        # Verify nmap results are gone but nikto remains
        self.assertIsNone(self.cache_manager.get_cached_result('nmap', 'example.com'))
        self.assertIsNotNone(self.cache_manager.get_cached_result('nikto', 'example.com'))
    
    def test_cache_stats(self):
        """Test cache statistics"""
        # Cache some results with all required attributes
        import time
        start_time = time.time()
        result = ToolResult(
            tool_name='nmap', command='test', status=ToolStatus.COMPLETED,
            execution_time=1.0, timestamp=start_time, findings=[]
        )
        
        self.cache_manager.cache_result('nmap', 'example.com', result)
        self.cache_manager.cache_result('nikto', 'example.com', result)
        
        stats = self.cache_manager.get_cache_stats()
        
        self.assertEqual(stats['total_entries'], 2)
        self.assertEqual(stats['backend_type'], 'MemoryCache')
        self.assertTrue(stats['enabled'])
        self.assertIn('tool_breakdown', stats)
    
    def test_disabled_cache(self):
        """Test behavior when cache is disabled"""
        disabled_cache = CacheManager(enabled=False)
        
        result = ToolResult(tool_name='nmap', command='test', status=ToolStatus.COMPLETED)
        
        # Should not cache when disabled
        success = disabled_cache.cache_result('nmap', 'example.com', result)
        self.assertFalse(success)
        
        # Should not retrieve when disabled
        cached = disabled_cache.get_cached_result('nmap', 'example.com')
        self.assertIsNone(cached)


class TestToolManagerCacheIntegration(unittest.TestCase):
    """Test ToolManager integration with caching"""
    
    def test_cache_integration(self):
        """Test that ToolManager uses caching"""
        manager = ToolManager(mock_mode=True, cache_enabled=True, cache_backend='memory')
        
        # First execution should not be cached
        result1 = manager.execute_tool('nmap', 'example.com', timing=4)
        self.assertEqual(result1.status, ToolStatus.COMPLETED)
        
        # Second execution should use cache
        result2 = manager.execute_tool('nmap', 'example.com', timing=4)
        self.assertEqual(result2.status, ToolStatus.COMPLETED)
        
        # Results should be identical (from cache)
        self.assertEqual(result1.tool_name, result2.tool_name)
        self.assertEqual(result1.command, result2.command)
    
    def test_cache_management_methods(self):
        """Test cache management methods in ToolManager"""
        manager = ToolManager(mock_mode=True, cache_enabled=True)
        
        # Execute some tools to populate cache
        manager.execute_tool('nmap', 'example.com')
        manager.execute_tool('nikto', 'example.com')
        
        # Test cache stats
        stats = manager.get_cache_stats()
        self.assertGreater(stats['total_entries'], 0)
        
        # Test cache invalidation
        invalidated = manager.invalidate_cache(tool_name='nmap')
        self.assertGreater(invalidated, 0)
        
        # Test cache clearing
        success = manager.clear_cache()
        self.assertTrue(success)
        
        # Verify cache is empty
        stats = manager.get_cache_stats()
        self.assertEqual(stats['total_entries'], 0)
    
    def test_different_cache_backends(self):
        """Test different cache backends"""
        # Test memory cache
        manager_memory = ToolManager(mock_mode=True, cache_backend='memory')
        self.assertIsNotNone(manager_memory.cache_manager)
        
        # Test file cache
        manager_file = ToolManager(mock_mode=True, cache_backend='file')
        self.assertIsNotNone(manager_file.cache_manager)
        
        # Test SQLite cache
        manager_sqlite = ToolManager(mock_mode=True, cache_backend='sqlite')
        self.assertIsNotNone(manager_sqlite.cache_manager)


class TestGlobalCacheManager(unittest.TestCase):
    """Test global cache manager function"""
    
    def test_get_cache_manager(self):
        """Test global cache manager function"""
        manager1 = get_cache_manager()
        manager2 = get_cache_manager()
        
        # Should return same instance
        self.assertIs(manager1, manager2)


if __name__ == '__main__':
    unittest.main()
