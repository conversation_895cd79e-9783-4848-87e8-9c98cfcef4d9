"""
Unit tests for monitoring and logging system.
"""

import os
import tempfile
import time
import unittest
from unittest.mock import Mock, patch

from tool_connectors.monitoring import (
    StructuredLogger, MetricsCollector, HealthMonitor, PerformanceMonitor,
    MonitoringDashboard, MonitoringManager, HealthCheck, HealthStatus,
    get_monitoring_manager
)
from tool_connectors.base import <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolStatus
from tool_connectors import ToolManager


class TestStructuredLogger(unittest.TestCase):
    """Test cases for StructuredLogger"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_log = tempfile.mktemp(suffix='.log')
        self.logger = StructuredLogger("test_logger", "DEBUG", self.temp_log)
    
    def tearDown(self):
        """Clean up test fixtures"""
        # Close all handlers to release file locks
        if hasattr(self, 'logger'):
            for handler in self.logger.logger.handlers[:]:
                handler.close()
                self.logger.logger.removeHandler(handler)

        # Try to remove the log file
        if os.path.exists(self.temp_log):
            try:
                os.unlink(self.temp_log)
            except PermissionError:
                # On Windows, sometimes the file is still locked
                import time
                time.sleep(0.1)
                try:
                    os.unlink(self.temp_log)
                except PermissionError:
                    pass  # Ignore if we can't delete it
    
    def test_logger_creation(self):
        """Test logger creation"""
        self.assertIsNotNone(self.logger.logger)
        self.assertEqual(len(self.logger.logger.handlers), 2)  # Console + file
    
    def test_logging_methods(self):
        """Test different logging methods"""
        self.logger.debug("Debug message", extra_field="debug_value")
        self.logger.info("Info message", extra_field="info_value")
        self.logger.warning("Warning message", extra_field="warning_value")
        self.logger.error("Error message", extra_field="error_value")
        self.logger.critical("Critical message", extra_field="critical_value")
        
        # Check that log file was created and has content
        self.assertTrue(os.path.exists(self.temp_log))
        with open(self.temp_log, 'r') as f:
            content = f.read()
            self.assertIn("Debug message", content)
            self.assertIn("Info message", content)


class TestMetricsCollector(unittest.TestCase):
    """Test cases for MetricsCollector"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.metrics = MetricsCollector(max_metrics=100)
    
    def test_record_metric(self):
        """Test recording metrics"""
        self.metrics.record_metric("test_metric", 42.0, {"tag": "value"}, "count")
        
        metrics = self.metrics.get_metrics()
        self.assertEqual(len(metrics), 1)
        self.assertEqual(metrics[0].name, "test_metric")
        self.assertEqual(metrics[0].value, 42.0)
        self.assertEqual(metrics[0].tags, {"tag": "value"})
    
    def test_counter_operations(self):
        """Test counter operations"""
        self.metrics.increment_counter("requests", 1, {"endpoint": "/api"})
        self.metrics.increment_counter("requests", 2, {"endpoint": "/api"})
        
        # Should have 2 metric points (one for each increment)
        metrics = self.metrics.get_metrics("requests")
        self.assertEqual(len(metrics), 2)
        self.assertEqual(metrics[1].value, 3.0)  # Cumulative count
    
    def test_gauge_operations(self):
        """Test gauge operations"""
        self.metrics.set_gauge("temperature", 23.5, {"sensor": "room1"})
        self.metrics.set_gauge("temperature", 24.0, {"sensor": "room1"})
        
        metrics = self.metrics.get_metrics("temperature")
        self.assertEqual(len(metrics), 2)
        self.assertEqual(metrics[1].value, 24.0)  # Latest value
    
    def test_histogram_operations(self):
        """Test histogram operations"""
        values = [1.0, 2.0, 3.0, 4.0, 5.0]
        for value in values:
            self.metrics.record_histogram("response_time", value, {"service": "api"})
        
        metrics = self.metrics.get_metrics("response_time")
        self.assertEqual(len(metrics), 5)
    
    def test_metrics_filtering(self):
        """Test metrics filtering"""
        # Record different metrics
        self.metrics.record_metric("cpu_usage", 50.0)
        self.metrics.record_metric("memory_usage", 60.0)
        self.metrics.record_metric("cpu_temp", 70.0)
        
        # Filter by name
        cpu_metrics = self.metrics.get_metrics("cpu")
        self.assertEqual(len(cpu_metrics), 2)  # cpu_usage and cpu_temp
        
        # Filter by time
        time.sleep(0.1)
        recent_time = time.time()
        self.metrics.record_metric("new_metric", 80.0)
        
        recent_metrics = self.metrics.get_metrics(since=recent_time)
        self.assertEqual(len(recent_metrics), 1)
        self.assertEqual(recent_metrics[0].name, "new_metric")
    
    def test_metrics_summary(self):
        """Test metrics summary"""
        self.metrics.increment_counter("requests", 5)
        self.metrics.set_gauge("active_connections", 10)
        self.metrics.record_histogram("latency", 100.0)
        
        summary = self.metrics.get_summary()
        
        self.assertGreater(summary['total_metrics'], 0)
        self.assertIn('counters', summary)
        self.assertIn('gauges', summary)
        self.assertIn('histogram_counts', summary)


class TestHealthMonitor(unittest.TestCase):
    """Test cases for HealthMonitor"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.health = HealthMonitor()
    
    def test_add_remove_checks(self):
        """Test adding and removing health checks"""
        def always_pass():
            return True
        
        check = HealthCheck("test_check", always_pass, "Test check")
        self.health.add_check(check)
        
        self.assertIn("test_check", self.health.checks)
        
        self.health.remove_check("test_check")
        self.assertNotIn("test_check", self.health.checks)
    
    def test_run_single_check(self):
        """Test running a single health check"""
        def always_pass():
            return True
        
        check = HealthCheck("pass_check", always_pass, "Always passes")
        self.health.add_check(check)
        
        result = self.health.run_check("pass_check")
        
        self.assertEqual(result['name'], "pass_check")
        self.assertEqual(result['status'], HealthStatus.HEALTHY.value)
        self.assertIn('execution_time', result)
    
    def test_failing_check(self):
        """Test failing health check"""
        def always_fail():
            return False
        
        check = HealthCheck("fail_check", always_fail, "Always fails")
        self.health.add_check(check)
        
        result = self.health.run_check("fail_check")
        
        self.assertEqual(result['status'], HealthStatus.UNHEALTHY.value)
        self.assertEqual(result['message'], "Check failed")
    
    def test_check_with_exception(self):
        """Test health check that raises exception"""
        def error_check():
            raise ValueError("Test error")
        
        check = HealthCheck("error_check", error_check, "Raises error")
        self.health.add_check(check)
        
        result = self.health.run_check("error_check")
        
        self.assertEqual(result['status'], HealthStatus.UNHEALTHY.value)
        self.assertIn("Test error", result['message'])
    
    def test_run_all_checks(self):
        """Test running all health checks"""
        def pass_check():
            return True
        
        def fail_check():
            return False
        
        self.health.add_check(HealthCheck("pass", pass_check, "Passes", critical=True))
        self.health.add_check(HealthCheck("fail", fail_check, "Fails", critical=False))
        
        results = self.health.run_all_checks()
        
        self.assertEqual(results['overall_status'], HealthStatus.DEGRADED.value)
        self.assertIn('pass', results['checks'])
        self.assertIn('fail', results['checks'])
    
    def test_nonexistent_check(self):
        """Test running non-existent check"""
        result = self.health.run_check("nonexistent")
        
        self.assertEqual(result['status'], HealthStatus.UNHEALTHY.value)
        self.assertIn("not found", result['message'])


class TestPerformanceMonitor(unittest.TestCase):
    """Test cases for PerformanceMonitor"""
    
    def setUp(self):
        """Set up test fixtures"""
        from tool_connectors.monitoring import MetricsCollector
        self.metrics = MetricsCollector()
        self.performance = PerformanceMonitor(self.metrics)
    
    def test_execution_monitoring(self):
        """Test monitoring tool execution"""
        # Start monitoring
        context = self.performance.start_execution("nmap", "example.com")
        
        self.assertIn('tool_name', context)
        self.assertIn('target', context)
        self.assertIn('start_time', context)
        
        # Simulate execution
        time.sleep(0.1)
        
        # Create mock result
        result = ToolResult(
            tool_name="nmap",
            command="nmap example.com",
            status=ToolStatus.COMPLETED,
            findings=[{"port": 80}]
        )
        
        # End monitoring
        perf_metrics = self.performance.end_execution(context, result)
        
        self.assertEqual(perf_metrics.tool_name, "nmap")
        self.assertEqual(perf_metrics.target, "example.com")
        self.assertGreater(perf_metrics.execution_time, 0)
        self.assertEqual(perf_metrics.findings_count, 1)
    
    def test_performance_summary(self):
        """Test performance summary generation"""
        # Add some execution history
        context = self.performance.start_execution("nmap", "test.com")
        time.sleep(0.05)
        
        result = ToolResult(
            tool_name="nmap",
            command="nmap test.com",
            status=ToolStatus.COMPLETED,
            findings=[{"port": 80}, {"port": 443}]
        )
        
        self.performance.end_execution(context, result)
        
        # Get summary
        summary = self.performance.get_performance_summary()
        
        self.assertIn('total_executions', summary)
        self.assertIn('execution_time', summary)
        self.assertIn('status_distribution', summary)
        self.assertEqual(summary['total_executions'], 1)


class TestMonitoringManager(unittest.TestCase):
    """Test cases for MonitoringManager"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.monitoring = MonitoringManager("DEBUG")
    
    def test_manager_initialization(self):
        """Test monitoring manager initialization"""
        self.assertIsNotNone(self.monitoring.logger)
        self.assertIsNotNone(self.monitoring.metrics)
        self.assertIsNotNone(self.monitoring.health)
        self.assertIsNotNone(self.monitoring.performance)
        self.assertIsNotNone(self.monitoring.dashboard)
    
    def test_tool_execution_monitoring(self):
        """Test monitoring tool execution through manager"""
        # Start monitoring
        context = self.monitoring.start_tool_execution("nmap", "example.com")
        
        # Simulate execution
        time.sleep(0.05)
        
        result = ToolResult(
            tool_name="nmap",
            command="nmap example.com",
            status=ToolStatus.COMPLETED,
            findings=[{"port": 80}]
        )
        
        # End monitoring
        perf_metrics = self.monitoring.end_tool_execution(context, result)
        
        self.assertIsNotNone(perf_metrics)
        self.assertEqual(perf_metrics.tool_name, "nmap")
    
    def test_monitoring_summary(self):
        """Test comprehensive monitoring summary"""
        summary = self.monitoring.get_monitoring_summary()
        
        self.assertIn('health', summary)
        self.assertIn('metrics', summary)
        self.assertIn('performance', summary)


class TestToolManagerMonitoringIntegration(unittest.TestCase):
    """Test ToolManager integration with monitoring"""
    
    def test_monitoring_integration(self):
        """Test that ToolManager integrates with monitoring"""
        manager = ToolManager(mock_mode=True, monitoring_enabled=True)
        
        self.assertIsNotNone(manager.monitoring)
        
        # Execute a tool to generate monitoring data
        result = manager.execute_tool('nmap', 'example.com')
        self.assertEqual(result.status, ToolStatus.COMPLETED)
        
        # Check monitoring data was collected
        summary = manager.get_monitoring_summary()
        self.assertIn('performance', summary)
    
    def test_monitoring_disabled(self):
        """Test behavior when monitoring is disabled"""
        manager = ToolManager(mock_mode=True, monitoring_enabled=False)
        
        self.assertIsNone(manager.monitoring)
        
        # Should still work without monitoring
        result = manager.execute_tool('nmap', 'example.com')
        self.assertEqual(result.status, ToolStatus.COMPLETED)
    
    def test_monitoring_methods(self):
        """Test monitoring management methods"""
        manager = ToolManager(mock_mode=True, monitoring_enabled=True)
        
        # Test dashboard generation
        dashboard = manager.get_monitoring_dashboard()
        self.assertIn("MONITORING DASHBOARD", dashboard)
        
        # Test health status
        health = manager.get_health_status()
        self.assertIn('overall_status', health)
        
        # Test performance metrics
        perf = manager.get_performance_metrics()
        self.assertIsInstance(perf, dict)
    
    def test_custom_health_checks(self):
        """Test adding custom health checks"""
        manager = ToolManager(mock_mode=True, monitoring_enabled=True)
        
        def custom_check():
            return True
        
        manager.add_health_check("custom_test", custom_check, "Custom test check")
        
        health = manager.get_health_status()
        self.assertIn("custom_test", health['checks'])
        
        manager.remove_health_check("custom_test")
        health = manager.get_health_status()
        self.assertNotIn("custom_test", health['checks'])


class TestGlobalMonitoringManager(unittest.TestCase):
    """Test global monitoring manager function"""
    
    def test_get_monitoring_manager(self):
        """Test global monitoring manager function"""
        manager1 = get_monitoring_manager()
        manager2 = get_monitoring_manager()
        
        # Should return same instance
        self.assertIs(manager1, manager2)


if __name__ == '__main__':
    unittest.main()
