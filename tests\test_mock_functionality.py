"""
Unit tests for mock functionality in tool connectors.
"""

import asyncio
import unittest
from unittest.mock import patch
import time

from tool_connectors import <PERSON><PERSON><PERSON><PERSON><PERSON>, NmapConnector
from tool_connectors.base import ToolStatus


class TestMockFunctionality(unittest.TestCase):
    """Test cases for mock functionality"""
    
    def test_nmap_mock_mode_initialization(self):
        """Test Nmap connector initialization in mock mode"""
        nmap = NmapConnector(mock_mode=True)
        
        self.assertTrue(nmap.mock_mode)
        self.assertTrue(nmap.is_available())
        self.assertEqual(nmap.tool_name, "nmap")
    
    def test_nmap_mock_execution(self):
        """Test Nmap mock execution"""
        nmap = NmapConnector(mock_mode=True)
        
        result = nmap.execute("example.com", ports="80,443", timing=4)
        
        self.assertEqual(result.status, ToolStatus.COMPLETED)
        self.assertEqual(result.tool_name, "nmap")
        self.assertGreater(result.execution_time, 0)
        self.assertIsInstance(result.findings, list)
        self.assertIn("example.com", result.command)
    
    def test_nmap_mock_output_parsing(self):
        """Test that mock output is properly parsed"""
        nmap = NmapConnector(mock_mode=True)
        
        result = nmap.execute("testhost.com", ports="22,80,443")
        
        # Should have findings for open ports
        self.assertGreater(len(result.findings), 0)
        
        # Check finding structure
        for finding in result.findings:
            self.assertIn('type', finding)
            self.assertIn('severity', finding)
            self.assertIn('host', finding)
            self.assertIn('port', finding)
            self.assertIn('description', finding)
    
    def test_tool_manager_mock_mode(self):
        """Test ToolManager in mock mode"""
        manager = ToolManager(mock_mode=True)
        
        # All tools should be available in mock mode
        available_tools = manager.get_available_tools()
        self.assertIn('nmap', available_tools)
        self.assertIn('nikto', available_tools)
        
        # Check tool status
        status = manager.get_tool_status()
        for tool_name, info in status.items():
            if tool_name != 'openvas':  # OpenVAS might have issues
                self.assertTrue(info['available'])
                self.assertTrue(info['mock_mode'])
    
    def test_tool_manager_mock_execution(self):
        """Test tool execution through manager in mock mode"""
        manager = ToolManager(mock_mode=True)
        
        result = manager.execute_tool('nmap', 'mocktest.com', ports='80,443')
        
        self.assertEqual(result.status, ToolStatus.COMPLETED)
        self.assertEqual(result.tool_name, 'nmap')
        self.assertIsInstance(result.findings, list)
    
    def test_async_mock_execution(self):
        """Test asynchronous mock execution"""
        async def run_test():
            manager = ToolManager(mock_mode=True)
            
            result = await manager.execute_tool_async('nmap', 'asynctest.com', ports='22,80')
            
            self.assertEqual(result.status, ToolStatus.COMPLETED)
            self.assertEqual(result.tool_name, 'nmap')
            return result
        
        result = asyncio.run(run_test())
        self.assertIsNotNone(result)
    
    def test_multiple_tools_mock_execution(self):
        """Test multiple tools execution in mock mode"""
        async def run_test():
            manager = ToolManager(mock_mode=True)
            
            tools = ['nmap', 'nikto']
            results = await manager.execute_multiple_tools_async(tools, 'multitest.com')
            
            self.assertEqual(len(results), 2)
            self.assertIn('nmap', results)
            self.assertIn('nikto', results)
            
            for tool_name, result in results.items():
                self.assertEqual(result.status, ToolStatus.COMPLETED)
                self.assertEqual(result.tool_name, tool_name)
            
            return results
        
        results = asyncio.run(run_test())
        self.assertIsNotNone(results)
    
    def test_mock_workflow_execution(self):
        """Test workflow execution in mock mode"""
        manager = ToolManager(mock_mode=True)
        
        results = manager.execute_scan_workflow('workflowtest.com', workflow_type='quick')
        
        # Quick workflow should include nmap and nikto
        self.assertIn('nmap', results)
        self.assertIn('nikto', results)
        
        for tool_name, result in results.items():
            self.assertEqual(result.status, ToolStatus.COMPLETED)
    
    def test_mock_findings_aggregation(self):
        """Test findings aggregation with mock data"""
        manager = ToolManager(mock_mode=True)
        
        results = manager.execute_scan_workflow('aggregatetest.com', workflow_type='quick')
        aggregated = manager.aggregate_findings(results)
        
        self.assertIn('total_findings', aggregated)
        self.assertIn('findings_by_severity', aggregated)
        self.assertIn('findings_by_tool', aggregated)
        self.assertIn('execution_summary', aggregated)
        
        # Check structure
        self.assertIsInstance(aggregated['total_findings'], int)
        self.assertIsInstance(aggregated['findings_by_severity'], dict)
        self.assertIsInstance(aggregated['findings_by_tool'], dict)
        self.assertIsInstance(aggregated['execution_summary'], dict)
    
    def test_enable_disable_mock_mode(self):
        """Test enabling and disabling mock mode"""
        nmap = NmapConnector(mock_mode=False)
        
        # Initially not in mock mode
        self.assertFalse(nmap.mock_mode)
        
        # Enable mock mode
        nmap.enable_mock_mode()
        self.assertTrue(nmap.mock_mode)
        self.assertTrue(nmap.is_available())
        
        # Disable mock mode
        nmap.disable_mock_mode()
        self.assertFalse(nmap.mock_mode)
    
    def test_tool_info_in_mock_mode(self):
        """Test tool info retrieval in mock mode"""
        nmap = NmapConnector(mock_mode=True)
        
        info = nmap.get_tool_info()
        
        self.assertEqual(info['name'], 'nmap')
        self.assertTrue(info['available'])
        self.assertTrue(info['mock_mode'])
        self.assertEqual(info['default_command'], 'nmap')
        self.assertIsInstance(info['timeout'], int)
    
    def test_mock_delay_parameter(self):
        """Test mock delay parameter"""
        nmap = NmapConnector(mock_mode=True)
        
        start_time = time.time()
        result = nmap.execute("delaytest.com", mock_delay=0.5)
        end_time = time.time()
        
        # Should take at least the mock delay time
        self.assertGreaterEqual(end_time - start_time, 0.5)
        self.assertEqual(result.status, ToolStatus.COMPLETED)
    
    def test_mixed_mode_operation(self):
        """Test mixed mode operation (some tools mock, others real)"""
        manager = ToolManager(mock_mode=False)
        
        # Enable mock mode for specific tool
        nmap_connector = manager.get_connector('nmap')
        if nmap_connector:
            nmap_connector.enable_mock_mode()
            
            # This tool should now be available in mock mode
            self.assertTrue(nmap_connector.is_available())
            self.assertTrue(nmap_connector.mock_mode)
            
            # Execute should work
            result = manager.execute_tool('nmap', 'mixedtest.com')
            self.assertEqual(result.status, ToolStatus.COMPLETED)


class TestMockDataGeneration(unittest.TestCase):
    """Test cases for mock data generation"""
    
    def test_nmap_mock_data_structure(self):
        """Test Nmap mock data structure"""
        nmap = NmapConnector(mock_mode=True)
        
        mock_output = nmap._generate_mock_output("testhost.com", ports="22,80,443")
        
        self.assertIn('stdout', mock_output)
        self.assertIn('stderr', mock_output)
        self.assertIn('findings', mock_output)
        
        # Check XML structure
        self.assertIn('<?xml', mock_output['stdout'])
        self.assertIn('nmaprun', mock_output['stdout'])
        self.assertIn('testhost.com', mock_output['stdout'])
    
    def test_mock_service_mapping(self):
        """Test mock service name mapping"""
        nmap = NmapConnector(mock_mode=True)
        
        # Test known port mappings
        self.assertEqual(nmap._get_mock_service_name(22), 'ssh')
        self.assertEqual(nmap._get_mock_service_name(80), 'http')
        self.assertEqual(nmap._get_mock_service_name(443), 'https')
        self.assertEqual(nmap._get_mock_service_name(9999), 'unknown')


if __name__ == '__main__':
    unittest.main()
